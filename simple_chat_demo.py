"""
Simple Chat History and Presets Demo

This demonstrates the chat history and preset functionality without heavy dependencies.
"""

import asyncio
import logging
import json
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def demo_chat_and_presets():
    """Simple demo of chat history and presets"""
    
    logger.info("🗨️  SmartClips GPT Editor - Chat History & Presets")
    logger.info("=" * 60)
    
    # Import only the chat service
    import sys
    sys.path.append('backend')
    from chat_history_service import chat_service
    
    # Demo user
    demo_user = "demo_user_2025"
    demo_session = f"session_{int(datetime.now().timestamp())}"
    
    logger.info(f"👤 Demo User: {demo_user}")
    logger.info(f"🔗 Session: {demo_session}")
    
    # Test prompts and their expected results
    test_scenarios = [
        {
            "prompt": "Make this video TikTok ready with viral subtitles and emojis",
            "commands": [
                {"action": "crop", "parameters": {"aspect_ratio": "9:16"}, "confidence": 0.95, "description": "Crop to vertical format"},
                {"action": "add_subtitles", "parameters": {"style": "viral_style"}, "confidence": 0.90, "description": "Add viral subtitles"},
                {"action": "add_emojis", "parameters": {"enabled": True}, "confidence": 0.85, "description": "Add emoji overlays"}
            ],
            "template": "tiktok"
        },
        {
            "prompt": "Apply podcast template with professional styling and enhance audio",
            "commands": [
                {"action": "apply_template", "parameters": {"template": "podcast"}, "confidence": 0.92, "description": "Apply podcast template"},
                {"action": "enhance_audio", "parameters": {"normalize": True}, "confidence": 0.88, "description": "Enhance audio quality"},
                {"action": "add_subtitles", "parameters": {"style": "clean_modern"}, "confidence": 0.85, "description": "Add professional subtitles"}
            ],
            "template": "podcast"
        },
        {
            "prompt": "Create gaming highlights: boost colors, add epic text, and optimize for YouTube",
            "commands": [
                {"action": "add_effects", "parameters": {"saturation": 1.3, "contrast": 1.2}, "confidence": 0.87, "description": "Boost colors"},
                {"action": "add_text", "parameters": {"text": "EPIC MOMENT", "position": "top"}, "confidence": 0.82, "description": "Add epic text"},
                {"action": "apply_template", "parameters": {"template": "gaming"}, "confidence": 0.90, "description": "Apply gaming template"}
            ],
            "template": "gaming"
        }
    ]
    
    created_jobs = []
    
    # Process each scenario
    for i, scenario in enumerate(test_scenarios, 1):
        logger.info(f"\n🎯 Scenario {i}: Processing User Request")
        logger.info(f"Prompt: '{scenario['prompt']}'")
        logger.info("-" * 50)
        
        try:
            # Step 1: Save user message
            user_msg_id = chat_service.save_user_message(
                user_id=demo_user,
                session_id=demo_session,
                prompt=scenario["prompt"],
                video_info={"duration": 180, "width": 1920, "height": 1080}
            )
            logger.info(f"💬 User message saved: {user_msg_id}")
            
            # Step 2: Save assistant response
            job_id = f"demo_job_{i}_{int(datetime.now().timestamp())}"
            avg_confidence = sum(cmd['confidence'] for cmd in scenario['commands']) / len(scenario['commands'])
            
            assistant_msg_id = chat_service.save_assistant_response(
                user_id=demo_user,
                session_id=demo_session,
                parsed_commands=scenario['commands'],
                job_id=job_id,
                confidence_score=avg_confidence,
                template_used=scenario['template']
            )
            logger.info(f"🤖 Assistant response saved: {assistant_msg_id}")
            logger.info(f"📊 Parsed {len(scenario['commands'])} commands (avg confidence: {avg_confidence:.2f})")
            
            # Step 3: Simulate job completion
            result_url = f"https://res.cloudinary.com/demo/video/upload/result_{job_id}.mp4"
            processing_time = 25.0 + (i * 5)  # Simulate different processing times
            
            chat_service.update_processing_status(
                job_id=job_id,
                status="completed",
                result_url=result_url,
                processing_time=processing_time
            )
            logger.info(f"✅ Job completed: {result_url}")
            logger.info(f"⏱️  Processing time: {processing_time}s")
            
            created_jobs.append({
                "job_id": job_id,
                "prompt": scenario["prompt"],
                "commands": scenario["commands"],
                "result_url": result_url
            })
            
        except Exception as e:
            logger.error(f"❌ Error in scenario {i}: {e}")
    
    # Demonstrate chat history retrieval
    logger.info(f"\n📜 CHAT HISTORY DEMONSTRATION")
    logger.info("=" * 35)
    
    try:
        chat_history = chat_service.get_chat_history(
            user_id=demo_user,
            session_id=demo_session,
            limit=20
        )
        
        logger.info(f"📊 Retrieved {len(chat_history)} messages:")
        
        for msg in chat_history:
            msg_type = msg['message_type']
            timestamp = msg['timestamp'][:19]
            content_preview = msg['content'][:50] + "..." if len(msg['content']) > 50 else msg['content']
            
            type_emoji = "👤" if msg_type == "user" else "🤖"
            logger.info(f"   {type_emoji} [{timestamp}] {content_preview}")
            
            if msg.get('job_id'):
                status = msg.get('processing_status', 'unknown')
                status_emoji = "✅" if status == "completed" else "⏳" if status == "processing" else "❌"
                logger.info(f"      {status_emoji} Job: {msg['job_id']} ({status})")
                
                if msg.get('result_url'):
                    logger.info(f"      🎬 Result: {msg['result_url']}")
    
    except Exception as e:
        logger.error(f"❌ Error retrieving chat history: {e}")
    
    # Demonstrate preset functionality
    logger.info(f"\n🎨 PRESET SYSTEM DEMONSTRATION")
    logger.info("=" * 35)
    
    try:
        # Get user's presets (should include auto-created ones)
        user_presets = chat_service.get_presets(user_id=demo_user, limit=10)
        
        logger.info(f"📊 Found {len(user_presets)} presets for user:")
        
        for preset in user_presets:
            logger.info(f"   🎨 '{preset['name']}'")
            logger.info(f"      Category: {preset['category']}")
            logger.info(f"      Commands: {len(preset['commands'])}")
            logger.info(f"      Confidence: {preset['avg_confidence']:.2f}")
            logger.info(f"      Used: {preset['usage_count']} times")
            logger.info(f"      Success Rate: {preset['success_rate']:.1%}")
            
            # Show command details
            for j, cmd in enumerate(preset['commands'][:2], 1):  # Show first 2 commands
                logger.info(f"         {j}. {cmd['action']}: {cmd['description']}")
        
        # Test manual preset creation
        if created_jobs:
            logger.info(f"\n🛠️  Creating Manual Preset...")
            
            best_job = max(created_jobs, key=lambda x: sum(cmd['confidence'] for cmd in x['commands']))
            
            manual_preset_id = chat_service.create_preset_from_successful_job(
                job_id=best_job['job_id'],
                preset_name="Custom Viral Video Template",
                description="Manually created template for viral video content",
                is_public=True
            )
            
            if manual_preset_id:
                logger.info(f"✅ Manual preset created: {manual_preset_id}")
                logger.info(f"   Based on: '{best_job['prompt'][:40]}...'")
            else:
                logger.info(f"ℹ️  Preset might already exist or creation failed")
        
        # Test preset usage
        if user_presets:
            logger.info(f"\n🔄 Testing Preset Usage...")
            
            test_preset = user_presets[0]
            logger.info(f"🎨 Using preset: '{test_preset['name']}'")
            
            preset_data = chat_service.use_preset(test_preset['id'], demo_user)
            
            if preset_data:
                logger.info(f"✅ Preset used successfully!")
                logger.info(f"   Original prompt: {preset_data['original_prompt'][:50]}...")
                logger.info(f"   Commands: {len(preset_data['commands'])}")
                logger.info(f"   Category: {preset_data['category']}")
                logger.info(f"   Usage count increased to: {test_preset['usage_count'] + 1}")
            else:
                logger.info(f"❌ Preset usage failed")
    
    except Exception as e:
        logger.error(f"❌ Error with presets: {e}")
    
    # Show public presets
    logger.info(f"\n🌐 PUBLIC PRESETS SHOWCASE")
    logger.info("-" * 30)
    
    try:
        public_presets = chat_service.get_presets(is_public=True, limit=5)
        
        logger.info(f"📊 Available public presets: {len(public_presets)}")
        
        for preset in public_presets:
            logger.info(f"   🌟 '{preset['name']}'")
            logger.info(f"      Category: {preset['category']}")
            logger.info(f"      Success Rate: {preset['success_rate']:.1%}")
            logger.info(f"      Community Usage: {preset['usage_count']} times")
    
    except Exception as e:
        logger.error(f"❌ Error getting public presets: {e}")
    
    # Final statistics
    logger.info(f"\n📊 FINAL STATISTICS")
    logger.info("=" * 20)
    
    try:
        all_chat = chat_service.get_chat_history(user_id=demo_user, limit=100)
        all_presets = chat_service.get_presets(user_id=demo_user, limit=50)
        
        user_messages = [msg for msg in all_chat if msg['message_type'] == 'user']
        assistant_messages = [msg for msg in all_chat if msg['message_type'] == 'assistant']
        successful_jobs = [msg for msg in assistant_messages if msg.get('success')]
        
        logger.info(f"💬 Total Messages: {len(all_chat)}")
        logger.info(f"👤 User Messages: {len(user_messages)}")
        logger.info(f"🤖 Assistant Responses: {len(assistant_messages)}")
        logger.info(f"✅ Successful Jobs: {len(successful_jobs)}")
        logger.info(f"🎨 Created Presets: {len(all_presets)}")
        
        if successful_jobs:
            avg_processing_time = sum(msg.get('processing_time', 0) for msg in successful_jobs) / len(successful_jobs)
            logger.info(f"⚡ Avg Processing Time: {avg_processing_time:.1f}s")
        
        if all_presets:
            total_usage = sum(p['usage_count'] for p in all_presets)
            avg_confidence = sum(p['avg_confidence'] for p in all_presets) / len(all_presets)
            logger.info(f"🔄 Total Preset Usage: {total_usage}")
            logger.info(f"🎯 Avg Preset Confidence: {avg_confidence:.2f}")
        
        success_rate = len(successful_jobs) / len(assistant_messages) * 100 if assistant_messages else 0
        logger.info(f"📈 Success Rate: {success_rate:.1f}%")
    
    except Exception as e:
        logger.error(f"❌ Error getting statistics: {e}")
    
    # API Usage Examples
    logger.info(f"\n🌐 API USAGE EXAMPLES")
    logger.info("=" * 25)
    
    api_examples = [
        {
            "name": "Get Chat History",
            "method": "GET",
            "endpoint": "/api/gpt-editor/chat-history?session_id=session_123&limit=20",
            "description": "Retrieve conversation history for a session"
        },
        {
            "name": "Create Preset",
            "method": "POST", 
            "endpoint": "/api/gpt-editor/create-preset",
            "body": {"job_id": "job_456", "name": "My Template", "is_public": False},
            "description": "Create a reusable preset from successful job"
        },
        {
            "name": "Use Preset",
            "method": "POST",
            "endpoint": "/api/gpt-editor/use-preset/preset_789",
            "description": "Apply existing preset to create new job"
        },
        {
            "name": "Browse Presets",
            "method": "GET",
            "endpoint": "/api/gpt-editor/presets?category=tiktok&is_public=true",
            "description": "Find presets by category and visibility"
        }
    ]
    
    for example in api_examples:
        logger.info(f"   📡 {example['name']}")
        logger.info(f"      {example['method']} {example['endpoint']}")
        if 'body' in example:
            logger.info(f"      Body: {json.dumps(example['body'])}")
        logger.info(f"      {example['description']}")
    
    logger.info(f"\n" + "=" * 60)
    logger.info("🎉 CHAT HISTORY & PRESETS DEMO COMPLETE!")
    logger.info("=" * 60)
    
    logger.info(f"✅ Successfully demonstrated:")
    logger.info(f"   📝 Automatic chat history saving for all interactions")
    logger.info(f"   🎨 Intelligent preset creation from successful workflows")
    logger.info(f"   🔄 One-click preset reuse for common editing patterns")
    logger.info(f"   📊 Usage analytics and performance tracking")
    logger.info(f"   🌐 Public preset sharing and community templates")
    logger.info(f"   🏷️  Category-based preset organization and discovery")
    logger.info(f"   🌐 Complete REST API for frontend integration")
    
    logger.info(f"\n🚀 Benefits for Users:")
    logger.info(f"   • Never lose track of editing conversations")
    logger.info(f"   • Automatically save successful editing workflows")
    logger.info(f"   • Reuse proven templates with one click")
    logger.info(f"   • Discover community-created presets")
    logger.info(f"   • Track editing performance and success rates")

async def main():
    """Main demo function"""
    await demo_chat_and_presets()

if __name__ == "__main__":
    asyncio.run(main())
