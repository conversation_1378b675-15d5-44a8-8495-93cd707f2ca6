"""
Test script to identify import issues in main.py
"""

import sys
import traceback

def test_import(module_name, description=""):
    try:
        if module_name == "main":
            from main import app
            print(f"✅ {module_name} imported successfully {description}")
            return True
        else:
            __import__(module_name)
            print(f"✅ {module_name} imported successfully {description}")
            return True
    except Exception as e:
        print(f"❌ Failed to import {module_name}: {e}")
        traceback.print_exc()
        return False

def main():
    print("Testing imports for SmartClips backend...")
    
    # Test basic imports first
    basic_imports = [
        ("os", ""),
        ("sys", ""),
        ("json", ""),
        ("time", ""),
        ("logging", ""),
        ("tempfile", ""),
        ("subprocess", ""),
        ("pathlib", ""),
    ]
    
    print("\n🔍 Testing basic Python imports:")
    for module, desc in basic_imports:
        test_import(module, desc)
    
    # Test third-party imports
    third_party_imports = [
        ("fastapi", ""),
        ("uvicorn", ""),
        ("sqlalchemy", ""),
        ("pydantic", ""),
        ("requests", ""),
        ("openai", ""),
        ("cloudinary", ""),
    ]
    
    print("\n🔍 Testing third-party imports:")
    for module, desc in third_party_imports:
        test_import(module, desc)
    
    # Test local imports
    local_imports = [
        ("models", ""),
        ("database", ""),
        ("storage", ""),
        ("video_processing", ""),
        ("url_processor", ""),
    ]
    
    print("\n🔍 Testing local imports:")
    for module, desc in local_imports:
        test_import(module, desc)
    
    # Test environment variables
    print("\n🔍 Testing environment variables:")
    import os
    env_vars = [
        "SECRET_KEY",
        "OPENAI_API_KEY", 
        "CLOUDINARY_CLOUD_NAME",
        "CLOUDINARY_API_KEY",
        "CLOUDINARY_API_SECRET",
        "ELEVENLABS_API_KEY"
    ]
    
    for var in env_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: Set (length: {len(value)})")
        else:
            print(f"⚠️  {var}: Not set")
    
    # Finally test main import
    print("\n🔍 Testing main.py import:")
    success = test_import("main", "")
    
    if success:
        print("\n🎉 All imports successful! Backend should start properly.")
    else:
        print("\n❌ Main import failed. Check the errors above.")

if __name__ == "__main__":
    main()
