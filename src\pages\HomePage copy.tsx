import React, { useState, useEffect } from "react";
import { Navigate } from "react-router-dom";
import DashboardLayout from "@/components/Dashboard";
import { useAuth } from "@/context/AuthContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Scissors,
  VideoIcon,
  BookOpen,
  BarChart,
  Calendar,
  Settings,
  Image,
  Share2,
  Cloud,
  TrendingUp,
  Users,
  Play,
  Eye,
  Heart,
  MessageCircle,
  Upload,
  Edit,
  Plus,
  Filter,
  Search,
  MoreVertical,
  CheckCircle,
  Clock,
  AlertCircle,
  ArrowUp,
  ArrowDown,
  Zap,
  Gift,
  Link2,
  Check,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import {
  getUserClips,
  getUserStats,
  getUserSocialPlatforms,
  type UserClip,
  type UserStats,
  type SocialPlatform,
} from "@/services/userService";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Toggle } from "@/components/ui/toggle";
import { supabase } from "@/lib/supabase";

const HomePage = () => {
  const { isAuthenticated, user, isLoading, markWelcomeModalAsSeen } =
    useAuth();
  const [showWelcomeModal, setShowWelcomeModal] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");
  const [searchQuery, setSearchQuery] = useState("");
  const [clips, setClips] = useState<UserClip[]>([]);
  const [stats, setStats] = useState<UserStats>({
    totalViews: 0,
    totalVideos: 0,
    totalClips: 0,
    totalSubscribers: 0,
    watchTime: 0,
    credits: 0,
  });
  const [socialPlatforms, setSocialPlatforms] = useState<SocialPlatform[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [addEmojis, setAddEmojis] = useState(false);

  useEffect(() => {
    if (isAuthenticated && user && user.seen_welcome === false) {
      console.log("New user detected via database flag. Showing modal.");
      setShowWelcomeModal(true);
      // Mark it as seen so it never shows again.
      markWelcomeModalAsSeen();
    }
  }, [user, isAuthenticated, markWelcomeModalAsSeen]);

  useEffect(() => {
    console.log(
      "HomePage useEffect triggered. isAuthenticated:",
      isAuthenticated,
      "user:",
      user
    );
    // Fetch data only when authenticated AND the user object is available
    if (isAuthenticated && user) {
      fetchAllUserData();
    } else {
      console.log(
        "User not authenticated or user object not ready, skipping data fetch"
      );
    }
  }, [isAuthenticated, user]); // Depend on user object as well

  const fetchAllUserData = async () => {
    console.log("Starting to fetch user data...");
    if (!user) {
      console.log("User object not available yet.");
      setIsLoadingData(false);
      return;
    }
    setIsLoadingData(true);
    try {
      // 1. Fetch clips count and data in one API call
      const {
        data: rawClips,
        error: clipsError,
        count,
      } = await supabase
        .from("myclip")
        .select("id, url, created_at, platform", { count: "exact" }) // Get count and specific fields
        .eq("user_id", user.id) // Filter by the current user
        .order("created_at", { ascending: false }) // Get newest first
        .limit(20);

      if (clipsError) throw clipsError;

      // 2. Transform the raw data to match the existing `UserClip` type expected by the UI
      const formattedClips: UserClip[] = (rawClips || []).map((clip) => ({
        id: clip.id,
        // <<< CHANGE: THIS IS THE ONLY LINE THAT NEEDS TO BE MODIFIED
        // The UI expects a thumbnail. We can generate one from the Cloudinary video URL
        // by simply replacing the .mp4 extension with .jpg.
        thumbnail: clip.url.trim().replace(/\.mp4$/, ".jpg"),
        // The UI expects a title. We'll generate one from the creation date.
        title: `Clip from ${new Date(clip.created_at).toLocaleDateString()}`,
        // Provide default values for other fields the UI needs
        status: "published",
        duration: "0:30",
        platform: clip.platform || "Cloudinary",
        views: 0,
        likes: 0,
        comments: 0,
        // The UI in the "Content" tab uses `createdAt` (camelCase)
        createdAt: new Date(clip.created_at).toLocaleDateString(),
      }));

      setClips(formattedClips);

      // 3. Set stats from the API count and the user object from the auth context
      setStats((prevStats) => ({
        ...prevStats,
        totalClips: count || 0, // Set total clips from the count
        credits: user?.credits || 0, // Set credits from the user profile
      }));

      // This call is for a commented-out section, kept to minimize changes
      const platformsData = await getUserSocialPlatforms();
      setSocialPlatforms(platformsData);
    } catch (error) {
      console.error("Error fetching user data:", error);
      setClips([]);
      setStats({
        totalViews: 0,
        totalVideos: 0,
        totalClips: 0,
        totalSubscribers: 0,
        watchTime: 0,
        credits: user?.credits || 0, // Still try to show credits on error
      });
      setSocialPlatforms([]);
    } finally {
      setIsLoadingData(false);
      console.log("Data loading completed");
    }
  };

  if (isLoading) {
    console.log("Auth is loading...");
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    console.log("User not authenticated, redirecting to landing");
    return <Navigate to="/landing" />;
  }

  console.log("User is authenticated, rendering dashboard");

  // Add a simple test to see if the component renders at all
  if (isLoadingData) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="h-8 bg-muted rounded w-64 animate-pulse"></div>
              <div className="h-4 bg-muted rounded w-96 mt-2 animate-pulse"></div>
            </div>
            <div className="h-10 bg-muted rounded w-32 animate-pulse"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div
                key={i}
                className="h-32 bg-muted rounded animate-pulse"
              ></div>
            ))}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div
                key={i}
                className="h-48 bg-muted rounded animate-pulse"
              ></div>
            ))}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Use fetched social platforms data
  const connectedPlatforms = socialPlatforms;

  // Quick action cards
  const quickActions = [
    {
      title: "Create New",
      description: "Start a new video project",
      icon: Plus,
      link: "/smart-clipper",
      color: "text-green-500",
      bgColor: "bg-green-50 dark:bg-green-900/20",
    },
    {
      title: "My Clips",
      description: "View all your clips",
      icon: VideoIcon,
      link: "/clip-results",
      color: "text-blue-500",
      bgColor: "bg-blue-50 dark:bg-blue-900/20",
    },
    {
      title: "Analytics",
      description: "Performance insights",
      icon: BarChart,
      link: "/analytics",
      color: "text-purple-500",
      bgColor: "bg-purple-50 dark:bg-purple-900/20",
    },
    {
      title: "Schedule",
      description: "Content calendar",
      icon: Calendar,
      link: "/calendar",
      color: "text-orange-500",
      bgColor: "bg-orange-50 dark:bg-orange-900/20",
    },
  ];

  // Performance metrics using real data
  const performanceStats = [
    {
      title: "Clips Generated",
      value: stats.totalClips.toString(),
      // change: "+5",
      // trend: "up" as const,
      trend: "",
      icon: Scissors,
      color: "text-purple-500",
    },
    {
      title: "Credits Available",
      value: stats.credits.toString(),
      change: "Available",
      trend: "neutral" as const,
      icon: Zap,
      color: "text-yellow-500",
    },
  ];

  console.log("About to render HomePage with data:", {
    clips: clips.length,
    stats,
    socialPlatforms: socialPlatforms.length,
  });

  return (
    <DashboardLayout>
      <Dialog open={showWelcomeModal} onOpenChange={setShowWelcomeModal}>
        <DialogContent className="sm:max-w-md text-center">
          <DialogHeader>
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100 mb-4">
              <Gift className="h-6 w-6 text-green-600" aria-hidden="true" />
            </div>
            <DialogTitle className="text-2xl font-semibold">
              Welcome to SmartClips!
            </DialogTitle>
            <DialogDescription className="mt-2 text-base text-muted-foreground">
              We're excited to have you on board! To get you started, we've
              added
              <strong> 10 free credits</strong> to your account. Enjoy creating!
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="sm:justify-center">
            <Button type="button" onClick={() => setShowWelcomeModal(false)}>
              Get Started
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <div className="flex flex-col items-center justify-center min-h-screen w-full">
        <div className="sm:max-w-md w-full bg-[#1c1c1c] text-white p-6 sm:p-8 rounded-xl shadow-2xl border border-gray-800">
          {/* Header Section */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 mb-4 rounded-full bg-gradient-to-br from-purple-600 to-blue-500">
              <Zap className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-gray-100">
              Create a New Clip
            </h2>
            <p className="text-gray-400 mt-1">
              Drop a video link to get started.
            </p>
          </div>

          {/* Main Content Section */}
          <div className="space-y-6">
            {/* Input Field */}
            <div className="relative">
              <Link2 className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-500" />
              <Input
                placeholder="https://x.com/user/status/12345"
                className="pl-12 h-14 bg-[#2d2d2d] border-2 border-gray-700 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-base text-white placeholder:text-gray-500 rounded-lg transition-all"
                // <<< CHANGE 1: Bind value and onChange to state
                value={videoUrl}
                onChange={(e) => setVideoUrl(e.target.value)}
              />
            </div>

            {/* Feature Toggle */}
            <Toggle
              pressed={addEmojis}
              onPressedChange={setAddEmojis}
              aria-label="Toggle emojis"
              className="w-full h-14 flex items-center justify-between p-4 rounded-lg bg-[#2d2d2d] border-2 border-transparent data-[state=on]:border-purple-500 data-[state=on]:bg-[#2d2d2d] transition-all"
            >
              <div className="flex items-center">
                <span className="text-3xl mr-3">✨</span>
                <div>
                  <span className="font-semibold text-white">
                    AI Emoji Captions
                  </span>
                  <p className="text-xs text-gray-400">
                    Automatically add relevant emojis
                  </p>
                </div>
              </div>
              <div className="w-6 h-6 flex items-center justify-center rounded-full border-2 border-gray-600 data-[state=on]:border-purple-500 data-[state=on]:bg-purple-600 transition-colors">
                {addEmojis && <Check className="h-4 w-4 text-white" />}
              </div>
            </Toggle>
          </div>

          {/* <<< CHANGE 2: Add an error message display */}
          {error && (
            <p className="text-sm text-red-500 text-center mt-4 -mb-4">
              {error}
            </p>
          )}

          {/* Footer Section */}
          <DialogFooter className="mt-8">
            <Button
              className="w-full h-14 text-lg font-bold bg-gradient-to-r from-purple-600 to-blue-500 text-white hover:opacity-90 transition-opacity disabled:opacity-70"
              // <<< CHANGE 3: Update onClick and handle disabled/loading state
              onClick={handleGetClips}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                  Processing...
                </>
              ) : (
                <>
                  <Scissors className="w-5 h-5 mr-2" />
                  Get Clips
                </>
              )}
            </Button>
          </DialogFooter>
        </div>
      </div>{" "}
      <div className="space-y-6">
        <div></div>

        <div className="flex flex-col gap-4">
          <div>
            <h1 className="text-xl md:text-2xl font-semibold">
              Channel dashboard
            </h1>
            <p className="text-sm md:text-base text-muted-foreground">
              Welcome back, {user?.username || "Creator"}! Here's how your
              content is performing.
            </p>
          </div>
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
            <div className="relative flex-1 sm:max-w-xs">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search your content..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-full"
              />
            </div>
            <Link to="/smart-clipper" className="w-full sm:w-auto">
              <Button className="bg-gradient-purple-blue hover:opacity-90 w-full sm:w-auto">
                <Plus className="h-4 w-4 mr-2" />
                Create
              </Button>
            </Link>
          </div>
        </div>

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4">
          {performanceStats.map((stat, index) => (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardContent className="p-3 md:p-6">
                <div className="flex items-center justify-between mb-2 md:mb-4">
                  <div
                    className={`p-1.5 md:p-2 rounded-lg ${
                      stat.color === "text-blue-500"
                        ? "bg-blue-50 dark:bg-blue-900/20"
                        : stat.color === "text-green-500"
                        ? "bg-green-50 dark:bg-green-900/20"
                        : stat.color === "text-purple-500"
                        ? "bg-purple-50 dark:bg-purple-900/20"
                        : "bg-yellow-50 dark:bg-yellow-900/20"
                    }`}
                  >
                    <stat.icon
                      className={`h-4 w-4 md:h-5 md:w-5 ${stat.color}`}
                    />
                  </div>
                  {stat.trend !== "neutral" && (
                    <div
                      className={`flex items-center text-xs ${
                        stat.trend === "up" ? "text-green-600" : "text-red-600"
                      }`}
                    >
                      {stat.trend === "up" ? (
                        <ArrowUp className="h-3 w-3 mr-1" />
                      ) : (
                        <ArrowDown className="h-3 w-3 mr-1" />
                      )}
                      <span className="hidden sm:inline">{stat.change}</span>
                    </div>
                  )}
                </div>
                <div>
                  <p className="text-lg md:text-2xl font-bold mb-1">
                    {stat.value}
                  </p>
                  <p className="text-xs md:text-sm text-muted-foreground">
                    {stat.title}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-4 md:space-y-6"
        >
          <TabsList className=" grid-cols-4 h-auto p-1">
            <TabsTrigger value="overview" className="text-xs md:text-sm py-2">
              Overview
            </TabsTrigger>
            {/* <TabsTrigger value="content" className="text-xs md:text-sm py-2">
              Content
            </TabsTrigger> */}
            {/* <TabsTrigger value="analytics" className="text-xs md:text-sm py-2">
              Analytics
            </TabsTrigger> */}
            {/* <TabsTrigger value="settings" className="text-xs md:text-sm py-2">
              Settings
            </TabsTrigger> */}
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div>
              <div className="flex items-center justify-between mb-3 md:mb-4">
                <h2 className="text-lg md:text-xl font-semibold">
                  Latest uploads
                </h2>
                <Link to="/clip-results">
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs md:text-sm"
                  >
                    View all
                  </Button>
                </Link>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4">
                {clips.slice(0, 4).map((clip) => (
                  <Card
                    key={clip.id}
                    className="group hover:shadow-md transition-all relative overflow-hidden"
                  >
                    <div className="relative">
                      <img
                        src={clip.thumbnail}
                        alt={clip.title}
                        className="w-full h-32 object-cover"
                      />
                      <div className="absolute top-2 left-2">
                        <Badge
                          variant={
                            clip.status === "published"
                              ? "default"
                              : clip.status === "processing"
                              ? "secondary"
                              : "outline"
                          }
                          className="text-xs"
                        >
                          {clip.status === "published" && (
                            <CheckCircle className="h-3 w-3 mr-1" />
                          )}
                          {clip.status === "processing" && (
                            <Clock className="h-3 w-3 mr-1" />
                          )}
                          {clip.status === "draft" && (
                            <Edit className="h-3 w-3 mr-1" />
                          )}
                          {clip.status}
                        </Badge>
                      </div>
                      <div className="absolute bottom-2 right-2">
                        <Badge
                          variant="outline"
                          className="text-xs bg-black/70 text-white border-none"
                        >
                          {clip.duration}
                        </Badge>
                      </div>
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center">
                        <Button
                          size="sm"
                          className="opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <Play className="h-4 w-4 mr-2" />
                          Preview
                        </Button>
                      </div>
                    </div>
                    <CardContent className="p-3">
                      <h3 className="font-medium text-sm line-clamp-2 mb-2">
                        {clip.title}
                      </h3>
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>{clip.platform}</span>
                        <div className="flex items-center gap-3">
                          <span className="flex items-center gap-1">
                            <Eye className="h-3 w-3" />
                            {clip.views.toLocaleString()}
                          </span>
                          <span className="flex items-center gap-1">
                            <Heart className="h-3 w-3" />
                            {clip.likes}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* <div className="grid lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quick actions</CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-2 gap-3">
                  {quickActions.map((action) => (
                    <Link key={action.title} to={action.link}>
                      <Card className="hover:shadow-md transition-all cursor-pointer border-dashed hover:border-solid">
                        <CardContent className="p-4 text-center">
                          <div
                            className={`p-3 rounded-lg ${action.bgColor} mx-auto w-fit mb-3`}
                          >
                            <action.icon
                              className={`h-5 w-5 ${action.color}`}
                            />
                          </div>
                          <h3 className="font-medium text-sm mb-1">
                            {action.title}
                          </h3>
                          <p className="text-xs text-muted-foreground">
                            {action.description}
                          </p>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </CardContent>
              </Card>
            </div> */}
          </TabsContent>

          <TabsContent value="content" className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">Content library</h2>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
                <Button variant="outline" size="sm">
                  Sort by: Latest
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {clips.map((clip) => (
                <Card
                  key={clip.id}
                  className="group hover:shadow-md transition-all"
                >
                  <div className="relative">
                    <img
                      src={clip.thumbnail}
                      alt={clip.title}
                      className="w-full h-40 object-cover rounded-t-lg"
                    />
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center rounded-t-lg">
                      <Button
                        size="sm"
                        className="opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <Play className="h-4 w-4 mr-2" />
                        Preview
                      </Button>
                    </div>
                    <div className="absolute top-2 right-2">
                      <Badge
                        variant="outline"
                        className="text-xs bg-black/70 text-white border-none"
                      >
                        {clip.duration}
                      </Badge>
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-medium mb-2 line-clamp-2">
                      {clip.title}
                    </h3>
                    <div className="flex items-center justify-between text-sm text-muted-foreground mb-3">
                      <span>{clip.platform}</span>
                      <span>{clip.createdAt}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-4">
                        <span className="flex items-center gap-1">
                          <Eye className="h-4 w-4" />
                          {clip.views.toLocaleString()}
                        </span>
                        <span className="flex items-center gap-1">
                          <Heart className="h-4 w-4" />
                          {clip.likes}
                        </span>
                        <span className="flex items-center gap-1">
                          <MessageCircle className="h-4 w-4" />
                          {clip.comments}
                        </span>
                      </div>
                      <Button variant="ghost" size="sm">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <div className="text-center py-12">
              <BarChart className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                Analytics Dashboard
              </h3>
              <p className="text-muted-foreground mb-4">
                Detailed analytics and insights for your content performance
              </p>
              <Link to="/analytics">
                <Button>View Full Analytics</Button>
              </Link>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <div className="text-center py-12">
              <Settings className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">Channel Settings</h3>
              <p className="text-muted-foreground mb-4">
                Manage your channel preferences and configurations
              </p>
              <Link to="/settings">
                <Button>Open Settings</Button>
              </Link>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default HomePage;
