git+https://github.com/m-bain/whisperx.git
absl-py==2.3.0
altgraph==0.17.4
annotated-types==0.7.0
anyio==4.9.0
asgiref==3.7.2
asttokens==3.0.0
async-timeout==4.0.3
attrs==23.2.0
audioread==3.0.1
autobahn==23.6.2
Automat==22.10.0
autopep8==2.3.2
backports.tarfile==1.2.0
bcrypt==4.3.0
beautifulsoup4==4.12.3
blinker==1.8.2
blis==1.3.0
boto3==1.35.54
botocore==1.35.54
Brotli==1.1.0
bs4==0.0.2
cachetools==5.5.2
catalogue==2.0.10
certifi==2024.8.30
cffi==1.16.0
channels==4.0.0
channels-redis==4.2.0
chardet==5.2.0
charset-normalizer==3.3.2
click==8.1.7
cloudinary==1.44.0
cloudpathlib==0.21.1
confection==0.1.5
constantly==23.10.4
contourpy==1.3.2
cryptography==42.0.5
cycler==0.12.1
cymem==2.0.11
Cython==3.1.2
daphne==4.1.0
decorator==4.4.2
defusedxml==0.7.1
distlib==0.3.8
distro==1.9.0
Django==5.0.2
django-cors-headers==4.3.1
djangorestframework==3.14.0
dtw-python==1.5.3
ecdsa==0.19.1
emoji==1.7.0
en-core-web-sm==3.8.0
exceptiongroup==1.3.0
executing==2.1.0
fastapi==0.115.12
ffmpeg-python==0.2.0
filelock==3.13.1
Flask==3.0.3
Flask-Cors==4.0.1
flatbuffers==25.2.10
fonttools==4.58.4
fsspec==2025.5.1
future==1.0.0
google==3.0.0
google-ai-generativelanguage==0.6.15
google-api-core==2.24.2
google-api-python-client==2.172.0
google-auth==2.39.0
google-auth-httplib2==0.2.0
google-cloud-texttospeech==2.26.0
google-genai==1.20.0
google-generativeai==0.8.5
googleapis-common-protos==1.70.0
greenlet==3.2.1
groq==0.12.0
grpcio==1.71.0
grpcio-status==1.71.0
h11==0.14.0
hf-xet==1.1.4
httpcore==1.0.7
httplib2==0.22.0
httpx==0.28.1
huggingface-hub==0.33.0
hyperlink==21.0.0
idna==3.10
imageio==2.37.0
imageio-ffmpeg==0.6.0
importlib-metadata==8.7.0
incremental==22.10.0
instagrapi==2.1.2
ipython==8.30.0
itsdangerous==2.2.0
jaraco.classes==3.4.0
jaraco.context==6.0.1
jaraco.functools==4.1.0
jax==0.6.2
jaxlib==0.6.2
jedi==0.19.2
Jinja2==3.1.4
jira==3.8.0
jiter==0.9.0
jmespath==1.0.1
joblib==1.5.1
keyring==25.5.0
kiwisolver==1.4.8
langcodes==3.5.0
langdetect==1.0.9
language-data==1.3.0
lazy-loader==0.4
librosa==0.11.0
llvmlite==0.44.0
macholib==1.16.3
marisa-trie==1.2.1
markdown-it-py==3.0.0
MarkupSafe==2.1.5
matplotlib==3.10.3
matplotlib-inline==0.1.7
mdurl==0.1.2
mediapipe==0.10.21
ml-dtypes==0.5.1
more-itertools==10.5.0
MouseInfo==0.1.3
moviepy==1.0.3
mpmath==1.3.0
msgpack==1.0.8
murmurhash==1.0.13
mutagen==1.47.0
mysqlclient==2.2.4
networkx==3.4.2
nltk==3.9.1
numba==0.61.2
numpy==2.2.6
oauthlib==3.2.2
openai==1.75.0
openai-whisper==20240930
opencv-contrib-python==*********
opencv-python==*********
opt-einsum==3.4.0
outcome==1.3.0.post0
packaging==24.1
pandas==2.2.2
parso==0.8.4
passlib==1.7.4
pexpect==4.9.0
pillow==11.2.1
pilmoji==2.0.4
pip==25.1.1
pipenv==2023.12.1
platformdirs==4.2.0
pooch==1.8.2
preshed==3.0.10
proglog==0.1.12
prompt-toolkit==3.0.48
proto-plus==1.26.1
protobuf==5.29.5
psutil==6.0.0
psycopg==3.2.3
psycopg2-binary==2.9.9
ptyprocess==0.7.0
pure-eval==0.2.3
pyasn1==0.4.8
pyasn1-modules==0.3.0
PyAutoGUI==0.9.54
pycodestyle==2.14.0
pycparser==2.21
pycryptodomex==3.20.0
pydantic==2.7.1
pydantic-core==2.18.2
pydub==0.25.1
PyGetWindow==0.0.9
Pygments==2.18.0
pyinstaller==6.10.0
pyinstaller-hooks-contrib==2024.8
PyMsgBox==1.0.9
pynput==1.7.7
pyobjc-core==11.0
pyobjc-framework-ApplicationServices==11.0
pyobjc-framework-Cocoa==11.0
pyobjc-framework-CoreText==11.0
pyobjc-framework-Quartz==11.0
pyOpenSSL==24.0.0
pyparsing==3.2.3
pyperclip==1.8.2
PyRect==0.2.0
PyScreeze==0.1.30
PySocks==1.7.1
pysrt==1.1.2
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-jose==3.4.0
python-multipart==0.0.20
pytweening==1.2.0
pytz==2024.1
PyYAML==6.0.2
random-user-agent==1.0.1
redis==5.0.2
regex==2024.11.6
requests==2.31.0
requests-oauthlib==2.0.0
requests-toolbelt==0.7.0
rich==14.0.0
rsa==4.9.1
rubicon-objc==0.4.9
s3transfer==0.10.3
safetensors==0.5.3
scikit-learn==1.7.0
scipy==1.15.3
selenium==4.26.1
selenium-captcha-solver==0.0.4
sentencepiece==0.2.0
service-identity==24.1.0
setuptools==69.1.1
shellingham==1.5.4
six==1.16.0
smart-open==7.1.0
sniffio==1.3.1
sortedcontainers==2.4.0
sounddevice==0.5.2
soundfile==0.13.1
soupsieve==2.5
soxr==0.5.0.post1
spacy==3.8.7
spacy-legacy==3.0.12
spacy-loggers==1.0.5
SpeechRecognition==3.8.1
SQLAlchemy==2.0.40
sqlparse==0.4.4
srsly==2.5.1
stack-data==0.6.3
starlette==0.46.2
sympy==1.14.0
textblob==0.19.0
thinc==8.3.6
threadpoolctl==3.6.0
tiktoken==0.9.0
tokenizers==0.21.1
tomli==2.2.1
torch==2.6.0
torchaudio==2.6.0
torchvision==0.21.0 
tqdm==4.67.1
traitlets==5.14.3
transformers==4.52.4
trio==0.25.1
trio-websocket==0.11.1
Twisted==24.3.0
txaio==23.1.1
typer==0.16.0
typing-extensions==4.13.2
typing-inspection==0.4.1
tzdata==2024.1
undetected-chromedriver==3.5.5
uritemplate==4.2.0
urllib3==1.26.20
uvicorn==0.34.2
virtualenv==20.25.1
wasabi==1.1.3
wcwidth==0.2.13
weasel==0.4.1
webdriver-manager==4.0.2
websocket-client==1.8.0
websockets==15.0.1
Werkzeug==3.0.3
whisper-timestamped==1.15.8
wrapt==1.17.2
wsproto==1.2.0
yt-dlp==2025.6.9
zipp==3.22.0
zope.interface==6.2
PocketSphinx
scenedetect[opencv]
faster-whisper
huggingface_hub[hf_xet]