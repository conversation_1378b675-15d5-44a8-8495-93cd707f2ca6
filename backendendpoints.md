# 📋 **Manual Editor API Endpoints - Comprehensive Documentation**

## 🎯 **Overview**
The Manual Editor API provides a complete backend system for video editing operations, including project management, timeline manipulation, text overlays, effects, and export functionality.

---

## 🗂️ **1. Project Management Endpoints**

### **Create Project**
```http
POST /editor/projects
Content-Type: application/json
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "name": "My Video Project",
  "description": "A comprehensive video editing project",
  "timeline_data": {
    "duration": 60.0,
    "tracks": 2
  },
  "clips": [],
  "text_overlays": [],
  "effects": []
}
```

**Response:**
```json
{
  "id": "project_1642123456",
  "name": "My Video Project",
  "description": "A comprehensive video editing project",
  "timeline_data": { "duration": 60.0, "tracks": 2 },
  "clips": [],
  "text_overlays": [],
  "effects": [],
  "created_at": "2024-01-15T10:00:00Z",
  "updated_at": "2024-01-15T10:00:00Z",
  "user_id": "user123"
}
```

### **Get All Projects**
```http
GET /editor/projects
Authorization: Bearer {token}
```

**Response:**
```json
[
  {
    "id": "project_1",
    "name": "My First Project",
    "description": "A sample video project",
    "created_at": "2024-01-15T10:00:00Z",
    "updated_at": "2024-01-15T10:30:00Z",
    "clip_count": 3,
    "duration": 45.5
  }
]
```

### **Get Specific Project**
```http
GET /editor/projects/{project_id}
Authorization: Bearer {token}
```

**Response:**
```json
{
  "id": "project_123",
  "name": "My Video Project",
  "description": "A comprehensive video editing project",
  "timeline_data": { "duration": 60.0, "tracks": 2 },
  "clips": [...],
  "text_overlays": [...],
  "effects": [...],
  "created_at": "2024-01-15T10:00:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### **Update Project**
```http
PUT /editor/projects/{project_id}
Content-Type: application/json
Authorization: Bearer {token}
```

### **Delete Project**
```http
DELETE /editor/projects/{project_id}
Authorization: Bearer {token}
```

---

## ⏱️ **2. Timeline Operations**

### **Add Clip to Timeline**
```http
POST /editor/projects/{project_id}/clips
Content-Type: application/json
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "id": "clip_123",
  "start": 0,
  "end": 30,
  "duration": 30,
  "url": "https://example.com/video.mp4",
  "title": "Intro Clip",
  "track": 0
}
```

**Response:**
```json
{
  "message": "Clip added to timeline",
  "clip": {
    "id": "clip_123",
    "start": 0,
    "end": 30,
    "duration": 30,
    "url": "https://example.com/video.mp4",
    "title": "Intro Clip",
    "track": 0
  }
}
```

### **Update Timeline Clip**
```http
PUT /editor/projects/{project_id}/clips/{clip_id}
Content-Type: application/json
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "start": 5,
  "end": 35,
  "title": "Updated Intro Clip"
}
```

### **Delete Timeline Clip**
```http
DELETE /editor/projects/{project_id}/clips/{clip_id}
Authorization: Bearer {token}
```

### **Split Timeline Clip**
```http
POST /editor/projects/{project_id}/clips/{clip_id}/split
Content-Type: application/json
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "split_time": 15.5
}
```

**Response:**
```json
{
  "message": "Clip split successfully",
  "original_clip_id": "clip_123",
  "new_clip_id": "clip_123_split_1642123456",
  "split_time": 15.5
}
```

### **Duplicate Timeline Clip**
```http
POST /editor/projects/{project_id}/clips/{clip_id}/duplicate
Authorization: Bearer {token}
```

**Response:**
```json
{
  "message": "Clip duplicated successfully",
  "original_clip_id": "clip_123",
  "new_clip_id": "clip_123_dup_1642123456"
}
```

---

## 📝 **3. Text Overlay Management**

### **Add Text Overlay**
```http
POST /editor/projects/{project_id}/text-overlays
Content-Type: application/json
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "id": "overlay_123",
  "text": "Hello World",
  "x": 100,
  "y": 200,
  "fontSize": 24,
  "color": "#ffffff",
  "startTime": 5.0,
  "endTime": 10.0
}
```

### **Update Text Overlay**
```http
PUT /editor/projects/{project_id}/text-overlays/{overlay_id}
Content-Type: application/json
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "text": "Updated Text",
  "fontSize": 28,
  "color": "#ff0000"
}
```

### **Delete Text Overlay**
```http
DELETE /editor/projects/{project_id}/text-overlays/{overlay_id}
Authorization: Bearer {token}
```

---

## ✨ **4. Effects System**

### **Add Effect**
```http
POST /editor/projects/{project_id}/effects
Content-Type: application/json
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "type": "fade_in",
  "parameters": {
    "duration": 2.0
  },
  "startTime": 0,
  "endTime": 2.0,
  "target": "clip_123"
}
```

**Response:**
```json
{
  "message": "Effect added",
  "effect": {
    "id": "effect_1642123456",
    "type": "fade_in",
    "parameters": { "duration": 2.0 },
    "startTime": 0,
    "endTime": 2.0,
    "target": "clip_123"
  }
}
```

### **Get Effect Templates**
```http
GET /editor/effects/templates
Authorization: Bearer {token}
```

**Response:**
```json
[
  {
    "id": "fade_in",
    "name": "Fade In",
    "description": "Gradually fade in the video",
    "category": "transition",
    "parameters": {
      "duration": {
        "type": "number",
        "default": 1.0,
        "min": 0.1,
        "max": 5.0
      }
    }
  },
  {
    "id": "blur",
    "name": "Blur",
    "description": "Apply blur effect",
    "category": "filter",
    "parameters": {
      "intensity": {
        "type": "number",
        "default": 5,
        "min": 1,
        "max": 20
      }
    }
  }
]
```

---

## 💾 **5. Save and Export Functionality**

### **Save Project State**
```http
POST /editor/projects/{project_id}/save
Content-Type: application/json
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "timeline_data": {
    "duration": 60.0,
    "tracks": 2
  },
  "clips": [...],
  "text_overlays": [...],
  "effects": [...],
  "version": 1
}
```

**Response:**
```json
{
  "message": "Project saved successfully",
  "project_id": "project_123",
  "saved_at": "2024-01-15T10:30:00Z",
  "version": 1
}
```

### **Export Project**
```http
POST /editor/projects/{project_id}/export
Content-Type: application/json
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "format": "mp4",
  "quality": "high",
  "resolution": "1920x1080",
  "fps": 30
}
```

**Response:**
```json
{
  "export_id": "export_1642123456",
  "status": "processing",
  "message": "Export started successfully",
  "estimated_time": "2-5 minutes",
  "export_options": {
    "format": "mp4",
    "quality": "high",
    "resolution": "1920x1080",
    "fps": 30
  }
}
```

### **Get Export Status**
```http
GET /editor/exports/{export_id}
Authorization: Bearer {token}
```

**Response:**
```json
{
  "export_id": "export_1642123456",
  "status": "completed",
  "progress": 100,
  "download_url": "https://example.com/exported_video.mp4",
  "file_size": "15.2 MB",
  "duration": "45.5s"
}
```

---

## 🔧 **6. Frontend Integration Service**

### **EditorService TypeScript Interface**

```typescript
// File: src/services/editorService.ts

export interface EditorProject {
  id?: string;
  name: string;
  description?: string;
  timeline_data: {
    duration: number;
    tracks: number;
  };
  clips: TimelineClip[];
  text_overlays: TextOverlay[];
  effects: Effect[];
  created_at?: string;
  updated_at?: string;
}

export interface TimelineClip {
  id: string;
  start: number;
  end: number;
  duration: number;
  url: string;
  title: string;
  track: number;
}

export interface TextOverlay {
  id: string;
  text: string;
  x: number;
  y: number;
  fontSize: number;
  color: string;
  startTime: number;
  endTime: number;
}

export interface Effect {
  id: string;
  type: string;
  parameters: Record<string, any>;
  startTime: number;
  endTime: number;
  target: string;
}
```

### **Key Service Functions**

```typescript
// Project Management
export const createProject = async (project: EditorProject): Promise<EditorProject>
export const getProjects = async (): Promise<EditorProject[]>
export const getProject = async (projectId: string): Promise<EditorProject>
export const updateProject = async (projectId: string, project: EditorProject): Promise<EditorProject>
export const deleteProject = async (projectId: string): Promise<void>

// Save & Export
export const saveProject = async (projectId: string, saveData: any): Promise<any>
export const exportProject = async (projectId: string, options: ExportOptions): Promise<any>
export const getExportStatus = async (exportId: string): Promise<any>

// Timeline Operations
export const addClipToTimeline = async (projectId: string, clip: TimelineClip): Promise<any>
export const updateTimelineClip = async (projectId: string, clipId: string, updates: Partial<TimelineClip>): Promise<any>
export const deleteTimelineClip = async (projectId: string, clipId: string): Promise<any>
export const splitTimelineClip = async (projectId: string, clipId: string, splitTime: number): Promise<any>
export const duplicateTimelineClip = async (projectId: string, clipId: string): Promise<any>

// Text Overlays
export const addTextOverlay = async (projectId: string, overlay: TextOverlay): Promise<any>
export const updateTextOverlay = async (projectId: string, overlayId: string, updates: Partial<TextOverlay>): Promise<any>
export const deleteTextOverlay = async (projectId: string, overlayId: string): Promise<any>

// Effects
export const addEffect = async (projectId: string, effect: Partial<Effect>): Promise<any>
export const getEffectTemplates = async (): Promise<EffectTemplate[]>
```

---

## 🎬 **7. ClipEditor Integration**

### **Updated Save Functionality**

```typescript
// File: src/pages/ClipEditor.tsx

const handleSave = async () => {
  try {
    setSaving(true);
    
    // Prepare save data
    const saveData = {
      timeline_data: {
        duration: virtualDuration,
        tracks: 2
      },
      clips: clips.map(clip => ({
        id: clip.id,
        start: clip.start,
        end: clip.end,
        duration: clip.duration,
        url: clip.url,
        title: clip.title,
        track: clip.track
      })),
      text_overlays: textOverlays,
      effects: [],
      version: 1
    };

    const projectId = clipId || `project_${Date.now()}`;
    await saveProject(projectId, saveData);
    
    toast({
      title: "Clip saved",
      description: "Your changes have been saved successfully",
    });
  } catch (error) {
    toast({
      title: "Save failed",
      description: "Failed to save your changes",
      variant: "destructive",
    });
  } finally {
    setSaving(false);
  }
};
```

### **Updated Export Functionality**

```typescript
const handleExport = async () => {
  try {
    setExporting(true);
    
    const projectId = clipId || `project_${Date.now()}`;
    const exportOptions = {
      format: "mp4",
      quality: "high",
      resolution: "1920x1080",
      fps: 30
    };
    
    const exportResult = await exportProject(projectId, exportOptions);
    
    toast({
      title: "Export started",
      description: `Export ID: ${exportResult.export_id}. Estimated time: ${exportResult.estimated_time}`,
    });
    
  } catch (error) {
    toast({
      title: "Export failed",
      description: "Failed to export your clip",
      variant: "destructive",
    });
  } finally {
    setExporting(false);
  }
};
```

---

## 🚀 **8. Usage Examples**

### **Creating a New Project**
```typescript
const newProject = await createProject({
  name: "My New Video",
  description: "A test project",
  timeline_data: { duration: 60, tracks: 2 },
  clips: [],
  text_overlays: [],
  effects: []
});
```

### **Adding a Clip to Timeline**
```typescript
const clip = {
  id: "clip_1",
  start: 0,
  end: 30,
  duration: 30,
  url: "https://example.com/video.mp4",
  title: "Intro",
  track: 0
};

await addClipToTimeline("project_123", clip);
```

### **Splitting a Clip**
```typescript
await splitTimelineClip("project_123", "clip_1", 15.0);
```

### **Adding Text Overlay**
```typescript
const overlay = {
  id: "text_1",
  text: "Welcome!",
  x: 100,
  y: 200,
  fontSize: 24,
  color: "#ffffff",
  startTime: 5,
  endTime: 10
};

await addTextOverlay("project_123", overlay);
```

---

## 📊 **9. Error Handling**

All endpoints return appropriate HTTP status codes:
- **200**: Success
- **400**: Bad Request (invalid parameters)
- **401**: Unauthorized (invalid token)
- **404**: Not Found (project/clip/overlay not found)
- **500**: Internal Server Error

**Error Response Format:**
```json
{
  "detail": "Error message description"
}
```

---

## 🔐 **10. Authentication**

All endpoints require Bearer token authentication:
```http
Authorization: Bearer {your_jwt_token}
```

The token is obtained through the main authentication system and should be included in all API requests.

---

This comprehensive API system provides full CRUD operations for video editing projects, enabling a professional-grade video editor with save/export functionality, timeline manipulation, text overlays, and effects management.
