"""
Simple GPT Editor Demo

This demonstrates the core functionality with a working example.
"""

import os
import asyncio
import logging
import subprocess

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def simple_processing_demo():
    """Simple demo showing basic video processing"""
    
    logger.info("🎬 Simple GPT Editor Demo")
    logger.info("=" * 40)
    
    # Check if sample video exists
    sample_video = "sample_video.mp4"
    if not os.path.exists(sample_video):
        logger.info("Creating sample video...")
        cmd = [
            "ffmpeg", "-y",
            "-f", "lavfi", "-i", "testsrc=duration=5:size=640x480:rate=30",
            "-f", "lavfi", "-i", "sine=frequency=1000:duration=5",
            "-c:v", "libx264", "-c:a", "aac", "-t", "5",
            sample_video
        ]
        subprocess.run(cmd, capture_output=True, check=True)
        logger.info(f"✅ Created sample video: {sample_video}")
    
    # Import our modules
    from ffmpeg_command_generator import FFmpegCommandGenerator
    from gpt_editor_service import GPTEditorService
    
    ffmpeg_gen = FFmpegCommandGenerator()
    gpt_service = GPTEditorService()
    
    # Get video info
    video_info = ffmpeg_gen.get_video_info(sample_video)
    logger.info(f"📊 Video: {video_info['width']}x{video_info['height']}, {video_info['duration']:.1f}s")
    
    # Test simple commands that work
    logger.info("\n🧠 Testing AI Command Parsing:")
    
    test_commands = [
        "Trim this video to 3 seconds",
        "Add text saying 'Hello World'",
        "Make this TikTok ready"
    ]
    
    for cmd in test_commands:
        logger.info(f"\nCommand: '{cmd}'")
        parsed = await gpt_service.parse_editing_command(cmd, video_info)
        for p in parsed:
            logger.info(f"  → {p['action']}: {p['description']} (confidence: {p['confidence']:.2f})")
    
    # Test simple processing - just trim and add text
    logger.info("\n⚙️  Testing Simple Processing:")
    
    simple_commands = [
        {
            "action": "trim",
            "parameters": {"start_time": 0, "end_time": 3},
            "confidence": 0.9,
            "description": "Trim to 3 seconds"
        },
        {
            "action": "add_text",
            "parameters": {"text": "GPT Editor Works!", "position": "center"},
            "confidence": 0.8,
            "description": "Add demo text"
        }
    ]
    
    output_video = "simple_demo_output.mp4"
    
    try:
        # Generate commands
        command_sequence = ffmpeg_gen.generate_command_sequence(
            sample_video, output_video, simple_commands
        )
        
        logger.info(f"Generated {len(command_sequence)} commands")
        
        # Execute with progress tracking
        async def progress_callback(progress, message):
            bar = "█" * int(progress * 10) + "░" * (10 - int(progress * 10))
            logger.info(f"[{bar}] {progress*100:4.1f}% | {message}")
        
        success = await ffmpeg_gen.execute_command_sequence(
            command_sequence, progress_callback
        )
        
        if success and os.path.exists(output_video):
            output_info = ffmpeg_gen.get_video_info(output_video)
            logger.info(f"✅ Success! Output: {output_info['width']}x{output_info['height']}, {output_info['duration']:.1f}s")
            logger.info(f"📁 File: {output_video} ({os.path.getsize(output_video)/1024:.1f} KB)")
        else:
            logger.error("❌ Processing failed")
            
    except Exception as e:
        logger.error(f"❌ Error: {e}")
    
    # Show template system
    logger.info("\n🎨 Available Templates:")
    templates = gpt_service.video_templates
    for name, config in templates.items():
        logger.info(f"  📋 {config['name']}: {config['description']}")
    
    logger.info("\n🎉 Demo Complete!")
    logger.info("The GPT Editor backend is working and ready for API requests!")

if __name__ == "__main__":
    asyncio.run(simple_processing_demo())
