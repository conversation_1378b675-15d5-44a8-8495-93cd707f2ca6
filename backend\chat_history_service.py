"""
Chat History Service for GPT Editor

This module manages chat history for GPT prompts and automatically creates
presets based on successful editing workflows.
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import uuid
from sqlalchemy import create_engine, Column, String, DateTime, Text, Float, Integer, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.dialects.postgresql import JSON
import hashlib

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

Base = declarative_base()

class ChatMessage(Base):
    """Chat message model for storing GPT prompts and responses"""
    __tablename__ = "chat_messages"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, nullable=False)
    session_id = Column(String, nullable=False)
    message_type = Column(String, nullable=False)  # 'user', 'assistant', 'system'
    content = Column(Text, nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # GPT Editor specific fields
    parsed_commands = Column(JSON)
    job_id = Column(String)
    processing_status = Column(String)  # 'pending', 'processing', 'completed', 'failed'
    result_url = Column(String)
    confidence_score = Column(Float)
    processing_time = Column(Float)
    
    # Metadata
    video_info = Column(JSON)
    template_used = Column(String)
    success = Column(Boolean, default=False)

class EditingPreset(Base):
    """Preset model for storing successful editing workflows"""
    __tablename__ = "editing_presets"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False)
    description = Column(Text)
    created_by = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)
    
    # Preset configuration
    original_prompt = Column(Text, nullable=False)
    commands = Column(JSON, nullable=False)  # List of editing commands
    template_config = Column(JSON)
    
    # Usage statistics
    usage_count = Column(Integer, default=0)
    success_rate = Column(Float, default=0.0)
    avg_confidence = Column(Float, default=0.0)
    avg_processing_time = Column(Float, default=0.0)
    
    # Categorization
    category = Column(String)  # 'social_media', 'podcast', 'gaming', etc.
    tags = Column(JSON)  # List of tags
    is_public = Column(Boolean, default=False)
    is_featured = Column(Boolean, default=False)
    
    # Hash for duplicate detection
    command_hash = Column(String, unique=True)

class ChatHistoryService:
    """Service for managing chat history and creating presets"""
    
    def __init__(self, database_url: str = None):
        self.database_url = database_url or os.getenv("DATABASE_URL", "sqlite:///smartclips_chat.db")
        self.engine = create_engine(self.database_url)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # Create tables
        Base.metadata.create_all(bind=self.engine)
        
        logger.info("Chat History Service initialized")
    
    def get_db(self) -> Session:
        """Get database session"""
        return self.SessionLocal()
    
    def save_user_message(self, user_id: str, session_id: str, prompt: str, 
                         video_info: Dict[str, Any] = None) -> str:
        """Save user prompt message"""
        db = self.get_db()
        try:
            message = ChatMessage(
                user_id=user_id,
                session_id=session_id,
                message_type="user",
                content=prompt,
                video_info=video_info
            )
            
            db.add(message)
            db.commit()
            db.refresh(message)
            
            logger.info(f"Saved user message: {message.id}")
            return message.id
            
        except Exception as e:
            logger.error(f"Error saving user message: {e}")
            db.rollback()
            raise
        finally:
            db.close()
    
    def save_assistant_response(self, user_id: str, session_id: str, 
                              parsed_commands: List[Dict[str, Any]], 
                              job_id: str, confidence_score: float,
                              template_used: str = None) -> str:
        """Save assistant response with parsed commands"""
        db = self.get_db()
        try:
            # Create response summary
            response_content = self._create_response_summary(parsed_commands, template_used)
            
            message = ChatMessage(
                user_id=user_id,
                session_id=session_id,
                message_type="assistant",
                content=response_content,
                parsed_commands=parsed_commands,
                job_id=job_id,
                processing_status="pending",
                confidence_score=confidence_score,
                template_used=template_used
            )
            
            db.add(message)
            db.commit()
            db.refresh(message)
            
            logger.info(f"Saved assistant response: {message.id}")
            return message.id
            
        except Exception as e:
            logger.error(f"Error saving assistant response: {e}")
            db.rollback()
            raise
        finally:
            db.close()
    
    def update_processing_status(self, job_id: str, status: str, 
                               result_url: str = None, processing_time: float = None):
        """Update processing status for a job"""
        db = self.get_db()
        try:
            message = db.query(ChatMessage).filter(ChatMessage.job_id == job_id).first()
            if message:
                message.processing_status = status
                message.success = (status == "completed")
                
                if result_url:
                    message.result_url = result_url
                if processing_time:
                    message.processing_time = processing_time
                
                db.commit()
                
                # If successful, consider creating a preset
                if status == "completed" and message.confidence_score > 0.7:
                    self._consider_creating_preset(db, message)
                
                logger.info(f"Updated processing status for job {job_id}: {status}")
            
        except Exception as e:
            logger.error(f"Error updating processing status: {e}")
            db.rollback()
        finally:
            db.close()
    
    def get_chat_history(self, user_id: str, session_id: str = None, 
                        limit: int = 50) -> List[Dict[str, Any]]:
        """Get chat history for a user"""
        db = self.get_db()
        try:
            query = db.query(ChatMessage).filter(ChatMessage.user_id == user_id)
            
            if session_id:
                query = query.filter(ChatMessage.session_id == session_id)
            
            messages = query.order_by(ChatMessage.timestamp.desc()).limit(limit).all()
            
            return [self._message_to_dict(msg) for msg in messages]
            
        except Exception as e:
            logger.error(f"Error getting chat history: {e}")
            return []
        finally:
            db.close()
    
    def create_preset_from_successful_job(self, job_id: str, preset_name: str, 
                                        description: str = None, 
                                        is_public: bool = False) -> Optional[str]:
        """Manually create a preset from a successful job"""
        db = self.get_db()
        try:
            # Find the successful message
            message = db.query(ChatMessage).filter(
                ChatMessage.job_id == job_id,
                ChatMessage.success == True
            ).first()
            
            if not message:
                logger.warning(f"No successful job found for {job_id}")
                return None
            
            # Get the original user prompt
            user_message = db.query(ChatMessage).filter(
                ChatMessage.session_id == message.session_id,
                ChatMessage.message_type == "user",
                ChatMessage.timestamp <= message.timestamp
            ).order_by(ChatMessage.timestamp.desc()).first()
            
            if not user_message:
                logger.warning(f"No user prompt found for job {job_id}")
                return None
            
            # Create preset
            preset_id = self._create_preset(
                db=db,
                name=preset_name,
                description=description or f"Auto-generated from successful prompt: {user_message.content[:100]}...",
                created_by=message.user_id,
                original_prompt=user_message.content,
                commands=message.parsed_commands,
                template_used=message.template_used,
                confidence_score=message.confidence_score,
                processing_time=message.processing_time,
                is_public=is_public
            )
            
            return preset_id
            
        except Exception as e:
            logger.error(f"Error creating preset from job: {e}")
            return None
        finally:
            db.close()
    
    def get_presets(self, user_id: str = None, category: str = None, 
                   is_public: bool = None, limit: int = 20) -> List[Dict[str, Any]]:
        """Get editing presets"""
        db = self.get_db()
        try:
            query = db.query(EditingPreset)
            
            if user_id:
                query = query.filter(EditingPreset.created_by == user_id)
            
            if category:
                query = query.filter(EditingPreset.category == category)
            
            if is_public is not None:
                query = query.filter(EditingPreset.is_public == is_public)
            
            presets = query.order_by(EditingPreset.usage_count.desc()).limit(limit).all()
            
            return [self._preset_to_dict(preset) for preset in presets]
            
        except Exception as e:
            logger.error(f"Error getting presets: {e}")
            return []
        finally:
            db.close()
    
    def use_preset(self, preset_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """Use a preset and update usage statistics"""
        db = self.get_db()
        try:
            preset = db.query(EditingPreset).filter(EditingPreset.id == preset_id).first()
            
            if not preset:
                return None
            
            # Update usage count
            preset.usage_count += 1
            preset.updated_at = datetime.utcnow()
            db.commit()
            
            # Return preset data for use
            return {
                "id": preset.id,
                "name": preset.name,
                "description": preset.description,
                "original_prompt": preset.original_prompt,
                "commands": preset.commands,
                "template_config": preset.template_config,
                "category": preset.category,
                "tags": preset.tags
            }
            
        except Exception as e:
            logger.error(f"Error using preset: {e}")
            return None
        finally:
            db.close()
    
    def _create_response_summary(self, parsed_commands: List[Dict[str, Any]], 
                               template_used: str = None) -> str:
        """Create a summary of the assistant response"""
        summary_parts = []
        
        if template_used:
            summary_parts.append(f"Applied {template_used} template")
        
        summary_parts.append(f"Parsed {len(parsed_commands)} editing commands:")
        
        for i, cmd in enumerate(parsed_commands, 1):
            action = cmd.get('action', 'unknown')
            description = cmd.get('description', 'No description')
            confidence = cmd.get('confidence', 0)
            
            summary_parts.append(f"{i}. {action.title()}: {description} (confidence: {confidence:.2f})")
        
        return "\n".join(summary_parts)
    
    def _consider_creating_preset(self, db: Session, message: ChatMessage):
        """Consider automatically creating a preset from successful job"""
        try:
            # Get the user prompt
            user_message = db.query(ChatMessage).filter(
                ChatMessage.session_id == message.session_id,
                ChatMessage.message_type == "user",
                ChatMessage.timestamp <= message.timestamp
            ).order_by(ChatMessage.timestamp.desc()).first()
            
            if not user_message or not message.parsed_commands:
                return
            
            # Check if similar preset already exists
            command_hash = self._generate_command_hash(message.parsed_commands)
            existing = db.query(EditingPreset).filter(
                EditingPreset.command_hash == command_hash
            ).first()
            
            if existing:
                # Update existing preset statistics
                existing.usage_count += 1
                existing.success_rate = min(existing.success_rate + 0.1, 1.0)
                existing.updated_at = datetime.utcnow()
                db.commit()
                return
            
            # Auto-create preset for high-confidence, multi-step commands
            if (message.confidence_score > 0.8 and 
                len(message.parsed_commands) >= 2 and
                len(user_message.content) > 20):
                
                preset_name = self._generate_preset_name(user_message.content, message.parsed_commands)
                category = self._determine_category(message.parsed_commands, message.template_used)
                
                self._create_preset(
                    db=db,
                    name=preset_name,
                    description=f"Auto-generated from successful prompt",
                    created_by=message.user_id,
                    original_prompt=user_message.content,
                    commands=message.parsed_commands,
                    template_used=message.template_used,
                    confidence_score=message.confidence_score,
                    processing_time=message.processing_time,
                    category=category,
                    command_hash=command_hash,
                    is_public=False
                )
                
                logger.info(f"Auto-created preset: {preset_name}")
        
        except Exception as e:
            logger.error(f"Error considering preset creation: {e}")
    
    def _create_preset(self, db: Session, name: str, description: str, created_by: str,
                      original_prompt: str, commands: List[Dict[str, Any]], 
                      template_used: str = None, confidence_score: float = 0.0,
                      processing_time: float = 0.0, category: str = None,
                      command_hash: str = None, is_public: bool = False) -> str:
        """Create a new preset"""
        
        if not command_hash:
            command_hash = self._generate_command_hash(commands)
        
        preset = EditingPreset(
            name=name,
            description=description,
            created_by=created_by,
            original_prompt=original_prompt,
            commands=commands,
            template_config={"template": template_used} if template_used else None,
            avg_confidence=confidence_score,
            avg_processing_time=processing_time,
            category=category or "general",
            tags=self._generate_tags(commands),
            command_hash=command_hash,
            is_public=is_public,
            usage_count=1,
            success_rate=1.0
        )
        
        db.add(preset)
        db.commit()
        db.refresh(preset)
        
        return preset.id
    
    def _generate_command_hash(self, commands: List[Dict[str, Any]]) -> str:
        """Generate hash for command sequence"""
        command_signature = []
        for cmd in commands:
            action = cmd.get('action', '')
            params = json.dumps(cmd.get('parameters', {}), sort_keys=True)
            command_signature.append(f"{action}:{params}")
        
        signature_str = "|".join(command_signature)
        return hashlib.md5(signature_str.encode()).hexdigest()
    
    def _generate_preset_name(self, prompt: str, commands: List[Dict[str, Any]]) -> str:
        """Generate a preset name from prompt and commands"""
        # Extract key actions
        actions = [cmd.get('action', '') for cmd in commands]
        
        # Common patterns
        if 'apply_template' in actions:
            template_cmd = next((cmd for cmd in commands if cmd.get('action') == 'apply_template'), {})
            template_name = template_cmd.get('parameters', {}).get('template', 'template')
            return f"{template_name.title()} Style"
        
        if 'trim' in actions and 'add_subtitles' in actions:
            return "Quick Clip with Subtitles"
        
        if 'crop' in actions:
            crop_cmd = next((cmd for cmd in commands if cmd.get('action') == 'crop'), {})
            aspect_ratio = crop_cmd.get('parameters', {}).get('aspect_ratio', '')
            if aspect_ratio == '9:16':
                return "Vertical Format"
            elif aspect_ratio == '1:1':
                return "Square Format"
        
        # Fallback to first few words of prompt
        words = prompt.split()[:3]
        return " ".join(words).title()
    
    def _determine_category(self, commands: List[Dict[str, Any]], template_used: str = None) -> str:
        """Determine category from commands and template"""
        if template_used:
            return template_used
        
        actions = [cmd.get('action', '') for cmd in commands]
        
        if 'apply_template' in actions:
            template_cmd = next((cmd for cmd in commands if cmd.get('action') == 'apply_template'), {})
            return template_cmd.get('parameters', {}).get('template', 'general')
        
        if any(action in ['crop', 'add_emojis'] for action in actions):
            return 'social_media'
        
        if 'add_subtitles' in actions:
            return 'educational'
        
        return 'general'
    
    def _generate_tags(self, commands: List[Dict[str, Any]]) -> List[str]:
        """Generate tags from commands"""
        tags = []
        actions = [cmd.get('action', '') for cmd in commands]
        
        if 'trim' in actions:
            tags.append('trimming')
        if 'crop' in actions:
            tags.append('format-conversion')
        if 'add_subtitles' in actions:
            tags.append('subtitles')
        if 'add_text' in actions:
            tags.append('text-overlay')
        if 'apply_template' in actions:
            tags.append('template')
        if any(action in ['add_effects', 'color_correction'] for action in actions):
            tags.append('effects')
        
        return tags
    
    def _message_to_dict(self, message: ChatMessage) -> Dict[str, Any]:
        """Convert message to dictionary"""
        return {
            "id": message.id,
            "message_type": message.message_type,
            "content": message.content,
            "timestamp": message.timestamp.isoformat(),
            "parsed_commands": message.parsed_commands,
            "job_id": message.job_id,
            "processing_status": message.processing_status,
            "result_url": message.result_url,
            "confidence_score": message.confidence_score,
            "processing_time": message.processing_time,
            "template_used": message.template_used,
            "success": message.success
        }
    
    def _preset_to_dict(self, preset: EditingPreset) -> Dict[str, Any]:
        """Convert preset to dictionary"""
        return {
            "id": preset.id,
            "name": preset.name,
            "description": preset.description,
            "created_at": preset.created_at.isoformat(),
            "original_prompt": preset.original_prompt,
            "commands": preset.commands,
            "template_config": preset.template_config,
            "usage_count": preset.usage_count,
            "success_rate": preset.success_rate,
            "avg_confidence": preset.avg_confidence,
            "category": preset.category,
            "tags": preset.tags,
            "is_public": preset.is_public,
            "is_featured": preset.is_featured
        }

# Global service instance
chat_service = ChatHistoryService()
