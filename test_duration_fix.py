#!/usr/bin/env python3
"""
Test script to verify the duration calculation fixes in SmartClips
"""

import sys
import os
import tempfile
import json
from pathlib import Path

# Add backend to path
sys.path.append('backend')

try:
    from backend.video_processing import segment_transcript, clip_video_from_text
    # Import only what we need from video_processing to avoid main.py static file issues
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the SmartClips root directory")
    sys.exit(1)

def test_duration_validation():
    """Test that duration parameters are properly validated"""
    print("🔍 Testing Duration Parameter Validation...")
    
    # Mock valid timestamps
    mock_timestamps = [
        {"word": "Hello", "start": 0.0, "end": 0.5},
        {"word": "world", "start": 0.5, "end": 1.0},
        {"word": "this", "start": 1.0, "end": 1.3},
        {"word": "is", "start": 1.3, "end": 1.5},
        {"word": "a", "start": 1.5, "end": 1.6},
        {"word": "test", "start": 1.6, "end": 2.0},
        {"word": "video", "start": 2.0, "end": 2.5},
        {"word": "transcript.", "start": 2.5, "end": 3.5},
        {"word": "Let's", "start": 4.0, "end": 4.3},
        {"word": "see", "start": 4.3, "end": 4.6},
        {"word": "how", "start": 4.6, "end": 4.9},
        {"word": "this", "start": 4.9, "end": 5.2},
        {"word": "works", "start": 5.2, "end": 5.8},
        {"word": "with", "start": 5.8, "end": 6.1},
        {"word": "segmentation.", "start": 6.1, "end": 7.0},
        {"word": "Amazing", "start": 8.0, "end": 8.5},
        {"word": "content", "start": 8.5, "end": 9.0},
        {"word": "here!", "start": 9.0, "end": 9.8},
        {"word": "This", "start": 10.5, "end": 10.8},
        {"word": "should", "start": 10.8, "end": 11.2},
        {"word": "create", "start": 11.2, "end": 11.7},
        {"word": "good", "start": 11.7, "end": 12.0},
        {"word": "clips.", "start": 12.0, "end": 12.8},
    ]
    
    mock_transcript = "Hello world this is a test video transcript. Let's see how this works with segmentation. Amazing content here! This should create good clips."
    
    test_cases = [
        # (min_duration, max_duration, should_succeed, description)
        (5.0, 15.0, True, "Valid duration range"),
        (2.0, 8.0, True, "Shorter clips"),
        (10.0, 30.0, True, "Longer clips"),
        (-1.0, 15.0, False, "Negative min_duration"),
        (5.0, -1.0, False, "Negative max_duration"),
        (15.0, 10.0, False, "min_duration >= max_duration"),
        (0.0, 15.0, False, "Zero min_duration"),
        (5.0, 0.0, False, "Zero max_duration"),
    ]
    
    passed = 0
    total = len(test_cases)
    
    for min_dur, max_dur, should_succeed, description in test_cases:
        try:
            segments = segment_transcript(
                mock_transcript,
                mock_timestamps,
                min_duration=min_dur,
                max_duration=max_dur,
                refine_with_ai=False
            )
            
            if should_succeed:
                if segments and len(segments) > 0:
                    # Validate all segments have valid durations
                    all_valid = True
                    for i, segment in enumerate(segments):
                        duration = segment['end'] - segment['start']
                        if duration <= 0:
                            print(f"    ❌ {description}: Segment {i} has invalid duration {duration:.2f}s")
                            all_valid = False
                        elif duration < min_dur:
                            print(f"    ⚠️  {description}: Segment {i} below minimum ({duration:.2f}s < {min_dur}s)")
                        elif duration > max_dur:
                            print(f"    ⚠️  {description}: Segment {i} above maximum ({duration:.2f}s > {max_dur}s)")
                    
                    if all_valid:
                        print(f"    ✅ {description}: Created {len(segments)} valid segments")
                        passed += 1
                    else:
                        print(f"    ❌ {description}: Some segments have invalid durations")
                else:
                    print(f"    ❌ {description}: No segments created")
            else:
                print(f"    ❌ {description}: Should have failed but didn't")
                
        except ValueError as e:
            if not should_succeed:
                print(f"    ✅ {description}: Correctly rejected with error: {str(e)}")
                passed += 1
            else:
                print(f"    ❌ {description}: Unexpected error: {str(e)}")
        except Exception as e:
            print(f"    ❌ {description}: Unexpected exception: {str(e)}")
    
    print(f"\nDuration validation: {passed}/{total} tests passed")
    return passed == total

def test_segment_duration_calculation():
    """Test that segment durations are calculated correctly"""
    print("\n🔍 Testing Segment Duration Calculation...")
    
    # Create test segments with known durations
    test_segments = [
        {"text": "First segment", "start": 0.0, "end": 5.0},  # 5 seconds
        {"text": "Second segment", "start": 5.0, "end": 12.0},  # 7 seconds
        {"text": "Third segment", "start": 12.0, "end": 25.0},  # 13 seconds
        {"text": "Invalid segment", "start": 25.0, "end": 25.0},  # 0 seconds (invalid)
        {"text": "Negative segment", "start": 30.0, "end": 28.0},  # Negative (invalid)
    ]
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        # We can't actually test video clipping without a real video file,
        # but we can test the validation logic
        
        valid_segments = []
        invalid_segments = []
        
        for i, segment in enumerate(test_segments):
            duration = segment["end"] - segment["start"]
            
            if duration > 0:
                valid_segments.append(segment)
                print(f"    ✅ Segment {i}: {duration:.1f}s - Valid")
            else:
                invalid_segments.append(segment)
                print(f"    ❌ Segment {i}: {duration:.1f}s - Invalid")
        
        print(f"\nSegment validation: {len(valid_segments)} valid, {len(invalid_segments)} invalid")
        
        # Test that we correctly identify valid vs invalid segments
        expected_valid = 3  # First three segments should be valid
        expected_invalid = 2  # Last two segments should be invalid
        
        if len(valid_segments) == expected_valid and len(invalid_segments) == expected_invalid:
            print("✅ Segment duration calculation working correctly")
            return True
        else:
            print(f"❌ Expected {expected_valid} valid and {expected_invalid} invalid segments")
            return False

def test_edge_cases():
    """Test edge cases that might cause duration calculation errors"""
    print("\n🔍 Testing Edge Cases...")
    
    edge_cases = [
        # (timestamps, description, should_succeed)
        ([], "Empty timestamps", False),
        ([{"word": "single", "start": 0.0, "end": 1.0}], "Single word", False),  # Too short for min_duration
        ([{"word": "test", "start": 0.0, "end": 0.0}], "Zero duration word", False),
        ([{"word": "negative", "start": 1.0, "end": 0.0}], "Negative duration word", False),
    ]
    
    passed = 0
    total = len(edge_cases)
    
    for timestamps, description, should_succeed in edge_cases:
        try:
            segments = segment_transcript(
                " ".join([ts.get("word", "") for ts in timestamps]),
                timestamps,
                min_duration=5.0,
                max_duration=15.0,
                refine_with_ai=False
            )
            
            if should_succeed:
                if segments:
                    print(f"    ✅ {description}: Handled correctly")
                    passed += 1
                else:
                    print(f"    ❌ {description}: No segments created when expected")
            else:
                if not segments:
                    print(f"    ✅ {description}: Correctly handled (no segments)")
                    passed += 1
                else:
                    print(f"    ❌ {description}: Created segments when shouldn't have")
                    
        except Exception as e:
            if not should_succeed:
                print(f"    ✅ {description}: Correctly failed with: {str(e)}")
                passed += 1
            else:
                print(f"    ❌ {description}: Unexpected error: {str(e)}")
    
    print(f"\nEdge case handling: {passed}/{total} tests passed")
    return passed == total

def test_max_duration_as_limit():
    """Test that max_duration is used as a limit, not a target"""
    print("\n🔍 Testing Max Duration as Limit...")
    
    # Create timestamps for a longer conversation
    long_timestamps = []
    current_time = 0.0
    words = ["This", "is", "a", "very", "long", "conversation", "that", "should", "be", "split", 
             "into", "multiple", "segments", "based", "on", "the", "maximum", "duration", "limit",
             "rather", "than", "trying", "to", "reach", "the", "maximum", "as", "a", "target.",
             "The", "AI", "clipper", "should", "create", "shorter", "clips", "when", "content",
             "is", "punchy", "and", "engaging", "like", "this", "amazing", "example!"]
    
    for word in words:
        start_time = current_time
        end_time = current_time + 0.5  # 0.5 seconds per word
        long_timestamps.append({
            "word": word,
            "start": start_time,
            "end": end_time
        })
        current_time = end_time + 0.1  # Small gap between words
    
    transcript = " ".join(words)
    
    # Test with different max_duration values
    test_cases = [
        (5.0, 10.0, "Short clips"),
        (8.0, 15.0, "Medium clips"),
        (10.0, 20.0, "Longer clips"),
    ]
    
    passed = 0
    for min_dur, max_dur, description in test_cases:
        try:
            segments = segment_transcript(
                transcript,
                long_timestamps,
                min_duration=min_dur,
                max_duration=max_dur,
                refine_with_ai=False
            )
            
            if segments:
                all_within_limit = True
                for i, segment in enumerate(segments):
                    duration = segment['end'] - segment['start']
                    if duration > max_dur:
                        print(f"    ❌ {description}: Segment {i} exceeds max_duration ({duration:.1f}s > {max_dur}s)")
                        all_within_limit = False
                
                if all_within_limit:
                    print(f"    ✅ {description}: All {len(segments)} segments within max_duration limit")
                    passed += 1
                else:
                    print(f"    ❌ {description}: Some segments exceed max_duration")
            else:
                print(f"    ❌ {description}: No segments created")
                
        except Exception as e:
            print(f"    ❌ {description}: Error: {str(e)}")
    
    print(f"\nMax duration limit test: {passed}/{len(test_cases)} passed")
    return passed == len(test_cases)

def main():
    """Run all duration calculation tests"""
    print("🚀 Testing SmartClips Duration Calculation Fixes\n")
    
    tests = [
        test_duration_validation,
        test_segment_duration_calculation,
        test_edge_cases,
        test_max_duration_as_limit,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 Duration Fix Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All duration calculation tests passed!")
        print("✨ The 'could not calculate a valid duration' error should be fixed!")
    else:
        print("⚠️  Some duration calculation tests failed.")
        print("🔧 Additional fixes may be needed.")
    
    return passed == total

if __name__ == "__main__":
    main()
