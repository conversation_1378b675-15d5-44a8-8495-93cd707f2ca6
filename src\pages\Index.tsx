import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import {
  <PERSON>I<PERSON>,
  ArrowRight,
  Play,
  Scissors,
  Sparkles,
  Zap,
  CheckCircle,
  Star,
  Users,
  TrendingUp,
  Shield,
  Clock,
  Link2,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

// Animated background blobs for a 3D atmosphere
// const AnimatedBackground = () => (
//   <div className="absolute inset-0 pointer-events-none z-0 w-full">
//     <div
//       className="absolute"
//       style={{
//         top: "12%",
//         left: "-10%",
//         width: 500,
//         height: 500,
//         background: "radial-gradient(circle, #7f53accc 60%, transparent 100%)",
//         filter: "blur(90px) brightness(1.03)",
//         boxShadow: "0 0 200px 60px #7f53ac88",
//         animation: "blobOscillate1 12s ease-in-out infinite alternate",
//       }}
//     />
//     <div
//       className="absolute"
//       style={{
//         top: "68%",
//         left: "70%",
//         width: 400,
//         height: 400,
//         background: "radial-gradient(circle, #647deecc 60%, transparent 100%)",
//         filter: "blur(90px) brightness(1.06)",
//         boxShadow: "0 0 200px 70px #647dee55",
//         animation: "blobOscillate2 14s ease-in-out infinite alternate",
//       }}
//     />
//     <style>
//       {`
//       @keyframes blobOscillate1 {
//         0% { transform: scale(1) translateY(0) rotate(0deg);}
//         100% { transform: scale(1.05) translateY(32px) rotate(12deg);}
//       }
//       @keyframes blobOscillate2 {
//         0% { transform: scale(1) translateY(0) rotate(0deg);}
//         100% { transform: scale(.98) translateY(-20px) rotate(-8deg);}
//       }
//     `}
//     </style>
//   </div>
// );

const FluidSwitch = () => {
  const [selected, setSelected] = useState("getStarted");
  const [pauseAnim, setPauseAnim] = useState(false);
  const navigate = useNavigate();

  const handleSelect = (option) => {
    setSelected(option);
    if (option === "getStarted") {
      navigate("/login");
    } else {
      const el = document.getElementById("features");
      if (el) el.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <div className="w-full flex flex-col items-center">
      {/* Animation definitions */}
      <style>
        {`
          @keyframes growShrink {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.045); }
          }
          .animate-grow-shrink {
            animation: growShrink 8s ease-in-out infinite;
            animation-play-state: running;
          }
          .pause-animation {
            animation-play-state: paused !important;
          }
        `}
      </style>
      {/* <div
        tabIndex={-1} 
        className={`
          ${!pauseAnim ? "animate-grow-shrink" : "pause-animation"} 
          flex flex-col gap-5 items-center w-fit
          bg-white/10 border border-[#7f53ac1b] p-4 rounded-[2.5rem]
          shadow-3xl relative z-10 transition-transform duration-400 outline-none
        `}
        style={{
          backdropFilter: "blur(6px)",
        }}
        onMouseEnter={() => setPauseAnim(true)}
        onMouseLeave={() => setPauseAnim(false)}
        onFocusCapture={() => setPauseAnim(true)}
        onBlurCapture={() => setPauseAnim(false)}
      >
       
        <div className="flex flex-col sm:flex-row items-center gap-4">
          <div
            className="
            flex items-center rounded-full bg-[#23213a] pr-2 pl-5 py-2
            shadow-lg border border-[#5c4fa622] min-w-[200px]
            transition-transform duration-300 hover:scale-105
          "
          >
            <Link2 className="w-6 h-8 text-[#bdbdbd]" />
            <input
              type="text"
              placeholder="Drop a video link"
              className="bg-transparent border-none outline-none placeholder-[#bdbdbd] text-[#ededed] font-medium ml-3 text-[1.1rem] w-[110px] md:w-[170px] min-w-[92px]"
            />
           <Link to="/login">
            <button className="ml-2 h-[44px] px-7 bg-white hover:bg-gradient-to-br hover:from-brand-purple hover:to-brand-blue hover:text-white text-black font-semibold text-[12px] md:text-lg rounded-full shadow transition-colors duration-300 focus:outline-none">
              Get free clips
            </button>
           </Link>
          </div>
      
          <div className="flex items-center justify-center h-full py-3">
            <span
              className="
              block text-white/70 text-lg font-medium select-none
              bg-[#28243e] px-4 py-1 rounded-full shadow
            "
            >
              or
            </span>
          </div>
        
             <Link to="/login">
          <button
            className="
              h-[56px] px-10 border border-[#3f3f3f]
              bg-[#181826] hover:bg-gradient-to-br hover:from-brand-purple hover:to-brand-blue
              rounded-full text-lg text-white font-semibold shadow-lg transition-colors duration-300 focus:outline-none
            "
          >
            Upload files
          </button>
          </Link>
        </div>
       
      </div> */}
      <br />
      <button
        type="button"
        onClick={() => handleSelect("getStarted")}
        className={`
            flex items-center gap-2 px-8 py-4 rounded-full font-semibold text-[14px] md:text-lg
            shadow-xl transition-transform duration-300 mt-2
            ${
              selected === "getStarted"
                ? "bg-gradient-to-br from-brand-purple to-brand-blue text-white scale-105"
                : "text-muted-foreground bg-white/40 hover:bg-gradient-to-br hover:from-brand-purple/80 hover:to-brand-blue/90 hover:text-white"
            }
          `}
      >
        Get Started Free <ArrowRight className="ml-2 w-5 h-5" />
      </button>
    </div>
  );
};

const FluidSwitchCTA = () => {
  const [selected, setSelected] = useState("getStarted");
  const navigate = useNavigate();
  const handleSelect = (option) => {
    setSelected(option);
    if (option === "getStarted") {
      navigate("/login");
    } else if (option === "contactSales") {
      navigate("/contact");
    }
  };
  return (
    // <div
    //   className="flex gap-2 bg-muted-foreground/10 rounded-full w-fit p-1 shadow-2xl relative border border-brand-purple/15"
    //   style={{
    //     boxShadow: "0 10px 40px 0 #647dee21, 0 2px 8px 0 #7f53ac13",
    //     backdropFilter: "blur(3.5px)",
    //   }}
    // >
    <button
      type="button"
      onClick={() => handleSelect("getStarted")}
      className={`flex items-center gap-2 px-8 py-4 rounded-full font-semibold text-[14px] md:text-lg shadow-lg transition-all
          ${
            selected === "getStarted"
              ? "bg-gradient-purple-blue text-white shadow-3xl scale-105"
              : "text-muted-foreground hover:bg-card/80 hover:scale-104"
          }`}
      style={{
        transition: "all .25s cubic-bezier(.4,0,.2,1)",
        boxShadow:
          selected === "getStarted" ? "0 4px 24px #7f53ac39" : undefined,
      }}
    >
      Get Started Free <ArrowRight className="ml-2 w-5 h-5" />
    </button>
    /* <button
        type="button"
        onClick={() => handleSelect("contactSales")}
        className={`flex items-center gap-2 px-8 py-4 rounded-full font-semibold text-lg shadow-lg transition-all
          ${
            selected === "contactSales"
              ? "bg-gradient-purple-blue text-white shadow-3xl scale-105"
              : "text-muted-foreground hover:bg-card/80 hover:scale-104"
          }`}
        style={{
          transition: "all .25s cubic-bezier(.4,0,.2,1)",
          boxShadow: selected === "contactSales" ? "0 4px 24px #647dee49" : undefined,
        }}
      >
        <Shield className="mr-2 w-5 h-5" /> Contact Sales
      </button> */
    // </div>
  );
};

const FeatureCard = ({ title, description, icon, features, linkTo }) => (
  <div
    className="group relative w-full"
    style={{
      perspective: "650px",
      willChange: "transform",
      transition:
        "transform .45s cubic-bezier(.68,-0.55,.27,1.55),box-shadow .3s",
      boxShadow:
        "0 18px 56px 0 rgba(127,83,172,0.11), 0 2px 8px 0 rgba(100,125,238,0.11)",
      background: "linear-gradient(120deg, #7f53ac14 0%, #647dee12 100%)",
      border: "1.5px solid #7f53ac17",
    }}
    onMouseEnter={(e) =>
      (e.currentTarget.style.transform =
        "translateY(-8px) rotateY(-2deg) scale(1.035)")
    }
    onMouseLeave={(e) => (e.currentTarget.style.transform = "none")}
  >
    <div className="bg-card/50 backdrop-blur border border-border/50 rounded-2xl p-5 h-full flex flex-col items-center justify-center  transition-all duration-300 hover:shadow-2xl hover:shadow-primary/10">
      <div className="w-16 h-16 rounded-xl bg-gradient-purple-blue flex items-center justify-center mb-6 group-hover:scale-110 group-hover:shadow-xl transition-transform duration-300 mx-auto">
        <div className="text-white drop-shadow flex items-center justify-center w-full h-full">
          {icon}
        </div>
      </div>
      <h3 className="text-2xl font-semibold mb-4 text-center">{title}</h3>
      <p className="text-muted-foreground mb-6 leading-relaxed text-center">
        {description}
      </p>
      <ul className="mb-8 space-y-2">
  {features.map((feature, index) => (
    <li key={index} className="flex items-start gap-1">
      <span className="inline-flex w-5 justify-center">
        <CheckCircle className="h-4 w-4 text-primary mt-0.5" />
      </span>
      <span className="text-sm text-muted-foreground flex-1">{feature}</span>
    </li>
  ))}
</ul>

      <Link
        to={linkTo}
        className="inline-flex text-[14px] md:text-[16px] items-center text-primary hover:text-primary/80 font-medium group-hover:translate-x-1 transition-all duration-300 hover:drop-shadow"
      >
        Explore {title}
        <ArrowRight className="ml-2 h-4 w-4" />
      </Link>
    </div>
  </div>
);

const Index = () => {
  const [currentFeature, setCurrentFeature] = useState(0);

  const features = [
    {
      title: "Smart Clipper",
      description:
        "AI-powered video clipping that finds the most engaging moments",
      icon: <Scissors className="h-8 w-8 mx-auto" />,
      color: "text-blue-500",
    },
    {
      title: "Video Editor",
      description: "Professional editing tools with real-time preview",
      icon: <VideoIcon className="h-8 w-8 mx-auto" />,
      color: "text-purple-500",
    },
    {
      title: "Social Integration",
      description: "Direct publishing to all major social platforms",
      icon: <TrendingUp className="h-8 w-8 mx-auto" />,
      color: "text-green-500",
    },
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [features.length]);

  return (
    <div
      className="min-h-screen flex flex-col bg-background relative"
      style={{ perspective: "900px" }}
    >
      {/* <AnimatedBackground /> */}
      <main className="flex-1 relative mt-10 w-full">
        <section className="relative min-h-screen w-full  flex flex-col items-center justify-center overflow-visible">
          <div className="relative z-10 container mx-auto px-4 text-center flex flex-col w-full">
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-light mt-4 mb-8 tracking-tight drop-shadow-[0_6px_32px_#7f53ac42]">
              <span className="block">Transform</span>
              <span className="block gradient-text font-medium drop-shadow-[0_10px_30px_#647dee33]">
                Every Video
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto font-light leading-relaxed drop-shadow-[0_2px_14px_#647dee24]">
              AI-powered video editing that turns long-form content into viral
              clips.
              <br />
              Professional results in minutes, not hours.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16 relative">
              <FluidSwitch />
            </div>
            {/* Animated Feature showcase */}
            <div className="max-w-2xl mx-auto">
              <div
                className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-2xl p-8 transition-all duration-500 shadow-2xl"
                style={{
                  perspective: "660px",
                  transform: "scale3d(1.003,1.016,1.018) rotateY(-3deg)",
                  boxShadow: "0 20px 60px 0 #7f53ac26, 0 2px 8px 0 #647dee16",
                  transition:
                    "transform .39s cubic-bezier(.61,-0.28,.7,1.27), box-shadow .38s",
                }}
                onMouseEnter={(e) =>
                  (e.currentTarget.style.transform =
                    "scale3d(1.033,1.025,1.018) rotateY(2deg)")
                }
                onMouseLeave={(e) =>
                  (e.currentTarget.style.transform =
                    "scale3d(1.003,1.016,1.018) rotateY(-3deg)")
                }
              >
                <div className="flex items-center justify-center mb-4 scale-110 transition-transform animate-pulse [animation-duration:3s]">
                  {features[currentFeature].icon}
                </div>
                <h3 className="text-2xl font-semibold mb-3">
                  {features[currentFeature].title}
                </h3>
                <p className="text-muted-foreground text-lg">
                  {features[currentFeature].description}
                </p>
              </div>
              <div className="flex justify-center mt-6 space-x-2">
                {features.map((_, index) => (
                  <button
                    type="button"
                    key={index}
                    onClick={() => setCurrentFeature(index)}
                    className={`w-3 h-3 rounded-full border border-primary/40 transition-all duration-300
                      ${
                        index === currentFeature
                          ? "bg-primary scale-125 shadow-lg"
                          : "bg-muted-foreground/30 hover:bg-muted-foreground/50 shadow"
                      }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </section>
        {/* Features Section */}
        <section id="features" className="py-32  bg-card/20 relative">
          <div className="container w-full ">
            <div className="text-center mb-20">
              <h2 className="text-4xl md:text-6xl font-light mb-6 tracking-tight">
                Everything You Need
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto font-light">
                Professional video editing tools powered by AI, designed for
                creators who demand excellence
              </p>
            </div>
            <div className="grid lg:grid-cols-2 gap-12 mb-20">
              <FeatureCard
                title="Smart Clipper"
                description="AI analyzes your content to automatically extract the most engaging segments, saving hours of manual editing."
                icon={<Scissors className="h-8 w-8 mx-auto" />}
                features={[
                  "Auto-highlight detection",
                  "Viral moment analysis",
                  "Multi-platform optimization",
                ]}
                linkTo="/login"
              />
              <FeatureCard
                title="Professional Editor"
                description="Full-featured video editor with timeline, effects, and real-time collaboration tools."
                icon={<VideoIcon className="h-8 w-8 mx-auto" />}
                features={[
                  "Timeline editing",
                  "Real-time preview",
                  "Advanced effects",
                ]}
                 linkTo="/login"
              />
              {/* <FeatureCard
                title="Social Integration"
                description="Direct publishing to YouTube, TikTok, Instagram, and more with platform-specific optimization."
                icon={<TrendingUp className="h-8 w-8 mx-auto" />}
                features={["One-click publishing", "Platform optimization", "Analytics tracking"]}
                linkTo="/social-integration"
              /> */}
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div className="space-y-2">
                <div className="text-3xl md:text-4xl font-bold gradient-text drop-shadow-lg">
                  10M+
                </div>
                <div className="text-muted-foreground">Videos Processed</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl md:text-4xl font-bold gradient-text drop-shadow-lg">
                  500K+
                </div>
                <div className="text-muted-foreground">Active Creators</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl md:text-4xl font-bold gradient-text drop-shadow-lg">
                  95%
                </div>
                <div className="text-muted-foreground">Time Saved</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl md:text-4xl font-bold gradient-text drop-shadow-lg">
                  4.9★
                </div>
                <div className="text-muted-foreground">User Rating</div>
              </div>
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section className="py-32 px-4 relative">
          <div className="container mx-auto">
            <div className="text-center mb-20">
              <h2 className="text-4xl md:text-6xl font-light mb-6 tracking-tight">
                Simple. <span className="gradient-text">Powerful.</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto font-light">
                Three steps to transform your content into viral clips
              </p>
            </div>
            <div className="max-w-6xl mx-auto">
              <div className="grid md:grid-cols-3 gap-12">
                <div className="text-center group hover:scale-105 transition-transform duration-300">
                  <div className="relative mb-8">
                    <div className="w-20 h-20 mx-auto rounded-full bg-gradient-purple-blue flex items-center justify-center mb-4 shadow-2xl animate-spin-slow group-hover:scale-110 transition-transform duration-300">
                      <VideoIcon className="h-10 w-10 text-white drop-shadow" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-primary rounded-full flex items-center justify-center text-sm font-bold text-white shadow-md">
                      1
                    </div>
                  </div>
                  <h3 className="text-2xl font-semibold mb-4">
                    Upload Your Video
                  </h3>
                  <p className="text-muted-foreground leading-relaxed">
                    Simply drag and drop your long-form content or paste a URL
                    from YouTube, TikTok, or any supported platform.
                  </p>
                </div>
                <div className="text-center group hover:scale-105 transition-transform duration-300">
                  <div className="relative mb-8">
                    <div className="w-20 h-20 mx-auto rounded-full bg-gradient-purple-blue flex items-center justify-center mb-4 shadow-2xl group-hover:scale-110 transition-transform duration-300 animate-bounce-slow">
                      <Sparkles className="h-10 w-10 text-white drop-shadow" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-primary rounded-full flex items-center justify-center text-sm font-bold text-white shadow-md">
                      2
                    </div>
                  </div>
                  <h3 className="text-2xl font-semibold mb-4">AI Processing</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    Our AI analyzes your content, identifies key moments, and
                    automatically creates engaging clips optimized for each
                    platform.
                  </p>
                </div>
                <div className="text-center group hover:scale-105 transition-transform duration-300">
                  <div className="relative mb-8">
                    <div className="w-20 h-20 mx-auto rounded-full bg-gradient-purple-blue flex items-center justify-center mb-4 shadow-2xl group-hover:scale-110 transition-transform duration-300 animate-bounce-slow">
                      <Zap className="h-10 w-10 text-white drop-shadow" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-primary rounded-full flex items-center justify-center text-sm font-bold text-white shadow-md">
                      3
                    </div>
                  </div>
                  <h3 className="text-2xl font-semibold mb-4">
                    Publish & Share
                  </h3>
                  <p className="text-muted-foreground leading-relaxed">
                    Download your clips or publish directly to social media with
                    one click. Track performance with built-in analytics.
                  </p>
                </div>
                <style>
                  {`
                  /* Animate only grow and shrink (no twinkle, no rotation, no bounce) */
                  .animate-spin-slow { animation: growShrink 2.5s ease-in-out infinite alternate; }
                  .animate-bounce-slow { animation: growShrink 2.5s ease-in-out infinite alternate; }
                  @keyframes growShrink {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.1); }
                    100% { transform: scale(1); }
                  }
                `}
                </style>
              </div>
            </div>
          </div>
        </section>

        {/* Social Proof Section */}
        <section className="py-32 px-4 bg-card/20 relative z-10">
          <div className="container mx-auto text-center">
            <h2 className="text-4xl md:text-5xl font-light mb-6 tracking-tight">
              Trusted by{" "}
              <span className="gradient-text">Creators Worldwide</span>
            </h2>
            <p className="text-xl text-muted-foreground mb-16 max-w-3xl mx-auto font-light">
              Join thousands of content creators who have transformed their
              workflow with SmartClips
            </p>
            <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              {[
                {
                  quote:
                    "SmartClips cut my editing time by 90%. What used to take hours now takes minutes.",
                  name: "Sarah Chen",
                  job: "YouTube Creator, 2M subscribers",
                },
                {
                  quote:
                    "The AI knows exactly which moments will go viral. My engagement is up 300%.",
                  name: "Marcus Rodriguez",
                  job: "TikTok Influencer, 5M followers",
                },
                {
                  quote:
                    "Professional results without the professional price tag. Game changer!",
                  name: "Alex Thompson",
                  job: "Marketing Agency Owner",
                },
              ].map((person, i) => (
                <div
                  key={i}
                  className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-2xl p-8 shadow-2xl transition-transform duration-300 hover:-translate-y-1 hover:scale-[1.03] hover:shadow-lg"
                >
                  <div className="flex justify-center mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className="h-5 w-5 text-yellow-500 fill-current drop-shadow"
                      />
                    ))}
                  </div>
                  <p className="text-muted-foreground mb-6 italic">
                    "{person.quote}"
                  </p>
                  <div className="font-semibold">{person.name}</div>
                  <div className="text-sm text-muted-foreground">
                    {person.job}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA Section */}
        <section className="py-32 px-4 relative overflow-hidden z-20">
          <div className="absolute inset-0 bg-gradient-to-r from-brand-purple/10 to-brand-blue/10" />
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-brand-purple/5 rounded-full blur-3xl" />
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-brand-blue/5 rounded-full blur-3xl" />
          <div className="relative z-10 container mx-auto text-center">
            <h2 className="text-4xl md:text-7xl font-light mb-8 tracking-tight drop-shadow-[0_6px_16px_#7f53ac28]">
              Start Creating
              <span className="block gradient-text font-medium mt-3">Today</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-12 font-light leading-relaxed">
              Join the revolution in video creation. Transform your content,
              grow your audience, and save countless hours with AI-powered
              editing.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12 relative">
              <FluidSwitchCTA />
            </div>
            <div className="flex flex-wrap justify-center items-center gap-8 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                <span>Enterprise Security</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <span>24/7 Support</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                <span>99.9% Uptime</span>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      {/* <footer className="bg-card/30 backdrop-blur-sm py-16 px-4 border-t border-border/30 shadow-xl relative z-30">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-4 gap-8 mb-12">
            <div className="md:col-span-2">
              <Link to="/" className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 rounded-lg bg-gradient-purple-blue flex items-center justify-center shadow-md">
                  <VideoIcon className="h-6 w-6 text-white" />
                </div>
                <span className="font-bold text-2xl gradient-text">SmartClips</span>
              </Link>
              <p className="text-muted-foreground mb-6 max-w-md leading-relaxed">
                Transform your video content with AI-powered editing tools.
                Create viral clips, professional edits, and engaging content in minutes.
              </p>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Users className="h-4 w-4" />
                  <span>500K+ Creators</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Star className="h-4 w-4 text-yellow-500" />
                  <span>4.9/5 Rating</span>
                </div>
              </div>
            </div>
            <div>
              <h3 className="font-semibold mb-4 text-lg">Tools</h3>
              <ul className="space-y-3">
                <li>
                  <Link to="/smart-clipper" className="text-muted-foreground hover:text-foreground transition-colors">
                    Smart Clipper
                  </Link>
                </li>
                <li>
                  <Link to="/video-editor" className="text-muted-foreground hover:text-foreground transition-colors">
                    Video Editor
                  </Link>
                </li>
                <li>
                  <Link to="/analytics" className="text-muted-foreground hover:text-foreground transition-colors">
                    Analytics
                  </Link>
                </li>
                <li>
                  <Link to="/social-integration" className="text-muted-foreground hover:text-foreground transition-colors">
                    Social Integration
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4 text-lg">Company</h3>
              <ul className="space-y-3">
                <li>
                  <Link to="/about" className="text-muted-foreground hover:text-foreground transition-colors">
                    About Us
                  </Link>
                </li>
                <li>
                  <Link to="/contact" className="text-muted-foreground hover:text-foreground transition-colors">
                    Contact
                  </Link>
                </li>
                <li>
                  <Link to="/privacy" className="text-muted-foreground hover:text-foreground transition-colors">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link to="/payment" className="text-muted-foreground hover:text-foreground transition-colors">
                    Pricing
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="pt-8 border-t border-border/30 flex flex-col md:flex-row justify-between items-center">
            <p className="text-muted-foreground text-sm">
              © {new Date().getFullYear()} SmartClips. All rights reserved.
            </p>
            <div className="flex items-center gap-6 mt-4 md:mt-0">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Shield className="h-4 w-4" />
                <span>SOC 2 Compliant</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <CheckCircle className="h-4 w-4" />
                <span>GDPR Ready</span>
              </div>
            </div>
          </div>
        </div>
      </footer> */}
    </div>
  );
};

export default Index;
