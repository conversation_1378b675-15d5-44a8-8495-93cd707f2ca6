import React, { useState, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import DashboardLayout from '@/components/Dashboard';
import {
  Upload,
  Link,
  Video,
  AlertCircle,
  CheckCircle,
  Download,
} from 'lucide-react';

import FaceDetectionConfigComponent, {
  FaceDetectionConfig,
} from '@/components/FaceDetectionConfig';
import { useToast } from '@/components/ui/use-toast';

const FacialAIPage: React.FC = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('upload');
  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [videoUrl, setVideoUrl] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentJobId, setCurrentJobId] = useState<string | null>(null);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [previewData, setPreviewData] = useState<any>(null);
  const [processingStatus, setProcessingStatus] = useState<any>(null);
  const [resultUrl, setResultUrl] = useState<string | null>(null);

  const [config, setConfig] = useState<FaceDetectionConfig>({
    zoom_level: 1.5,
    transition_speed: 0.5,
    detection_sensitivity: 0.7,
    voice_threshold: 0.3,
    padding: 50,
  });

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type.startsWith('video/')) {
        setVideoFile(file);
        setVideoUrl('');
        toast({
          title: 'Video uploaded',
          description: `Selected ${file.name}`,
        });
      } else {
        toast({
          title: 'Invalid file type',
          description: 'Please select a video file',
          variant: 'destructive',
        });
      }
    }
  };

  const handleUrlChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setVideoUrl(event.target.value);
    setVideoFile(null);
  };

  const getAuthToken = () => {
    return localStorage.getItem('token');
  };

  const makeApiCall = async (endpoint: string, formData: FormData) => {
    const token = getAuthToken();
    if (!token) {
      throw new Error('Authentication required');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL}${endpoint}`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'API request failed');
    }

    return response.json();
  };

  const handleAnalyze = useCallback(async () => {
    if (!videoFile && !videoUrl) {
      toast({
        title: 'No video selected',
        description: 'Please upload a video file or provide a URL',
        variant: 'destructive',
      });
      return;
    }

    setIsProcessing(true);
    try {
      const formData = new FormData();

      if (videoFile) {
        formData.append('file', videoFile);
      }

      // Add config as JSON string
      formData.append(
        'request',
        JSON.stringify({
          video_url: videoUrl || null,
          config: config,
        })
      );

      const result = await makeApiCall('/api/face-detection/analyze', formData);

      if (result.success) {
        setAnalysisResult(result.analysis_data);
        toast({
          title: 'Analysis completed',
          description: 'Video analysis finished successfully',
        });
      } else {
        throw new Error(result.error || 'Analysis failed');
      }
    } catch (error) {
      console.error('Analysis error:', error);
      toast({
        title: 'Analysis failed',
        description:
          error instanceof Error ? error.message : 'Unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  }, [videoFile, videoUrl, config, toast]);

  const handlePreview = useCallback(async () => {
    if (!videoFile && !videoUrl) {
      toast({
        title: 'No video selected',
        description: 'Please upload a video file or provide a URL',
        variant: 'destructive',
      });
      return;
    }

    setIsProcessing(true);
    try {
      const formData = new FormData();

      if (videoFile) {
        formData.append('file', videoFile);
      }

      formData.append(
        'request',
        JSON.stringify({
          video_url: videoUrl || null,
          config: config,
        })
      );

      const result = await makeApiCall('/api/face-detection/preview', formData);

      if (result.success) {
        setPreviewData(result.preview_data);
        toast({
          title: 'Preview generated',
          description: 'Face tracking preview created successfully',
        });
      } else {
        throw new Error(result.error || 'Preview generation failed');
      }
    } catch (error) {
      console.error('Preview error:', error);
      toast({
        title: 'Preview failed',
        description:
          error instanceof Error ? error.message : 'Unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  }, [videoFile, videoUrl, config, toast]);

  const handleProcess = useCallback(async () => {
    if (!videoFile && !videoUrl) {
      toast({
        title: 'No video selected',
        description: 'Please upload a video file or provide a URL',
        variant: 'destructive',
      });
      return;
    }

    setIsProcessing(true);
    try {
      const formData = new FormData();

      if (videoFile) {
        formData.append('file', videoFile);
      }

      formData.append(
        'request',
        JSON.stringify({
          video_url: videoUrl || null,
          config: config,
          output_format: 'mp4',
        })
      );

      const result = await makeApiCall('/api/face-detection/process', formData);

      if (result.success && result.job_id) {
        setCurrentJobId(result.job_id);
        setProcessingStatus({
          status: 'pending',
          progress: 0,
          message: 'Processing started...',
        });

        // Start polling for status
        pollJobStatus(result.job_id);

        toast({
          title: 'Processing started',
          description:
            'Your video is being processed with facial AI enhancement',
        });
      } else {
        throw new Error(result.error || 'Processing failed to start');
      }
    } catch (error) {
      console.error('Processing error:', error);
      toast({
        title: 'Processing failed',
        description:
          error instanceof Error ? error.message : 'Unknown error occurred',
        variant: 'destructive',
      });
      setIsProcessing(false);
    }
  }, [videoFile, videoUrl, config, toast]);

  const pollJobStatus = async (jobId: string) => {
    const token = getAuthToken();
    if (!token) return;

    try {
      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/api/face-detection/status/${jobId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        const status = await response.json();
        setProcessingStatus(status);

        if (status.status === 'completed') {
          setResultUrl(status.result_url);
          setIsProcessing(false);
          toast({
            title: 'Processing completed',
            description: 'Your video has been enhanced with facial AI',
          });
        } else if (status.status === 'failed') {
          setIsProcessing(false);
          toast({
            title: 'Processing failed',
            description: status.error || 'Unknown error occurred',
            variant: 'destructive',
          });
        } else {
          // Continue polling
          setTimeout(() => pollJobStatus(jobId), 2000);
        }
      }
    } catch (error) {
      console.error('Status polling error:', error);
      setIsProcessing(false);
    }
  };

  const handleDownload = () => {
    if (resultUrl) {
      window.open(resultUrl, '_blank');
    }
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-8 space-y-8">
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold">Facial AI for Podcasts</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Enhance your podcast videos with intelligent face detection and
            dynamic zoom effects
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Video className="h-5 w-5" />
              Video Input
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="upload">Upload File</TabsTrigger>
                <TabsTrigger value="url">Video URL</TabsTrigger>
              </TabsList>

              <TabsContent value="upload" className="space-y-4">
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                  <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <div className="space-y-2">
                    <Label
                      htmlFor="video-upload"
                      className="text-lg font-medium cursor-pointer"
                    >
                      Upload Video File
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Select a podcast video file (MP4, MOV, AVI)
                    </p>
                    <Input
                      id="video-upload"
                      type="file"
                      accept="video/*"
                      onChange={handleFileUpload}
                      className="hidden"
                    />
                    <Button
                      onClick={() =>
                        document.getElementById('video-upload')?.click()
                      }
                      variant="outline"
                    >
                      Choose File
                    </Button>
                  </div>
                </div>

                {videoFile && (
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      Selected: {videoFile.name} (
                      {(videoFile.size / 1024 / 1024).toFixed(1)} MB)
                    </AlertDescription>
                  </Alert>
                )}
              </TabsContent>

              <TabsContent value="url" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="video-url">Video URL</Label>
                  <div className="flex gap-2">
                    <div className="relative flex-1">
                      <Link className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="video-url"
                        type="url"
                        placeholder="https://example.com/video.mp4"
                        value={videoUrl}
                        onChange={handleUrlChange}
                        className="pl-10"
                      />
                    </div>
                  </div>
                </div>

                {videoUrl && (
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      URL provided: {videoUrl}
                    </AlertDescription>
                  </Alert>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        <FaceDetectionConfigComponent
          config={config}
          onConfigChange={setConfig}
          onAnalyze={handleAnalyze}
          onProcess={handleProcess}
          onPreview={handlePreview}
          isProcessing={isProcessing}
          analysisResult={analysisResult}
          previewData={previewData}
          processingStatus={processingStatus}
        />

        {resultUrl && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                Processing Complete
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                Your video has been successfully enhanced with facial AI
                effects.
              </p>
              <div className="flex gap-3">
                <Button
                  onClick={handleDownload}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Download Enhanced Video
                </Button>
                <Button
                  variant="outline"
                  onClick={() => window.open(resultUrl, '_blank')}
                >
                  View in Browser
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
};

export default FacialAIPage;
