"""
LLM Prompting Test for GPT Editor

This script tests the actual LLM prompting capabilities by sending various
natural language commands and analyzing the AI's interpretation results.
"""

import os
import asyncio
import logging
import json
from datetime import datetime
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_llm_prompting():
    """Test LLM prompting with various natural language commands"""
    
    logger.info("🤖 Testing LLM Prompting Capabilities")
    logger.info("=" * 60)
    
    # Check for OpenAI API key
    openai_key = os.getenv("OPENAI_API_KEY")
    if not openai_key:
        logger.warning("⚠️  OPENAI_API_KEY not found!")
        logger.info("To test full AI capabilities, set your OpenAI API key:")
        logger.info("$env:OPENAI_API_KEY='your-api-key-here'  # PowerShell")
        logger.info("export OPENAI_API_KEY='your-api-key-here'  # Bash")
        logger.info("\nTesting with fallback parsing for now...")
    else:
        logger.info(f"✅ OpenAI API key found: {openai_key[:10]}...")
    
    # Import our service
    from gpt_editor_service import GPTEditorService
    
    gpt_service = GPTEditorService(openai_api_key=openai_key)
    
    # Comprehensive test commands covering different scenarios
    test_commands = [
        {
            "category": "Basic Video Editing",
            "commands": [
                "Trim this video to 30 seconds",
                "Cut the first 10 seconds off this video",
                "Make this video exactly 1 minute long",
                "Remove the last 15 seconds",
                "Keep only the middle 45 seconds"
            ]
        },
        {
            "category": "Social Media Optimization",
            "commands": [
                "Make this TikTok ready with viral subtitles and emojis",
                "Convert this to Instagram Reels format with trendy effects",
                "Optimize for YouTube Shorts with engaging thumbnails",
                "Create a LinkedIn-friendly professional version",
                "Make it perfect for Twitter with square format"
            ]
        },
        {
            "category": "Format Conversion",
            "commands": [
                "Convert this horizontal video to vertical format",
                "Make this video square for Instagram posts",
                "Change aspect ratio to 16:9 for YouTube",
                "Crop to 9:16 for mobile viewing",
                "Resize to 1080p HD quality"
            ]
        },
        {
            "category": "Text and Subtitles",
            "commands": [
                "Add subtitles with modern styling",
                "Put captions at the bottom in white text",
                "Add a title saying 'Amazing Content' at the top",
                "Include auto-generated subtitles with emojis",
                "Add watermark with company logo in corner"
            ]
        },
        {
            "category": "Visual Effects",
            "commands": [
                "Make the colors more vibrant and eye-catching",
                "Brighten this video by 20% and increase contrast",
                "Add a cinematic look with color grading",
                "Make it look more professional with subtle effects",
                "Apply Instagram-style filters and enhancement"
            ]
        },
        {
            "category": "Audio Enhancement",
            "commands": [
                "Enhance the audio quality and remove background noise",
                "Normalize the audio levels for consistent volume",
                "Make the voice clearer and more audible",
                "Add background music that fits the mood",
                "Improve audio clarity for podcast content"
            ]
        },
        {
            "category": "Template Application",
            "commands": [
                "Apply podcast template with professional styling",
                "Use gaming template with dynamic effects",
                "Make it educational style with clean presentation",
                "Apply business template for corporate use",
                "Use social media template for viral content"
            ]
        },
        {
            "category": "Complex Multi-Step Requests",
            "commands": [
                "Take this 5-minute video, trim it to 60 seconds, add viral subtitles, make it vertical for TikTok, and boost the colors",
                "Convert this podcast to YouTube Shorts: crop to vertical, add professional subtitles, enhance audio, and add our branding",
                "Create Instagram content: make it square, add trendy effects, include captions, brighten the video, and add call-to-action text",
                "Prepare for business presentation: keep it professional, add company watermark, normalize audio, ensure good quality, and make it under 2 minutes",
                "Transform into gaming highlight: find exciting moments, boost saturation, add dynamic text effects, optimize for YouTube, and create engaging thumbnail"
            ]
        }
    ]
    
    # Sample video info for context
    sample_video_info = {
        "duration": 180,  # 3 minutes
        "width": 1920,
        "height": 1080,
        "has_audio": True,
        "fps": 30,
        "aspect_ratio": "16:9"
    }
    
    all_results = []
    
    for category_data in test_commands:
        category = category_data["category"]
        commands = category_data["commands"]
        
        logger.info(f"\n🎯 Testing Category: {category}")
        logger.info("=" * (20 + len(category)))
        
        category_results = []
        
        for i, command in enumerate(commands, 1):
            logger.info(f"\n📝 Command {i}: '{command}'")
            logger.info("-" * 60)
            
            try:
                # Time the parsing
                start_time = time.time()
                parsed_commands = await gpt_service.parse_editing_command(
                    command, sample_video_info
                )
                parse_time = time.time() - start_time
                
                # Analyze results
                total_confidence = sum(cmd['confidence'] for cmd in parsed_commands)
                avg_confidence = total_confidence / len(parsed_commands) if parsed_commands else 0
                
                logger.info(f"⚡ Parsed in {parse_time:.3f}s")
                logger.info(f"📊 Generated {len(parsed_commands)} actions (avg confidence: {avg_confidence:.2f})")
                
                # Show detailed results
                for j, cmd in enumerate(parsed_commands, 1):
                    confidence_emoji = "🎯" if cmd['confidence'] > 0.8 else "⚡" if cmd['confidence'] > 0.6 else "🔍"
                    logger.info(f"   {j}. {confidence_emoji} {cmd['action'].upper()}")
                    logger.info(f"      Description: {cmd['description']}")
                    logger.info(f"      Confidence: {cmd['confidence']:.2f}")
                    
                    if cmd['parameters']:
                        logger.info(f"      Parameters: {json.dumps(cmd['parameters'], indent=10)}")
                
                # Estimate processing time
                estimated_time = gpt_service.estimate_processing_time(
                    parsed_commands, sample_video_info['duration']
                )
                logger.info(f"⏱️  Estimated processing time: {estimated_time} seconds")
                
                # Store results
                result = {
                    "command": command,
                    "category": category,
                    "parse_time": parse_time,
                    "num_actions": len(parsed_commands),
                    "avg_confidence": avg_confidence,
                    "estimated_time": estimated_time,
                    "actions": [cmd['action'] for cmd in parsed_commands],
                    "success": True
                }
                
                category_results.append(result)
                
            except Exception as e:
                logger.error(f"❌ Error parsing command: {e}")
                result = {
                    "command": command,
                    "category": category,
                    "error": str(e),
                    "success": False
                }
                category_results.append(result)
        
        all_results.extend(category_results)
        
        # Category summary
        successful = [r for r in category_results if r['success']]
        if successful:
            avg_parse_time = sum(r['parse_time'] for r in successful) / len(successful)
            avg_actions = sum(r['num_actions'] for r in successful) / len(successful)
            avg_confidence = sum(r['avg_confidence'] for r in successful) / len(successful)
            
            logger.info(f"\n📊 {category} Summary:")
            logger.info(f"   Success Rate: {len(successful)}/{len(category_results)} ({len(successful)/len(category_results)*100:.1f}%)")
            logger.info(f"   Avg Parse Time: {avg_parse_time:.3f}s")
            logger.info(f"   Avg Actions Generated: {avg_actions:.1f}")
            logger.info(f"   Avg Confidence: {avg_confidence:.2f}")
    
    # Overall analysis
    logger.info("\n" + "=" * 80)
    logger.info("📈 COMPREHENSIVE LLM PROMPTING ANALYSIS")
    logger.info("=" * 80)
    
    successful_results = [r for r in all_results if r['success']]
    total_commands = len(all_results)
    success_rate = len(successful_results) / total_commands * 100
    
    logger.info(f"🎯 Overall Success Rate: {success_rate:.1f}% ({len(successful_results)}/{total_commands})")
    
    if successful_results:
        # Performance metrics
        avg_parse_time = sum(r['parse_time'] for r in successful_results) / len(successful_results)
        avg_actions = sum(r['num_actions'] for r in successful_results) / len(successful_results)
        avg_confidence = sum(r['avg_confidence'] for r in successful_results) / len(successful_results)
        total_estimated_time = sum(r['estimated_time'] for r in successful_results)
        
        logger.info(f"\n⚡ Performance Metrics:")
        logger.info(f"   Average Parse Time: {avg_parse_time:.3f} seconds")
        logger.info(f"   Average Actions per Command: {avg_actions:.1f}")
        logger.info(f"   Average Confidence Score: {avg_confidence:.2f}")
        logger.info(f"   Total Estimated Processing: {total_estimated_time} seconds")
        
        # Action frequency analysis
        all_actions = []
        for r in successful_results:
            all_actions.extend(r['actions'])
        
        from collections import Counter
        action_counts = Counter(all_actions)
        
        logger.info(f"\n🔧 Most Common Actions Generated:")
        for action, count in action_counts.most_common(10):
            percentage = (count / len(all_actions)) * 100
            logger.info(f"   {action}: {count} times ({percentage:.1f}%)")
        
        # Category performance
        logger.info(f"\n📊 Performance by Category:")
        categories = {}
        for r in successful_results:
            cat = r['category']
            if cat not in categories:
                categories[cat] = []
            categories[cat].append(r)
        
        for cat, results in categories.items():
            cat_avg_confidence = sum(r['avg_confidence'] for r in results) / len(results)
            cat_avg_actions = sum(r['num_actions'] for r in results) / len(results)
            logger.info(f"   {cat}:")
            logger.info(f"     Avg Confidence: {cat_avg_confidence:.2f}")
            logger.info(f"     Avg Actions: {cat_avg_actions:.1f}")
    
    # AI vs Fallback analysis
    if openai_key and gpt_service.client:
        logger.info(f"\n🤖 AI Analysis Mode: ENABLED")
        logger.info(f"   Using OpenAI GPT-4 for intelligent parsing")
        logger.info(f"   Expected higher accuracy and context awareness")
    else:
        logger.info(f"\n🔧 AI Analysis Mode: FALLBACK")
        logger.info(f"   Using rule-based parsing")
        logger.info(f"   Limited but functional command interpretation")
    
    # Recommendations
    logger.info(f"\n💡 Recommendations:")
    if avg_confidence < 0.7:
        logger.info(f"   • Consider improving command parsing accuracy")
        logger.info(f"   • Add more training examples for edge cases")
    if avg_parse_time > 2.0:
        logger.info(f"   • Optimize parsing performance for faster response")
    if avg_actions < 1.5:
        logger.info(f"   • Commands might be generating too few actions")
        logger.info(f"   • Consider more comprehensive command interpretation")
    
    logger.info(f"\n🎉 LLM Prompting Test Complete!")
    
    if success_rate >= 80:
        logger.info(f"✅ Excellent performance! System ready for production.")
    elif success_rate >= 60:
        logger.info(f"⚡ Good performance with room for improvement.")
    else:
        logger.info(f"⚠️  Performance needs improvement before production.")
    
    return successful_results

async def test_specific_prompts():
    """Test specific challenging prompts to see how well the AI handles them"""
    
    logger.info("\n🎯 Testing Specific Challenging Prompts")
    logger.info("=" * 50)
    
    from gpt_editor_service import GPTEditorService
    
    openai_key = os.getenv("OPENAI_API_KEY")
    gpt_service = GPTEditorService(openai_api_key=openai_key)
    
    challenging_prompts = [
        {
            "prompt": "I have a 10-minute boring lecture video. Make it engaging for Gen Z audience on TikTok - add trendy effects, cut it down to 60 seconds of the best parts, add viral-style captions with emojis, make it vertical, and boost the energy with faster pacing.",
            "expected_complexity": "Very High"
        },
        {
            "prompt": "Transform this raw gaming footage into a highlight reel: find the most exciting moments, add 'EPIC' text when something cool happens, make colors pop, add smooth transitions, and optimize for YouTube algorithm.",
            "expected_complexity": "High"
        },
        {
            "prompt": "Take this corporate meeting recording and make it LinkedIn-ready: keep it professional, add clean subtitles, enhance audio clarity, add company branding, and ensure it's under 3 minutes.",
            "expected_complexity": "Medium"
        },
        {
            "prompt": "Just make this video look better.",
            "expected_complexity": "Low (Vague)"
        },
        {
            "prompt": "Create a masterpiece from this footage using advanced AI techniques, cinematic color grading, professional audio mastering, dynamic motion graphics, and viral social media optimization.",
            "expected_complexity": "Very High (Buzzwords)"
        }
    ]
    
    for i, test in enumerate(challenging_prompts, 1):
        logger.info(f"\n🔥 Challenge {i} ({test['expected_complexity']}):")
        logger.info(f"Prompt: '{test['prompt']}'")
        logger.info("-" * 70)
        
        try:
            start_time = time.time()
            parsed_commands = await gpt_service.parse_editing_command(test['prompt'])
            parse_time = time.time() - start_time
            
            logger.info(f"⚡ Parsed in {parse_time:.3f}s")
            logger.info(f"📊 Generated {len(parsed_commands)} actions")
            
            if parsed_commands:
                avg_confidence = sum(cmd['confidence'] for cmd in parsed_commands) / len(parsed_commands)
                logger.info(f"🎯 Average confidence: {avg_confidence:.2f}")
                
                logger.info(f"📋 Actions identified:")
                for j, cmd in enumerate(parsed_commands, 1):
                    confidence_emoji = "🎯" if cmd['confidence'] > 0.8 else "⚡" if cmd['confidence'] > 0.6 else "🔍"
                    logger.info(f"   {j}. {confidence_emoji} {cmd['action']} - {cmd['description']}")
                
                # Analyze complexity handling
                unique_actions = len(set(cmd['action'] for cmd in parsed_commands))
                logger.info(f"🔧 Unique action types: {unique_actions}")
                
                if avg_confidence > 0.7 and len(parsed_commands) >= 3:
                    logger.info(f"✅ Well handled complex prompt")
                elif avg_confidence > 0.5:
                    logger.info(f"⚡ Reasonably handled prompt")
                else:
                    logger.info(f"🔍 Struggled with prompt complexity")
            else:
                logger.info(f"❌ No actions generated")
                
        except Exception as e:
            logger.error(f"❌ Error: {e}")

async def main():
    """Main test function"""
    logger.info("🚀 Starting Comprehensive LLM Prompting Test")
    
    # Test general prompting capabilities
    results = await test_llm_prompting()
    
    # Test specific challenging prompts
    await test_specific_prompts()
    
    logger.info("\n" + "=" * 80)
    logger.info("🎉 ALL LLM PROMPTING TESTS COMPLETE!")
    logger.info("=" * 80)

if __name__ == "__main__":
    asyncio.run(main())
