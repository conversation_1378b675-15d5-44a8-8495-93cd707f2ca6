# SmartClips API Endpoint Verification Script
# PowerShell script to test all API endpoints

$baseUrl = "http://localhost:8000"
$results = @()

function Test-Endpoint {
    param(
        [string]$Method,
        [string]$Endpoint,
        [string]$Description,
        [hashtable]$Body = $null,
        [hashtable]$Headers = $null
    )
    
    $url = "$baseUrl$Endpoint"
    Write-Host "Testing: $Description" -ForegroundColor Yellow
    
    try {
        $params = @{
            Uri = $url
            Method = $Method
            UseBasicParsing = $true
        }
        
        if ($Headers) {
            $params.Headers = $Headers
        }
        
        if ($Body) {
            $params.Body = ($Body | ConvertTo-Json)
            $params.ContentType = "application/json"
        }
        
        $response = Invoke-WebRequest @params
        $statusCode = $response.StatusCode
        $content = $response.Content
        
        Write-Host "✅ PASS - Status: $statusCode" -ForegroundColor Green
        
        $script:results += [PSCustomObject]@{
            Endpoint = $Endpoint
            Method = $Method
            Description = $Description
            Status = "PASS"
            StatusCode = $statusCode
            Response = $content.Substring(0, [Math]::Min(100, $content.Length))
        }
        
        return $response
    }
    catch {
        $statusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode } else { "ERROR" }
        Write-Host "❌ FAIL - Status: $statusCode - Error: $($_.Exception.Message)" -ForegroundColor Red
        
        $script:results += [PSCustomObject]@{
            Endpoint = $Endpoint
            Method = $Method
            Description = $Description
            Status = "FAIL"
            StatusCode = $statusCode
            Response = $_.Exception.Message
        }
        
        return $null
    }
}

Write-Host "🚀 Starting SmartClips API Verification" -ForegroundColor Cyan
Write-Host "=" * 60

# Test 1: Health Check Endpoints
Write-Host "`n📋 Testing Health Check Endpoints" -ForegroundColor Magenta
Test-Endpoint -Method "GET" -Endpoint "/" -Description "Root endpoint"
Test-Endpoint -Method "GET" -Endpoint "/health" -Description "Health check endpoint"

# Test 2: Authentication Endpoints
Write-Host "`n🔐 Testing Authentication Endpoints" -ForegroundColor Magenta

# Create test user
$testUser = @{
    username = "test_deploy_user"
    email = "<EMAIL>"
    password = "TestPassword123!"
}

$userResponse = Test-Endpoint -Method "POST" -Endpoint "/users/" -Description "Create test user" -Body $testUser

# Login to get token
$loginData = @{
    username = $testUser.username
    password = $testUser.password
}

# Convert to form data for OAuth2PasswordRequestForm
$formData = "username=$($loginData.username)&password=$($loginData.password)"
try {
    $tokenResponse = Invoke-WebRequest -Uri "$baseUrl/token" -Method POST -Body $formData -ContentType "application/x-www-form-urlencoded" -UseBasicParsing
    $tokenData = $tokenResponse.Content | ConvertFrom-Json
    $accessToken = $tokenData.access_token
    
    Write-Host "✅ PASS - Token obtained" -ForegroundColor Green
    $script:results += [PSCustomObject]@{
        Endpoint = "/token"
        Method = "POST"
        Description = "Login and get access token"
        Status = "PASS"
        StatusCode = $tokenResponse.StatusCode
        Response = "Token obtained successfully"
    }
}
catch {
    Write-Host "❌ FAIL - Token request failed: $($_.Exception.Message)" -ForegroundColor Red
    $script:results += [PSCustomObject]@{
        Endpoint = "/token"
        Method = "POST"
        Description = "Login and get access token"
        Status = "FAIL"
        StatusCode = "ERROR"
        Response = $_.Exception.Message
    }
    $accessToken = $null
}

# Test protected endpoint if we have a token
if ($accessToken) {
    $authHeaders = @{
        "Authorization" = "Bearer $accessToken"
    }
    
    Test-Endpoint -Method "GET" -Endpoint "/users/me/" -Description "Get current user info" -Headers $authHeaders
}

# Test 3: Video Processing Endpoints
Write-Host "`n🎬 Testing Video Processing Endpoints" -ForegroundColor Magenta

$urlValidation = @{
    url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
}

Test-Endpoint -Method "POST" -Endpoint "/validate-url" -Description "URL validation" -Body $urlValidation
Test-Endpoint -Method "POST" -Endpoint "/url-metadata" -Description "URL metadata" -Body $urlValidation

# Test 4: User Data Endpoints (if authenticated)
if ($accessToken) {
    Write-Host "`n👤 Testing User Data Endpoints" -ForegroundColor Magenta
    
    Test-Endpoint -Method "GET" -Endpoint "/user/clips" -Description "Get user clips" -Headers $authHeaders
    Test-Endpoint -Method "GET" -Endpoint "/user/stats" -Description "Get user stats" -Headers $authHeaders
    Test-Endpoint -Method "GET" -Endpoint "/user/video-count" -Description "Get user video count" -Headers $authHeaders
    Test-Endpoint -Method "GET" -Endpoint "/user/social-platforms" -Description "Get user social platforms" -Headers $authHeaders
}

# Test 5: GPT Editor Endpoints
Write-Host "`n✏️ Testing GPT Editor Endpoints" -ForegroundColor Magenta

Test-Endpoint -Method "GET" -Endpoint "/api/gpt-editor/templates" -Description "Get GPT editor templates"
Test-Endpoint -Method "GET" -Endpoint "/api/gpt-editor/presets" -Description "Get GPT editor presets"

# Test 6: CORS Configuration
Write-Host "`n🌐 Testing CORS Configuration" -ForegroundColor Magenta

try {
    $corsHeaders = @{
        "Origin" = "http://localhost:3000"
        "Access-Control-Request-Method" = "GET"
        "Access-Control-Request-Headers" = "Content-Type"
    }
    
    $corsResponse = Invoke-WebRequest -Uri "$baseUrl/health" -Method OPTIONS -Headers $corsHeaders -UseBasicParsing
    Write-Host "✅ PASS - CORS preflight successful" -ForegroundColor Green
    
    $script:results += [PSCustomObject]@{
        Endpoint = "/health"
        Method = "OPTIONS"
        Description = "CORS preflight request"
        Status = "PASS"
        StatusCode = $corsResponse.StatusCode
        Response = "CORS headers present"
    }
}
catch {
    Write-Host "❌ FAIL - CORS test failed: $($_.Exception.Message)" -ForegroundColor Red
    $script:results += [PSCustomObject]@{
        Endpoint = "/health"
        Method = "OPTIONS"
        Description = "CORS preflight request"
        Status = "FAIL"
        StatusCode = "ERROR"
        Response = $_.Exception.Message
    }
}

# Generate Report
Write-Host "`n📊 VERIFICATION RESULTS" -ForegroundColor Cyan
Write-Host "=" * 60

$totalTests = $results.Count
$passedTests = ($results | Where-Object { $_.Status -eq "PASS" }).Count
$failedTests = $totalTests - $passedTests
$successRate = [math]::Round(($passedTests / $totalTests) * 100, 1)

Write-Host "`nSUMMARY:" -ForegroundColor White
Write-Host "Total Tests: $totalTests" -ForegroundColor White
Write-Host "Passed: $passedTests" -ForegroundColor Green
Write-Host "Failed: $failedTests" -ForegroundColor Red
Write-Host "Success Rate: $successRate%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

Write-Host "`nDETAILED RESULTS:" -ForegroundColor White
$results | Format-Table -Property Endpoint, Method, Description, Status, StatusCode -AutoSize

# Deployment Readiness Assessment
Write-Host "`n🎯 DEPLOYMENT READINESS ASSESSMENT" -ForegroundColor Cyan
Write-Host "=" * 60

if ($successRate -ge 80) {
    Write-Host "🎉 DEPLOYMENT READY" -ForegroundColor Green
    Write-Host "System passes 80%+ of tests and is ready for production deployment." -ForegroundColor Green
}
elseif ($successRate -ge 60) {
    Write-Host "⚠️  DEPLOYMENT CAUTION" -ForegroundColor Yellow
    Write-Host "System passes 60-80% of tests. Review failures before deployment." -ForegroundColor Yellow
}
else {
    Write-Host "🚫 DEPLOYMENT NOT READY" -ForegroundColor Red
    Write-Host "System passes <60% of tests. Fix critical issues before deployment." -ForegroundColor Red
}

# Save results to file
$results | Export-Csv -Path "api_verification_results.csv" -NoTypeInformation
Write-Host "`n📄 Results saved to: api_verification_results.csv" -ForegroundColor Cyan

Write-Host "`n✅ Verification Complete!" -ForegroundColor Green
