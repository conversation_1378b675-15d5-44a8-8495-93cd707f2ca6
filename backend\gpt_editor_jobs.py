"""
GPT Editor Job Processing System

This module handles background job processing for AI-powered video editing operations
with status tracking and progress monitoring.
"""

import os
import asyncio
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
import json
import tempfile
import shutil

from gpt_editor_service import GPTEditorService
from ffmpeg_command_generator import FF<PERSON><PERSON><PERSON><PERSON>mandGenerator
from template_system import TemplateSystemIntegration
from chat_history_service import chat_service
import storage

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GPTEditorJobManager:
    """Manages background jobs for GPT video editing operations"""
    
    def __init__(self, temp_dir: str = None, openai_api_key: str = None):
        self.temp_dir = temp_dir or "temp"
        self.openai_api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
        
        # Initialize services
        self.gpt_service = GPTEditorService(openai_api_key=self.openai_api_key)
        self.ffmpeg_generator = FFmpegCommandGenerator(temp_dir=self.temp_dir)
        self.template_system = TemplateSystemIntegration(
            openai_api_key=self.openai_api_key,
            temp_dir=self.temp_dir
        )
        
        # In-memory job storage (in production, use Redis or database)
        self.jobs: Dict[str, Dict[str, Any]] = {}
        self.job_locks: Dict[str, asyncio.Lock] = {}
        
        # Background task tracking
        self.running_tasks: Dict[str, asyncio.Task] = {}

    def create_job(self, command: str, video_url: str = None, video_file: str = None,
                   template: str = None, options: Dict[str, Any] = None,
                   user_id: str = None, session_id: str = None) -> str:
        """
        Create a new GPT editing job
        
        Args:
            command: Natural language editing command
            video_url: URL to video file
            video_file: Local path to video file
            template: Optional template to apply
            options: Additional processing options
            
        Returns:
            Job ID string
        """
        job_id = str(uuid.uuid4())
        
        job_data = {
            "job_id": job_id,
            "status": "pending",
            "progress": 0.0,
            "command": command,
            "video_url": video_url,
            "video_file": video_file,
            "template": template,
            "options": options or {},
            "parsed_commands": [],
            "result_url": None,
            "error_message": None,
            "processing_log": [],
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "estimated_time": None,
            "user_id": user_id,
            "session_id": session_id or str(uuid.uuid4()),
            "chat_message_id": None
        }
        
        self.jobs[job_id] = job_data
        self.job_locks[job_id] = asyncio.Lock()

        # Save user message to chat history
        if user_id:
            try:
                chat_message_id = chat_service.save_user_message(
                    user_id=user_id,
                    session_id=job_data["session_id"],
                    prompt=command,
                    video_info={"video_url": video_url, "template": template}
                )
                job_data["chat_message_id"] = chat_message_id
            except Exception as e:
                logger.warning(f"Failed to save chat message: {e}")

        logger.info(f"Created GPT editing job: {job_id}")
        return job_id

    async def start_job_processing(self, job_id: str) -> bool:
        """
        Start processing a job in the background
        
        Args:
            job_id: Job ID to process
            
        Returns:
            True if job started successfully
        """
        if job_id not in self.jobs:
            logger.error(f"Job {job_id} not found")
            return False
        
        if job_id in self.running_tasks:
            logger.warning(f"Job {job_id} is already running")
            return False
        
        # Start background processing task
        task = asyncio.create_task(self._process_job(job_id))
        self.running_tasks[job_id] = task
        
        logger.info(f"Started processing job: {job_id}")
        return True

    async def _process_job(self, job_id: str):
        """Process a single job (runs in background)"""
        try:
            await self._update_job_status(job_id, "processing", 0.1, "Starting job processing...")
            
            job_data = self.jobs[job_id]
            
            # Step 1: Download/prepare video file
            video_path = await self._prepare_video_file(job_id)
            if not video_path:
                await self._update_job_status(job_id, "failed", 0.0, error="Failed to prepare video file")
                return
            
            await self._update_job_status(job_id, "processing", 0.2, "Video file prepared")
            
            # Step 2: Get video information
            video_info = self.ffmpeg_generator.get_video_info(video_path)
            await self._update_job_status(job_id, "processing", 0.3, "Analyzed video properties")
            
            # Step 3: Parse editing command
            parsed_commands = await self.gpt_service.parse_editing_command(
                job_data["command"], video_info
            )
            
            if not parsed_commands:
                await self._update_job_status(job_id, "failed", 0.0, error="Failed to parse editing command")
                return
            
            # Update job with parsed commands
            async with self.job_locks[job_id]:
                self.jobs[job_id]["parsed_commands"] = parsed_commands

                # Estimate processing time
                estimated_time = self.gpt_service.estimate_processing_time(
                    parsed_commands, video_info.get("duration", 60)
                )
                self.jobs[job_id]["estimated_time"] = estimated_time

                # Save assistant response to chat history
                if job_data.get("user_id"):
                    try:
                        avg_confidence = sum(cmd['confidence'] for cmd in parsed_commands) / len(parsed_commands)
                        chat_service.save_assistant_response(
                            user_id=job_data["user_id"],
                            session_id=job_data["session_id"],
                            parsed_commands=parsed_commands,
                            job_id=job_id,
                            confidence_score=avg_confidence,
                            template_used=job_data.get("template")
                        )
                    except Exception as e:
                        logger.warning(f"Failed to save assistant response: {e}")
            
            await self._update_job_status(job_id, "processing", 0.4, f"Parsed {len(parsed_commands)} editing commands")
            
            # Step 4: Apply template if specified
            if job_data.get("template"):
                result = await self._apply_template(job_id, video_path, parsed_commands)
                if result:
                    await self._update_job_status(job_id, "completed", 1.0, "Template applied successfully", result_url=result)
                    return
            
            # Step 5: Generate and execute FFmpeg commands
            output_path = os.path.join(self.temp_dir, f"gpt_edited_{job_id}.mp4")
            
            try:
                ffmpeg_commands = self.ffmpeg_generator.generate_command_sequence(
                    video_path, output_path, parsed_commands
                )
                
                await self._update_job_status(job_id, "processing", 0.5, f"Generated {len(ffmpeg_commands)} FFmpeg commands")
                
                # Execute commands with progress tracking
                success = await self.ffmpeg_generator.execute_command_sequence(
                    ffmpeg_commands,
                    progress_callback=lambda progress, msg: self._update_job_progress(job_id, 0.5 + (progress * 0.4), msg)
                )
                
                if not success:
                    await self._update_job_status(job_id, "failed", 0.0, error="FFmpeg processing failed")
                    return
                
                await self._update_job_status(job_id, "processing", 0.9, "Video processing completed")
                
            except Exception as e:
                logger.error(f"Error in FFmpeg processing for job {job_id}: {e}")
                await self._update_job_status(job_id, "failed", 0.0, error=f"Processing error: {str(e)}")
                return
            
            # Step 6: Upload result to storage
            try:
                result_url = storage.upload_to_cloudinary(output_path)
                processing_time = time.time() - start_time if 'start_time' in locals() else 0
                await self._update_job_status(job_id, "completed", 1.0, "Job completed successfully", result_url=result_url)

                # Update chat history with completion
                try:
                    chat_service.update_processing_status(
                        job_id=job_id,
                        status="completed",
                        result_url=result_url,
                        processing_time=processing_time
                    )
                except Exception as e:
                    logger.warning(f"Failed to update chat history: {e}")

                # Clean up temporary files
                self._cleanup_job_files(job_id, video_path, output_path)
                
            except Exception as e:
                logger.error(f"Error uploading result for job {job_id}: {e}")
                await self._update_job_status(job_id, "failed", 0.0, error=f"Upload failed: {str(e)}")
                
        except Exception as e:
            logger.error(f"Unexpected error processing job {job_id}: {e}")
            await self._update_job_status(job_id, "failed", 0.0, error=f"Unexpected error: {str(e)}")
        
        finally:
            # Remove from running tasks
            if job_id in self.running_tasks:
                del self.running_tasks[job_id]

    async def _prepare_video_file(self, job_id: str) -> Optional[str]:
        """Download or prepare video file for processing"""
        job_data = self.jobs[job_id]
        
        if job_data.get("video_file"):
            # Local file path provided
            if os.path.exists(job_data["video_file"]):
                return job_data["video_file"]
            else:
                logger.error(f"Local video file not found: {job_data['video_file']}")
                return None
        
        elif job_data.get("video_url"):
            # Download from URL
            try:
                import requests
                response = requests.get(job_data["video_url"], stream=True)
                response.raise_for_status()
                
                # Save to temporary file
                temp_path = os.path.join(self.temp_dir, f"input_{job_id}.mp4")
                with open(temp_path, 'wb') as f:
                    shutil.copyfileobj(response.raw, f)
                
                return temp_path
                
            except Exception as e:
                logger.error(f"Error downloading video from URL: {e}")
                return None
        
        else:
            logger.error("No video source provided")
            return None

    async def _apply_template(self, job_id: str, video_path: str, parsed_commands: List[Dict[str, Any]]) -> Optional[str]:
        """Apply template processing to video"""
        job_data = self.jobs[job_id]
        template_name = job_data.get("template")
        
        if not template_name:
            return None
        
        try:
            output_dir = os.path.join(self.temp_dir, f"template_output_{job_id}")
            os.makedirs(output_dir, exist_ok=True)
            
            # Apply template using existing system
            result = await self.template_system.apply_template(
                video_path, output_dir, template_name, job_data.get("options", {})
            )
            
            if result.get("status") == "Success" and result.get("clips"):
                # Upload first clip as result
                first_clip = result["clips"][0]
                if first_clip.get("url"):
                    return first_clip["url"]
                elif first_clip.get("path"):
                    return storage.upload_to_cloudinary(first_clip["path"])
            
            return None
            
        except Exception as e:
            logger.error(f"Error applying template for job {job_id}: {e}")
            return None

    async def _update_job_status(self, job_id: str, status: str, progress: float, 
                               message: str = None, error: str = None, result_url: str = None):
        """Update job status and progress"""
        async with self.job_locks[job_id]:
            job_data = self.jobs[job_id]
            job_data["status"] = status
            job_data["progress"] = progress
            job_data["updated_at"] = datetime.now()
            
            if message:
                job_data["processing_log"].append({
                    "timestamp": datetime.now().isoformat(),
                    "message": message
                })
            
            if error:
                job_data["error_message"] = error
            
            if result_url:
                job_data["result_url"] = result_url
        
        logger.info(f"Job {job_id}: {status} ({progress*100:.1f}%) - {message or error or 'Status updated'}")

    async def _update_job_progress(self, job_id: str, progress: float, message: str):
        """Update job progress (callback for FFmpeg processing)"""
        await self._update_job_status(job_id, "processing", progress, message)

    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get current job status"""
        if job_id not in self.jobs:
            return None
        
        job_data = self.jobs[job_id].copy()
        
        # Convert datetime objects to ISO strings for JSON serialization
        job_data["created_at"] = job_data["created_at"].isoformat()
        job_data["updated_at"] = job_data["updated_at"].isoformat()
        
        return job_data

    def cancel_job(self, job_id: str) -> bool:
        """Cancel a running job"""
        if job_id not in self.jobs:
            return False
        
        # Cancel background task if running
        if job_id in self.running_tasks:
            task = self.running_tasks[job_id]
            task.cancel()
            del self.running_tasks[job_id]
        
        # Update job status
        asyncio.create_task(self._update_job_status(job_id, "cancelled", 0.0, "Job cancelled by user"))
        
        logger.info(f"Cancelled job: {job_id}")
        return True

    def cleanup_old_jobs(self, max_age_hours: int = 24):
        """Clean up old completed/failed jobs"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        jobs_to_remove = []
        
        for job_id, job_data in self.jobs.items():
            if (job_data["status"] in ["completed", "failed", "cancelled"] and 
                job_data["updated_at"] < cutoff_time):
                jobs_to_remove.append(job_id)
        
        for job_id in jobs_to_remove:
            del self.jobs[job_id]
            if job_id in self.job_locks:
                del self.job_locks[job_id]
        
        logger.info(f"Cleaned up {len(jobs_to_remove)} old jobs")

    def _cleanup_job_files(self, job_id: str, *file_paths):
        """Clean up temporary files for a job"""
        for file_path in file_paths:
            if file_path and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    logger.info(f"Cleaned up file: {file_path}")
                except Exception as e:
                    logger.warning(f"Failed to clean up file {file_path}: {e}")

    def get_job_statistics(self) -> Dict[str, Any]:
        """Get statistics about job processing"""
        total_jobs = len(self.jobs)
        status_counts = {}
        
        for job_data in self.jobs.values():
            status = job_data["status"]
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return {
            "total_jobs": total_jobs,
            "running_jobs": len(self.running_tasks),
            "status_breakdown": status_counts,
            "active_locks": len(self.job_locks)
        }

# Global job manager instance
job_manager = GPTEditorJobManager()
