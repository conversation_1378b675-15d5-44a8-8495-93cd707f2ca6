"""
AI-Powered Video Editor Service

This module provides natural language processing capabilities for video editing commands.
It uses OpenAI's GPT models to interpret user requests and convert them into structured
video editing operations.
"""

import os
import json
import logging
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import uuid
import re

from openai import AsyncOpenAI
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GPTEditorService:
    """Service for interpreting natural language video editing commands"""
    
    def __init__(self, openai_api_key: str = None):
        self.openai_api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
        if not self.openai_api_key:
            logger.warning("OpenAI API key not provided. AI features will be disabled.")
            self.client = None
        else:
            self.client = AsyncOpenAI(api_key=self.openai_api_key)
        
        # Define available video editing actions
        self.available_actions = {
            "trim": "Cut video to specific time range",
            "crop": "Crop video to specific dimensions or aspect ratio",
            "resize": "Resize video to specific dimensions",
            "add_text": "Add text overlay to video",
            "add_subtitles": "Generate and add subtitles to video",
            "add_music": "Add background music to video",
            "adjust_speed": "Change video playback speed",
            "add_effects": "Apply visual effects (blur, brightness, contrast, etc.)",
            "apply_template": "Apply predefined template styling",
            "enhance_audio": "Improve audio quality",
            "add_transitions": "Add transitions between clips",
            "color_correction": "Adjust colors, saturation, brightness",
            "stabilize": "Apply video stabilization",
            "add_watermark": "Add watermark or logo",
            "extract_audio": "Extract audio from video",
            "merge_videos": "Combine multiple videos",
            "create_thumbnail": "Generate video thumbnail",
            "add_emojis": "Add emoji overlays based on content analysis"
        }
        
        # Define video templates
        self.video_templates = {
            "podcast": {
                "name": "Podcast Style",
                "description": "Professional podcast video with speaker focus and clean aesthetics",
                "effects": [
                    {"action": "crop", "parameters": {"aspect_ratio": "16:9"}},
                    {"action": "add_subtitles", "parameters": {"style": "clean_modern"}},
                    {"action": "color_correction", "parameters": {"brightness": 1.1, "contrast": 1.05}}
                ],
                "style_config": {
                    "subtitle_style": "clean_modern",
                    "color_scheme": "professional",
                    "font_family": "Arial"
                }
            },
            "gaming": {
                "name": "Gaming Highlights",
                "description": "Dynamic gaming video with vibrant colors and effects",
                "effects": [
                    {"action": "crop", "parameters": {"aspect_ratio": "16:9"}},
                    {"action": "add_effects", "parameters": {"saturation": 1.2, "contrast": 1.15}},
                    {"action": "add_text", "parameters": {"style": "gaming", "position": "top"}}
                ],
                "style_config": {
                    "subtitle_style": "viral_style",
                    "color_scheme": "vibrant",
                    "font_family": "Impact"
                }
            },
            "tiktok": {
                "name": "TikTok Vertical",
                "description": "Vertical format optimized for TikTok with engaging effects",
                "effects": [
                    {"action": "crop", "parameters": {"aspect_ratio": "9:16"}},
                    {"action": "add_subtitles", "parameters": {"style": "viral_style"}},
                    {"action": "add_emojis", "parameters": {"enabled": True}}
                ],
                "style_config": {
                    "subtitle_style": "viral_style",
                    "color_scheme": "bright",
                    "font_family": "Montserrat"
                }
            },
            "instagram": {
                "name": "Instagram Reels",
                "description": "Square or vertical format for Instagram with trendy effects",
                "effects": [
                    {"action": "crop", "parameters": {"aspect_ratio": "1:1"}},
                    {"action": "add_effects", "parameters": {"brightness": 1.05, "saturation": 1.1}},
                    {"action": "add_subtitles", "parameters": {"style": "modern"}}
                ],
                "style_config": {
                    "subtitle_style": "modern",
                    "color_scheme": "instagram",
                    "font_family": "Helvetica"
                }
            },
            "youtube": {
                "name": "YouTube Shorts",
                "description": "Vertical format optimized for YouTube Shorts",
                "effects": [
                    {"action": "crop", "parameters": {"aspect_ratio": "9:16"}},
                    {"action": "add_subtitles", "parameters": {"style": "youtube"}},
                    {"action": "add_watermark", "parameters": {"position": "bottom_right"}}
                ],
                "style_config": {
                    "subtitle_style": "youtube",
                    "color_scheme": "youtube",
                    "font_family": "Roboto"
                }
            }
        }

    async def parse_editing_command(self, command: str, video_info: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Parse natural language editing command into structured actions
        
        Args:
            command: Natural language editing command
            video_info: Optional video metadata for context
            
        Returns:
            List of structured editing commands
        """
        if not self.client:
            logger.warning("OpenAI client not available, using fallback parsing")
            return self._fallback_parse_command(command)
        
        try:
            # Create context-aware prompt
            prompt = self._create_parsing_prompt(command, video_info)
            
            response = await self.client.chat.completions.create(
                model="gpt-4-turbo-preview",
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.3,
                response_format={"type": "json_object"}
            )
            
            result = json.loads(response.choices[0].message.content)
            return self._validate_and_structure_commands(result.get("commands", []))
            
        except Exception as e:
            logger.error(f"Error parsing command with AI: {e}")
            return self._fallback_parse_command(command)

    def _get_system_prompt(self) -> str:
        """Get the system prompt for the AI model"""
        actions_list = "\n".join([f"- {action}: {desc}" for action, desc in self.available_actions.items()])
        templates_list = "\n".join([f"- {name}: {info['description']}" for name, info in self.video_templates.items()])
        
        return f"""You are an expert video editing assistant. Your job is to interpret natural language video editing commands and convert them into structured JSON commands.

Available Actions:
{actions_list}

Available Templates:
{templates_list}

You must respond with a JSON object containing a "commands" array. Each command should have:
- action: One of the available actions
- parameters: Object with relevant parameters for the action
- confidence: Float between 0.0 and 1.0 indicating confidence in interpretation
- description: Human-readable description of what this command will do

Example response:
{{
  "commands": [
    {{
      "action": "trim",
      "parameters": {{"start_time": 10, "end_time": 60}},
      "confidence": 0.9,
      "description": "Trim video from 10 seconds to 60 seconds"
    }}
  ]
}}

Be precise with parameters and provide high confidence scores only when you're certain about the interpretation."""

    def _create_parsing_prompt(self, command: str, video_info: Dict[str, Any] = None) -> str:
        """Create a context-aware prompt for parsing the command"""
        prompt = f"Parse this video editing command: '{command}'"
        
        if video_info:
            context = []
            if video_info.get("duration"):
                context.append(f"Video duration: {video_info['duration']} seconds")
            if video_info.get("resolution"):
                context.append(f"Video resolution: {video_info['resolution']}")
            if video_info.get("aspect_ratio"):
                context.append(f"Current aspect ratio: {video_info['aspect_ratio']}")
            if video_info.get("has_audio"):
                context.append(f"Has audio: {video_info['has_audio']}")
            
            if context:
                prompt += f"\n\nVideo context:\n" + "\n".join(context)
        
        return prompt

    def _validate_and_structure_commands(self, commands: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate and structure the parsed commands"""
        structured_commands = []
        
        for cmd in commands:
            if not isinstance(cmd, dict):
                continue
                
            action = cmd.get("action")
            if action not in self.available_actions:
                logger.warning(f"Unknown action: {action}")
                continue
            
            structured_cmd = {
                "action": action,
                "parameters": cmd.get("parameters", {}),
                "confidence": min(max(cmd.get("confidence", 0.5), 0.0), 1.0),
                "description": cmd.get("description", f"Apply {action} to video")
            }
            
            structured_commands.append(structured_cmd)
        
        return structured_commands

    def _fallback_parse_command(self, command: str) -> List[Dict[str, Any]]:
        """Fallback parsing when AI is not available"""
        command_lower = command.lower()
        commands = []
        
        # Simple keyword-based parsing
        if any(word in command_lower for word in ["trim", "cut", "clip"]):
            # Try to extract time ranges
            time_pattern = r'(\d+(?:\.\d+)?)\s*(?:to|-)?\s*(\d+(?:\.\d+)?)'
            match = re.search(time_pattern, command)
            if match:
                start_time, end_time = match.groups()
                commands.append({
                    "action": "trim",
                    "parameters": {"start_time": float(start_time), "end_time": float(end_time)},
                    "confidence": 0.7,
                    "description": f"Trim video from {start_time}s to {end_time}s"
                })
        
        if any(word in command_lower for word in ["subtitle", "caption"]):
            commands.append({
                "action": "add_subtitles",
                "parameters": {"style": "clean_modern"},
                "confidence": 0.8,
                "description": "Add subtitles to video"
            })
        
        if any(word in command_lower for word in ["tiktok", "vertical"]):
            commands.append({
                "action": "apply_template",
                "parameters": {"template": "tiktok"},
                "confidence": 0.9,
                "description": "Apply TikTok template"
            })
        
        # If no specific commands found, suggest adding subtitles as default
        if not commands:
            commands.append({
                "action": "add_subtitles",
                "parameters": {"style": "clean_modern"},
                "confidence": 0.3,
                "description": "Add subtitles (default action)"
            })
        
        return commands

    def get_template_by_name(self, template_name: str) -> Optional[Dict[str, Any]]:
        """Get template configuration by name"""
        return self.video_templates.get(template_name.lower())

    def list_available_templates(self) -> Dict[str, Dict[str, Any]]:
        """Get all available templates"""
        return self.video_templates

    def estimate_processing_time(self, commands: List[Dict[str, Any]], video_duration: float = 60) -> int:
        """Estimate processing time in seconds based on commands and video duration"""
        base_time = 10  # Base processing time
        
        time_multipliers = {
            "trim": 0.1,
            "crop": 0.2,
            "resize": 0.2,
            "add_text": 0.3,
            "add_subtitles": 1.0,  # Transcription takes time
            "add_music": 0.2,
            "adjust_speed": 0.3,
            "add_effects": 0.5,
            "apply_template": 0.8,
            "enhance_audio": 0.7,
            "add_transitions": 0.4,
            "color_correction": 0.3,
            "stabilize": 1.2,
            "add_watermark": 0.2,
            "extract_audio": 0.1,
            "merge_videos": 0.5,
            "create_thumbnail": 0.1,
            "add_emojis": 0.6
        }
        
        total_multiplier = sum(time_multipliers.get(cmd["action"], 0.5) for cmd in commands)
        estimated_time = base_time + (video_duration * total_multiplier)
        
        return max(int(estimated_time), 15)  # Minimum 15 seconds
