from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor
import uuid  # <-- Make sure this import is at the top of your file
import asyncio
from openai import AsyncOpenAI
import url_processor
import storage
from advanced_video_processor import AdvancedVideoProcessor, process_video_with_enhancements
import video_processing
from facial_ai_processor import FacialAIProcessor, ZoomConfig
import models
from database import SessionLocal, engine, Base
import time
from fastapi.staticfiles import StaticFiles
import requests
from google.cloud import texttospeech
import openai
from moviepy.editor import VideoFileClip
import cloudinary.uploader
import cloudinary
from sqlalchemy.orm import Session
from pydantic import BaseModel
from passlib.context import CryptContext
from jose import JWTError, jwt
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from fastapi.middleware.cors import CORSMiddleware
from fastapi import FastAPI, Depends, HTTPException, status, File, UploadFile, Form, BackgroundTasks
import json
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import subprocess
import logging
import shutil
import os
import uvicorn
import ffmpeg
import tempfile

# Load environment variables
from dotenv import load_dotenv
load_dotenv()


import moviepy.config as mpconfig
# mpconfig.change_settings(
#     {"IMAGEMAGICK_BINARY": r"C:\Program Files\ImageMagick-7.1.2-Q16-HDRI\magick.exe"})

# for live
# IMAGEMAGICK_BINARY = os.getenv('IMAGEMAGICK_BINARY', '/usr/bin/convert')
# mpconfig.change_settings({"IMAGEMAGICK_BINARY": IMAGEMAGICK_BINARY})


os.environ["TOKENIZERS_PARALLELISM"] = "false"


# Configure FFmpeg path BEFORE importing any MoviePy modules


def find_ffmpeg():
    # First try to find ffmpeg in PATH
    try:
        # for macOS/Linux - `which`, and for Windows - `where`
        cmd = 'where' if os.name == 'nt' else 'which'
        result = subprocess.run(
            [cmd, 'ffmpeg'], capture_output=True, text=True)
        if result.returncode == 0:
            ffmpeg_path = result.stdout.strip().split('\n')[0]
            return ffmpeg_path
    except:
        pass

    # Fallback common paths (only for Windows)
    if os.name == 'nt':
        common_paths = [
            r"C:\ffmpeg\bin\ffmpeg.exe",
            r"C:\ffmpeg\ffmpeg-7.1.1-essentials_build\bin\ffmpeg.exe",
            r"C:\Program Files\ffmpeg\bin\ffmpeg.exe",
            r"C:\Program Files (x86)\ffmpeg\bin\ffmpeg.exe"
        ]

        for path in common_paths:
            if os.path.exists(path):
                return path

    return None


# Set FFmpeg path as environment variable before importing MoviePy
ffmpeg_path = find_ffmpeg()
if ffmpeg_path:
    os.environ['FFMPEG_BINARY'] = ffmpeg_path
    print(f"Using FFmpeg at: {ffmpeg_path}")
else:
    print("Warning: FFmpeg not found in system PATH or common locations")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Google Cloud TTS Client (lazy initialization)
tts_client = None


def get_tts_client():
    global tts_client
    if tts_client is None:
        try:
            tts_client = texttospeech.TextToSpeechClient()
        except Exception as e:
            logger.warning(
                f"Failed to initialize Google Cloud TTS client: {e}")
            tts_client = False
    return tts_client if tts_client is not False else None


# Initialize the app
app = FastAPI(title="QuikClips API",
              description="Backend API for QuikClips video processing")


@app.on_event("startup")
def startup_event():
    """
    Creates a single, shared ThreadPoolExecutor when the application starts.
    The number of workers can be configured. Using os.cpu_count() is a sensible default.
    """
    app.state.executor = ThreadPoolExecutor(max_workers=os.cpu_count())
    logger.info(f"ThreadPoolExecutor started with {os.cpu_count()} workers.")


@app.on_event("shutdown")
def shutdown_event():
    """
    Gracefully shuts down the ThreadPoolExecutor when the application stops.
    """
    app.state.executor.shutdown(wait=True)
    logger.info("ThreadPoolExecutor shut down.")

# Dependency to get the executor in endpoints


def get_executor():
    return app.state.executor


# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.mount("/static", StaticFiles(directory="static"), name="static")


# Create tables
Base.metadata.create_all(bind=engine)

# Directory for temporary files
TEMP_DIR = "temp"
os.makedirs(TEMP_DIR, exist_ok=True)

# Authentication configuration
SECRET_KEY = os.getenv("SECRET_KEY", "development_secret_key")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24  # 24 hours

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Initialize Cloudinary
cloudinary.config(
    cloud_name=os.getenv("CLOUDINARY_CLOUD_NAME"),
    api_key=os.getenv("CLOUDINARY_API_KEY"),
    api_secret=os.getenv("CLOUDINARY_API_SECRET")
)

# Initialize S3 storage if configured
s3_enabled = os.getenv("AWS_ACCESS_KEY_ID") and os.getenv(
    "AWS_SECRET_ACCESS_KEY")
if s3_enabled:
    storage.initialize_s3()

# --- Authentication Models ---


class Token(BaseModel):
    access_token: str
    token_type: str
    user_id: int
    username: str
    subscription: str


class TokenData(BaseModel):
    username: Optional[str] = None


class UserCreate(BaseModel):
    username: str
    email: str
    password: str


class UserProfile(BaseModel):
    username: str
    email: str
    avatar_url: Optional[str] = None
    bio: Optional[str] = None
    subscription: str = "free"

# --- Video Processing Models ---


class VideoSegment(BaseModel):
    start_time: float
    end_time: float
    text: str


class ViralityAnalysisResult(BaseModel):
    score: int
    feedback: str


class ProcessedVideo(BaseModel):
    segments: List[VideoSegment]
    video_urls: List[str]
    # virality_result: Optional[Dict[str, str]] = None
    virality_analysis: Optional[ViralityAnalysisResult] = None


class URLProcessRequest(BaseModel):
    url: str
    min_duration: float = 10.0
    max_duration: float = 60.0
    quality: str = "best"
    max_clips: Optional[int] = 10  # Default to 10 clips
    analyze_virality: Optional[bool] = True
    platform: Optional[str] = None
    subscription: Optional[str] = None
    credits: Optional[float] = 0.0  # Credits sent from frontend


class URLValidationResponse(BaseModel):
    valid: bool
    platform: Optional[str] = None
    video_id: Optional[str] = None
    error: Optional[str] = None


class VideoMetadataResponse(BaseModel):
    title: str
    duration: float
    uploader: str
    view_count: int
    platform: str
    thumbnail: str

# Advanced Video Processing Models


class AdvancedProcessingOptions(BaseModel):
    add_subtitles: bool = True
    add_emojis: bool = True
    add_clipart: bool = True
    create_short_form: bool = True
    platforms: List[str] = ["tiktok", "instagram"]
    subtitle_style: str = "modern"  # modern, tiktok, elegant
    max_short_clips: int = 3
    chunk_duration_seconds: Optional[float] = None
    clipmode: Optional[str] = None
    add_watermark: Optional[bool] = None
    credits: Optional[float] = 0.0


class AdvancedProcessingRequest(BaseModel):
    video_url: Optional[str] = None  # For URL processing
    options: AdvancedProcessingOptions = AdvancedProcessingOptions()
    # credits: Optional[float] = 0.0


class AdvancedProcessingResponse(BaseModel):
    success: bool
    message: str
    original_video: str
    processed_videos: Dict[str, str] = {}
    short_form_clips: Dict[str, List[str]] = {}
    metadata: Dict[str, Any] = {}
    processing_time: float = 0.0
    error: Optional[str] = None


class ProcessedClipDetail(BaseModel):
    url: str
    text: str
    start_time: float
    end_time: float
    virality_analysis: Optional[ViralityAnalysisResult] = None,
    platform: Optional[str] = None


class InstantProcessResponse(BaseModel):
    """Response for the instant processing endpoint."""
    success: bool
    message: str
    clips: List[ProcessedClipDetail] = []
    error: Optional[str] = None


class InstantProcessOptions(BaseModel):
    """Options for the one-click instant processing."""
    add_emojis: bool = False
    max_clips: int = 10
    subtitle_style: str = "clean_modern"
    platform: str = "tiktok"


class InstantProcessURLRequest(BaseModel):
    """Request model for instant processing from a URL."""
    url: str
    options: InstantProcessOptions = InstantProcessOptions()


class CompressionResponse(BaseModel):
    url: str


class InsufficientCreditsError(Exception):
    """Custom exception raised when credits are not enough for a video's duration."""
    pass


# --- Helper Functions ---

def check_credits_against_video_duration(credits_from_request: float, video_path: str):
    """
    Checks if the provided credits from the frontend are sufficient for the video's duration.
    This function has NO external dependencies (no DB, no Supabase).

    Raises:
        InsufficientCreditsError: If credits are not enough.
        Exception: If the video file cannot be read.
    """
    try:
        # Step 1: Get the video's duration in seconds by reading the file
        with VideoFileClip(video_path) as clip:
            seconds_required = clip.duration

        # Step 2: Convert the credits from the request into available seconds
        available_seconds = credits_from_request * 60

        logger.info(
            f"Credit Check: Video requires {seconds_required:.2f}s. Request claims {available_seconds:.2f}s are available.")

        # Step 3: Compare. If not enough, raise a specific error.
        if available_seconds < seconds_required:
            raise InsufficientCreditsError(
                f"Not enough credits. This video is {int(seconds_required)} seconds long, but your balance only covers {int(available_seconds)} seconds."
            )

        # If the function completes without an error, the check has passed.
        logger.info("Credit check passed.")

    except InsufficientCreditsError:
        raise  # Let the specific error pass through to be caught by the endpoint
    except Exception as e:
        logger.error(
            f"Could not read video file for credit check '{video_path}'. Error: {e}")
        # Raise a generic error if the file is unreadable
        raise Exception("Could not process video file to check duration.")


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password):
    return pwd_context.hash(password)


def get_user(db, username: str):
    return db.query(models.User).filter(models.User.username == username).first()


def authenticate_user(db, username: str, password: str):
    user = get_user(db, username)
    if not user or not verify_password(password, user.hashed_password):
        return False
    return user


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    user = get_user(db, username=token_data.username)
    if user is None:
        raise credentials_exception
    return user

# Initialize OpenAI client (lazy initialization)
openai_client = None


async def analyze_virality_with_openai(transcript: str) -> Optional[Dict[str, Any]]:
    prompt = (
        f"You are an encouraging and insightful social media strategist. Your goal is to analyze a video transcript, "
        f"identify its strengths, and provide constructive, positive feedback to boost its viral potential.\n\n"
        f"When providing a score, be optimistic. A score of 50-60 should indicate a solid foundation with potential, not a failure. "
        f"Scores below 40 should be reserved for content with fundamental issues, but the feedback must still be encouraging.\n\n"
        f"You MUST respond with a valid JSON object containing two keys: "
        f"1. 'score': An integer from 0 to 100, reflecting the content's potential with an optimistic lens. "
        f"2. 'feedback': A string containing a few bullet points. Start by highlighting what's already working well, "
        f"then suggest 'opportunities' or 'ideas to try' to make it even more engaging. Use markdown for bullets (e.g., '* Point 1\\n* Point 2'). "
        f"Do not include any text outside of the JSON object.\n\n"
        f"Transcript to Analyze:\n{transcript[:3000]}"
    )

    try:
        client = AsyncOpenAI()
        if not client:
            logger.warning("OpenAI client not available")
            return None

        response = await client.chat.completions.create(
            model="gpt-4-turbo-preview",
            messages=[
                # More explicit system prompt
                {"role": "system", "content": "You are a helpful assistant that analyzes text and responds ONLY with a valid, complete JSON object. Do not write any other text."},
                {"role": "user", "content": prompt}
            ],
            # INCREASED TOKENS to prevent cutoff
            max_tokens=400,
            temperature=0.4,
            response_format={"type": "json_object"},
        )

        result_string = response.choices[0].message.content.strip()
        logger.info(f"Raw virality analysis from OpenAI: '{result_string}'")

        try:
            parsed_data = json.loads(result_string)
            return parsed_data
        except json.JSONDecodeError:
            logger.error(f"Failed to decode JSON from OpenAI: {result_string}")
            return None

    except Exception as e:
        logger.error(f"OpenAI virality analysis API call failed: {e}")
        return None


# # --- Routes ---


@app.get("/")
async def root():
    return {"message": "SmartClips Backend API is running!", "status": "ok", "version": "1.0.0"}


@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "ffmpeg_available": os.path.exists(os.environ.get('FFMPEG_BINARY', 'ffmpeg')),
        "database": "connected",
        "services": {
            "openai": bool(os.getenv("OPENAI_API_KEY")),
            "cloudinary": bool(os.getenv("CLOUDINARY_API_KEY")),
            "elevenlabs": bool(os.getenv("ELEVENLABS_API_KEY"))
        }
    }


@app.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    user = authenticate_user(db, form_data.username, form_data.password)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user_id": user.id,
        "username": user.username,
        "subscription": user.subscription
    }


@app.post("/users/", response_model=UserProfile)
async def create_user(user: UserCreate, db: Session = Depends(get_db)):
    db_user = get_user(db, username=user.username)
    if db_user:
        raise HTTPException(
            status_code=400, detail="Username already registered")

    email_exists = db.query(models.User).filter(
        models.User.email == user.email).first()
    if email_exists:
        raise HTTPException(status_code=400, detail="Email already registered")

    hashed_password = get_password_hash(user.password)
    db_user = models.User(
        username=user.username,
        email=user.email,
        hashed_password=hashed_password,
        subscription="free"
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    return {
        "username": db_user.username,
        "email": db_user.email,
        "avatar_url": db_user.avatar_url,
        "bio": db_user.bio,
        "subscription": db_user.subscription
    }


@app.get("/users/me/", response_model=UserProfile)
async def read_users_me(current_user: models.User = Depends(get_current_user)):
    return {
        "username": current_user.username,
        "email": current_user.email,
        "avatar_url": current_user.avatar_url,
        "bio": current_user.bio,
        "subscription": current_user.subscription
    }


@app.post("/upload", response_model=ProcessedVideo)
async def upload_video(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    storage_type: str = Form("cloudinary"),
    min_duration: float = Form(10.0),
    max_duration: float = Form(60.0),
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Check subscription limits
    if current_user.subscription == "free" and db.query(models.Video).filter(models.Video.user_id == current_user.id).count() >= 5:
        raise HTTPException(
            status_code=402, detail="Free subscription limit reached (5 videos)")

    try:
        # Save the uploaded video temporarily
        temp_video_path = os.path.join(TEMP_DIR, file.filename)
        with open(temp_video_path, "wb") as temp_video:
            shutil.copyfileobj(file.file, temp_video)

        # Log video info
        try:
            with VideoFileClip(temp_video_path) as clip:
                duration = clip.duration
                resolution = f"{clip.size[0]}x{clip.size[1]}"
        except Exception as e:
            logger.error(f"Error getting video info: {str(e)}")
            duration = 0
            resolution = "unknown"

        # Create video record
        db_video = models.Video(
            user_id=current_user.id,
            filename=file.filename,
            original_path=temp_video_path,
            duration=duration,
            resolution=resolution,
            status="processing"
        )
        db.add(db_video)
        db.commit()
        db.refresh(db_video)

        # Process the video
        transcript, timestamps = video_processing.transcribe_video(
            temp_video_path, TEMP_DIR)
        segments = video_processing.segment_transcript(
            transcript,
            timestamps,
            min_duration=min_duration,
            max_duration=max_duration,
            refine_with_ai=current_user.subscription != "free"
        )
        clipped_videos = video_processing.clip_video_from_text(
            temp_video_path, segments, TEMP_DIR)

        # Upload to appropriate storage
        video_urls = []
        for clip_path in clipped_videos:
            if storage_type == "s3" and s3_enabled:
                url = storage.upload_to_s3(clip_path)
            else:
                url = storage.upload_to_cloudinary(clip_path)
            video_urls.append(url)

            # Create clip record with proper duration calculation
            try:
                with VideoFileClip(clip_path) as clip:
                    clip_duration = clip.duration
            except Exception as e:
                logger.error(
                    f"Error getting clip duration for {clip_path}: {str(e)}")
                clip_duration = 0

            db_clip = models.VideoClip(
                video_id=db_video.id,
                url=url,
                duration=clip_duration,
                status="completed"
            )
            db.add(db_clip)

        # Update video status
        db_video.status = "completed"
        db_video.processed_count = len(clipped_videos)
        db.commit()

        # Clean up in background
        def cleanup():
            try:
                os.remove(temp_video_path)
                for clip in clipped_videos:
                    if os.path.exists(clip):
                        os.remove(clip)
            except Exception as e:
                logger.error(f"Cleanup error: {str(e)}")

        background_tasks.add_task(cleanup)

        return {
            "segments": [
                {"start_time": s["start"],
                    "end_time": s["end"], "text": s["text"]}
                for s in segments
            ],
            "video_urls": video_urls
        }

    except Exception as e:
        logger.error(f"Upload error: {str(e)}")
        if 'db_video' in locals():
            db_video.status = "failed"
            db_video.error_message = str(e)
            db.commit()
        raise HTTPException(status_code=500, detail=str(e))


# --- GPT Editor Models ---

class GPTEditingRequest(BaseModel):
    """Request model for AI-powered video editing"""
    video_url: Optional[str] = None
    video_file: Optional[str] = None  # For uploaded files
    command: str  # Natural language editing command
    template: Optional[str] = None  # Optional template to apply
    options: Optional[Dict[str, Any]] = {}

class GPTEditingCommand(BaseModel):
    """Structured editing command parsed from natural language"""
    action: str  # e.g., "trim", "add_effects", "apply_template", "enhance_audio"
    parameters: Dict[str, Any]
    confidence: float
    description: str

class GPTEditingJob(BaseModel):
    """Job tracking model for GPT editing operations"""
    job_id: str
    status: str  # "pending", "processing", "completed", "failed"
    progress: float = 0.0
    command: str
    parsed_commands: List[GPTEditingCommand] = []
    result_url: Optional[str] = None
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime

class GPTEditingResponse(BaseModel):
    """Response model for GPT editing requests"""
    job_id: str
    status: str
    message: str
    parsed_commands: Optional[List[GPTEditingCommand]] = None
    estimated_time: Optional[int] = None  # in seconds

class GPTJobStatus(BaseModel):
    """Job status response model"""
    job_id: str
    status: str
    progress: float
    result_url: Optional[str] = None
    error_message: Optional[str] = None
    processing_log: List[str] = []

class VideoTemplate(BaseModel):
    """Video template configuration"""
    name: str
    description: str
    effects: List[Dict[str, Any]]
    style_config: Dict[str, Any]
    target_platforms: List[str] = ["tiktok", "instagram"]

# --- Facial AI Models ---

class FaceDetectionConfig(BaseModel):
    """Enhanced configuration for face detection and zoom effects"""
    zoom_level: float = 1.5
    transition_speed: float = 0.5
    detection_sensitivity: float = 0.7
    voice_threshold: float = 0.3
    padding: int = 50
    # Enhanced framing options
    include_torso: bool = True
    headroom_ratio: float = 0.15
    torso_ratio: float = 0.6
    # Dynamic switching options
    speaker_switch_threshold: float = 0.5
    transition_smoothness: float = 0.3
    speaker_memory_duration: float = 2.0


class FaceDetectionAnalyzeRequest(BaseModel):
    """Request model for face detection analysis"""
    video_url: Optional[str] = None
    config: Optional[FaceDetectionConfig] = FaceDetectionConfig()


class FaceDetectionProcessRequest(BaseModel):
    """Request model for face detection processing"""
    video_url: Optional[str] = None
    output_format: str = "mp4"
    config: Optional[FaceDetectionConfig] = FaceDetectionConfig()


class FaceDetectionPreviewRequest(BaseModel):
    """Request model for face detection preview"""
    video_url: Optional[str] = None
    config: Optional[FaceDetectionConfig] = FaceDetectionConfig()


class FaceDetectionResponse(BaseModel):
    """Response model for face detection operations"""
    success: bool
    message: str
    job_id: Optional[str] = None
    result_url: Optional[str] = None
    analysis_data: Optional[Dict[str, Any]] = None
    preview_data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class FaceDetectionStatusResponse(BaseModel):
    """Response model for face detection job status"""
    job_id: str
    status: str  # pending, processing, completed, failed
    progress: float  # 0.0 to 1.0
    result_url: Optional[str] = None
    error: Optional[str] = None
    created_at: str
    completed_at: Optional[str] = None


@app.post("/edit/trim")
async def trim_video(
    file: UploadFile = File(...),
    start_time: float = Form(...),
    end_time: float = Form(...),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Save the uploaded video temporarily
        temp_video_path = os.path.join(TEMP_DIR, file.filename)
        with open(temp_video_path, "wb") as temp_video:
            shutil.copyfileobj(file.file, temp_video)

        # Trim the video
        output_path = os.path.join(TEMP_DIR, f"trimmed_{file.filename}")
        video_processing.trim_video(
            temp_video_path, output_path, start_time, end_time)

        # Upload to Cloudinary
        url = storage.upload_to_cloudinary(output_path)

        # Cleanup
        os.remove(temp_video_path)
        os.remove(output_path)

        return {"url": url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/edit/speed")
async def adjust_speed(
    file: UploadFile = File(...),
    speed_factor: float = Form(...),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Save the uploaded video temporarily
        temp_video_path = os.path.join(TEMP_DIR, file.filename)
        with open(temp_video_path, "wb") as temp_video:
            shutil.copyfileobj(file.file, temp_video)

        # Adjust speed
        output_path = os.path.join(TEMP_DIR, f"speed_{file.filename}")
        video_processing.adjust_speed(
            temp_video_path, output_path, speed_factor)

        # Upload to Cloudinary
        url = storage.upload_to_cloudinary(output_path)

        # Cleanup
        os.remove(temp_video_path)
        os.remove(output_path)

        return {"url": url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/edit/crop")
async def crop_video(
    file: UploadFile = File(...),
    x1: int = Form(...),
    y1: int = Form(...),
    x2: int = Form(...),
    y2: int = Form(...),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Save the uploaded video temporarily
        temp_video_path = os.path.join(TEMP_DIR, file.filename)
        with open(temp_video_path, "wb") as temp_video:
            shutil.copyfileobj(file.file, temp_video)

        # Crop the video
        output_path = os.path.join(TEMP_DIR, f"cropped_{file.filename}")
        video_processing.crop_video(
            temp_video_path, output_path, x1, y1, x2, y2)

        # Upload to Cloudinary
        url = storage.upload_to_cloudinary(output_path)

        # Cleanup
        os.remove(temp_video_path)
        os.remove(output_path)

        return {"url": url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/edit/rotate")
async def rotate_video(
    file: UploadFile = File(...),
    angle: int = Form(...),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Save the uploaded video temporarily
        temp_video_path = os.path.join(TEMP_DIR, file.filename)
        with open(temp_video_path, "wb") as temp_video:
            shutil.copyfileobj(file.file, temp_video)

        # Rotate the video
        output_path = os.path.join(TEMP_DIR, f"rotated_{file.filename}")
        video_processing.rotate_video(temp_video_path, output_path, angle)

        # Upload to Cloudinary
        url = storage.upload_to_cloudinary(output_path)

        # Cleanup
        os.remove(temp_video_path)
        os.remove(output_path)

        return {"url": url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/edit/merge")
async def merge_videos(
    files: List[UploadFile] = File(...),
    current_user: models.User = Depends(get_current_user)
):
    try:
        temp_paths = []
        for file in files:
            temp_path = os.path.join(TEMP_DIR, file.filename)
            with open(temp_path, "wb") as temp_video:
                shutil.copyfileobj(file.file, temp_video)
            temp_paths.append(temp_path)

        # Merge the videos
        output_path = os.path.join(
            TEMP_DIR, f"merged_{datetime.now().timestamp()}.mp4")
        video_processing.merge_videos(temp_paths, output_path)

        # Upload to Cloudinary
        url = storage.upload_to_cloudinary(output_path)

        # Cleanup
        for path in temp_paths:
            os.remove(path)
        os.remove(output_path)

        return {"url": url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/videos/")
async def list_videos(
    skip: int = 0,
    limit: int = 20,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    videos = db.query(models.Video).filter(
        models.Video.user_id == current_user.id
    ).offset(skip).limit(limit).all()

    result = []
    for video in videos:
        clips = db.query(models.VideoClip).filter(
            models.VideoClip.video_id == video.id).all()
        result.append({
            "id": video.id,
            "filename": video.filename,
            "duration": video.duration,
            "created_at": video.created_at,
            "status": video.status,
            "clips": [{"id": clip.id, "url": clip.url, "duration": clip.duration} for clip in clips]
        })

    return result


@app.get("/user/clips")
async def get_user_clips(
    skip: int = 0,
    limit: int = 20,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's clips with enhanced metadata for dashboard"""
    clips = db.query(models.VideoClip).join(models.Video).filter(
        models.Video.user_id == current_user.id
    ).offset(skip).limit(limit).all()

    result = []
    for clip in clips:
        # Get video metadata
        video = db.query(models.Video).filter(
            models.Video.id == clip.video_id).first()

        result.append({
            "id": clip.id,
            "url": clip.url,
            "title": f"{video.filename} - Clip {clip.id}" if video else f"Clip {clip.id}",
            "thumbnail": clip.url,  # TODO: Generate actual thumbnails
            "duration": clip.duration,
            "views": 0,  # TODO: Implement view tracking
            "likes": 0,  # TODO: Implement like tracking
            "comments": 0,  # TODO: Implement comment tracking
            "vitalityScore": 75,  # TODO: Calculate actual virality score
            "status": "published",  # TODO: Implement status tracking
            "createdAt": clip.created_at.strftime("%Y-%m-%d") if clip.created_at else None,
            "platform": "SmartClips",  # TODO: Track actual platform
            "videoId": clip.video_id
        })

    return result


@app.get("/user/stats")
async def get_user_stats(
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user statistics for dashboard"""
    # Count user's videos and clips
    video_count = db.query(models.Video).filter(
        models.Video.user_id == current_user.id).count()
    clip_count = db.query(models.VideoClip).join(models.Video).filter(
        models.Video.user_id == current_user.id
    ).count()

    # TODO: Implement actual view tracking, subscriber tracking, etc.
    return {
        "totalViews": clip_count * 1500,  # Mock calculation
        "totalVideos": video_count,
        "totalClips": clip_count,
        "totalSubscribers": 0,  # TODO: Implement subscriber tracking
        "watchTime": clip_count * 45,  # Mock calculation in minutes
        "credits": current_user.credits or 0
    }


@app.get("/user/social-platforms")
async def get_user_social_platforms(
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's connected social platforms"""
    # TODO: Implement actual social platform connection tracking
    # For now, return mock data based on user activity

    video_count = db.query(models.Video).filter(
        models.Video.user_id == current_user.id).count()

    return [
        {
            "name": "YouTube",
            "icon": "🎥",
            "connected": video_count > 0,  # Mock: connected if user has videos
            "followers": "12.5K" if video_count > 0 else "0",
            "profileUrl": "#",
            "color": "text-red-500",
            "bgColor": "bg-red-50 dark:bg-red-900/20"
        },
        {
            "name": "TikTok",
            "icon": "🎵",
            "connected": video_count > 2,  # Mock: connected if user has multiple videos
            "followers": "8.9K" if video_count > 2 else "0",
            "profileUrl": "#",
            "color": "text-black dark:text-white",
            "bgColor": "bg-gray-50 dark:bg-gray-900/20"
        },
        {
            "name": "Instagram",
            "icon": "📸",
            "connected": False,  # Mock: not connected
            "followers": "0",
            "profileUrl": "#",
            "color": "text-pink-500",
            "bgColor": "bg-pink-50 dark:bg-pink-900/20"
        },
        {
            "name": "Twitter",
            "icon": "🐦",
            "connected": False,  # Mock: not connected
            "followers": "0",
            "profileUrl": "#",
            "color": "text-blue-400",
            "bgColor": "bg-blue-50 dark:bg-blue-900/20"
        }
    ]


@app.get("/user/video-count")
async def get_user_video_count(
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's video and clip counts"""
    video_count = db.query(models.Video).filter(
        models.Video.user_id == current_user.id).count()
    clip_count = db.query(models.VideoClip).join(models.Video).filter(
        models.Video.user_id == current_user.id
    ).count()

    return {
        "videos": video_count,
        "clips": clip_count
    }


# --- Manual Editor API Endpoints ---

class EditorProject(BaseModel):
    id: Optional[str] = None
    name: str
    description: Optional[str] = None
    timeline_data: Dict[str, Any]
    clips: List[Dict[str, Any]]
    text_overlays: List[Dict[str, Any]] = []
    effects: List[Dict[str, Any]] = []
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


class TimelineClip(BaseModel):
    id: str
    start: float
    end: float
    duration: float
    url: str
    title: str
    track: int


class TextOverlay(BaseModel):
    id: str
    text: str
    x: float
    y: float
    fontSize: int
    color: str
    startTime: float
    endTime: float


@app.post("/editor/projects")
async def create_editor_project(
    project: EditorProject,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new editor project"""
    try:
        # TODO: Implement actual database storage for editor projects
        # For now, return mock response
        project_id = f"project_{int(datetime.now().timestamp())}"

        return {
            "id": project_id,
            "name": project.name,
            "description": project.description,
            "timeline_data": project.timeline_data,
            "clips": project.clips,
            "text_overlays": project.text_overlays,
            "effects": project.effects,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "user_id": current_user.id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/editor/projects")
async def get_editor_projects(
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all editor projects for the current user"""
    try:
        # TODO: Implement actual database query for editor projects
        # For now, return mock data
        return [
            {
                "id": "project_1",
                "name": "My First Project",
                "description": "A sample video project",
                "created_at": "2024-01-15T10:00:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "clip_count": 3,
                "duration": 45.5
            }
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/editor/projects/{project_id}")
async def get_editor_project(
    project_id: str,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific editor project"""
    try:
        # TODO: Implement actual database query for specific project
        # For now, return mock data
        return {
            "id": project_id,
            "name": "My Video Project",
            "description": "A comprehensive video editing project",
            "timeline_data": {
                "duration": 60.0,
                "tracks": 2
            },
            "clips": [
                {
                    "id": "clip_1",
                    "start": 0,
                    "end": 30,
                    "duration": 30,
                    "url": "https://example.com/video1.mp4",
                    "title": "Intro Clip",
                    "track": 0
                }
            ],
            "text_overlays": [],
            "effects": [],
            "created_at": "2024-01-15T10:00:00Z",
            "updated_at": "2024-01-15T10:30:00Z"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.put("/editor/projects/{project_id}")
async def update_editor_project(
    project_id: str,
    project: EditorProject,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update an existing editor project"""
    try:
        # TODO: Implement actual database update for editor projects
        # For now, return mock response
        return {
            "id": project_id,
            "name": project.name,
            "description": project.description,
            "timeline_data": project.timeline_data,
            "clips": project.clips,
            "text_overlays": project.text_overlays,
            "effects": project.effects,
            "updated_at": datetime.now().isoformat(),
            "user_id": current_user.id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/editor/projects/{project_id}")
async def delete_editor_project(
    project_id: str,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete an editor project"""
    try:
        # TODO: Implement actual database deletion for editor projects
        return {"message": "Project deleted successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/editor/projects/{project_id}/save")
async def save_editor_project(
    project_id: str,
    save_data: Dict[str, Any],
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Save editor project state"""
    try:
        # TODO: Implement actual save functionality
        # This would save the current timeline state, clips, overlays, etc.

        return {
            "message": "Project saved successfully",
            "project_id": project_id,
            "saved_at": datetime.now().isoformat(),
            "version": save_data.get("version", 1)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/editor/projects/{project_id}/export")
async def export_editor_project(
    project_id: str,
    export_options: Dict[str, Any],
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Export editor project to video"""
    try:
        # TODO: Implement actual video export functionality
        # This would render the timeline into a final video

        # Mock export process
        export_id = f"export_{int(datetime.now().timestamp())}"

        return {
            "export_id": export_id,
            "status": "processing",
            "message": "Export started successfully",
            "estimated_time": "2-5 minutes",
            "export_options": export_options
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/editor/exports/{export_id}")
async def get_export_status(
    export_id: str,
    current_user: models.User = Depends(get_current_user)
):
    """Get export status"""
    try:
        # TODO: Implement actual export status tracking
        # For now, return mock status
        return {
            "export_id": export_id,
            "status": "completed",
            "progress": 100,
            "download_url": "https://example.com/exported_video.mp4",
            "file_size": "15.2 MB",
            "duration": "45.5s"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# --- Timeline Operations ---

@app.post("/editor/projects/{project_id}/clips")
async def add_clip_to_timeline(
    project_id: str,
    clip_data: TimelineClip,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Add a clip to the timeline"""
    try:
        # TODO: Implement actual clip addition to timeline
        return {
            "message": "Clip added to timeline",
            "clip": {
                "id": clip_data.id,
                "start": clip_data.start,
                "end": clip_data.end,
                "duration": clip_data.duration,
                "url": clip_data.url,
                "title": clip_data.title,
                "track": clip_data.track
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.put("/editor/projects/{project_id}/clips/{clip_id}")
async def update_timeline_clip(
    project_id: str,
    clip_id: str,
    clip_updates: Dict[str, Any],
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a clip on the timeline"""
    try:
        # TODO: Implement actual clip update functionality
        return {
            "message": "Clip updated successfully",
            "clip_id": clip_id,
            "updates": clip_updates,
            "updated_at": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/editor/projects/{project_id}/clips/{clip_id}")
async def delete_timeline_clip(
    project_id: str,
    clip_id: str,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a clip from the timeline"""
    try:
        # TODO: Implement actual clip deletion
        return {
            "message": "Clip deleted from timeline",
            "clip_id": clip_id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/editor/projects/{project_id}/clips/{clip_id}/split")
async def split_timeline_clip(
    project_id: str,
    clip_id: str,
    split_time: float,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Split a clip at the specified time"""
    try:
        # TODO: Implement actual clip splitting
        new_clip_id = f"{clip_id}_split_{int(datetime.now().timestamp())}"

        return {
            "message": "Clip split successfully",
            "original_clip_id": clip_id,
            "new_clip_id": new_clip_id,
            "split_time": split_time
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/editor/projects/{project_id}/clips/{clip_id}/duplicate")
async def duplicate_timeline_clip(
    project_id: str,
    clip_id: str,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Duplicate a clip on the timeline"""
    try:
        # TODO: Implement actual clip duplication
        new_clip_id = f"{clip_id}_dup_{int(datetime.now().timestamp())}"

        return {
            "message": "Clip duplicated successfully",
            "original_clip_id": clip_id,
            "new_clip_id": new_clip_id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# --- Text Overlays and Effects ---

@app.post("/editor/projects/{project_id}/text-overlays")
async def add_text_overlay(
    project_id: str,
    overlay_data: TextOverlay,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Add a text overlay to the project"""
    try:
        # TODO: Implement actual text overlay addition
        return {
            "message": "Text overlay added",
            "overlay": {
                "id": overlay_data.id,
                "text": overlay_data.text,
                "x": overlay_data.x,
                "y": overlay_data.y,
                "fontSize": overlay_data.fontSize,
                "color": overlay_data.color,
                "startTime": overlay_data.startTime,
                "endTime": overlay_data.endTime
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.put("/editor/projects/{project_id}/text-overlays/{overlay_id}")
async def update_text_overlay(
    project_id: str,
    overlay_id: str,
    overlay_updates: Dict[str, Any],
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a text overlay"""
    try:
        # TODO: Implement actual text overlay update
        return {
            "message": "Text overlay updated",
            "overlay_id": overlay_id,
            "updates": overlay_updates
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/editor/projects/{project_id}/text-overlays/{overlay_id}")
async def delete_text_overlay(
    project_id: str,
    overlay_id: str,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a text overlay"""
    try:
        # TODO: Implement actual text overlay deletion
        return {
            "message": "Text overlay deleted",
            "overlay_id": overlay_id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/editor/projects/{project_id}/effects")
async def add_effect(
    project_id: str,
    effect_data: Dict[str, Any],
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Add an effect to the project"""
    try:
        # TODO: Implement actual effect addition
        effect_id = f"effect_{int(datetime.now().timestamp())}"

        return {
            "message": "Effect added",
            "effect": {
                "id": effect_id,
                "type": effect_data.get("type"),
                "parameters": effect_data.get("parameters", {}),
                "startTime": effect_data.get("startTime"),
                "endTime": effect_data.get("endTime"),
                "target": effect_data.get("target")  # clip_id or "global"
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/editor/effects/templates")
async def get_effect_templates(
    current_user: models.User = Depends(get_current_user)
):
    """Get available effect templates"""
    try:
        # TODO: Implement actual effect templates from database
        return [
            {
                "id": "fade_in",
                "name": "Fade In",
                "description": "Gradually fade in the video",
                "category": "transition",
                "parameters": {
                    "duration": {"type": "number", "default": 1.0, "min": 0.1, "max": 5.0}
                }
            },
            {
                "id": "fade_out",
                "name": "Fade Out",
                "description": "Gradually fade out the video",
                "category": "transition",
                "parameters": {
                    "duration": {"type": "number", "default": 1.0, "min": 0.1, "max": 5.0}
                }
            },
            {
                "id": "blur",
                "name": "Blur",
                "description": "Apply blur effect",
                "category": "filter",
                "parameters": {
                    "intensity": {"type": "number", "default": 5, "min": 1, "max": 20}
                }
            },
            {
                "id": "brightness",
                "name": "Brightness",
                "description": "Adjust video brightness",
                "category": "color",
                "parameters": {
                    "value": {"type": "number", "default": 1.0, "min": 0.1, "max": 3.0}
                }
            }
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/videos/{video_id}")
async def delete_video(
    video_id: int,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    video = db.query(models.Video).filter(
        models.Video.id == video_id,
        models.Video.user_id == current_user.id
    ).first()

    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    # Delete associated clips
    clips = db.query(models.VideoClip).filter(
        models.VideoClip.video_id == video.id).all()
    for clip in clips:
        db.delete(clip)

    # Delete video record
    db.delete(video)
    db.commit()

    return {"detail": "Video deleted successfully"}

# --- URL Processing Endpoints ---


class URLRequest(BaseModel):
    url: str


@app.post("/validate-url", response_model=URLValidationResponse)
async def validate_video_url(request: URLRequest):
    """Validate if URL is from a supported platform - Public endpoint"""
    try:
        processor = url_processor.URLVideoProcessor()
        validation = processor.validate_url(request.url)

        return URLValidationResponse(
            valid=validation['valid'],
            platform=validation.get('platform'),
            video_id=validation.get('video_id'),
            error=validation.get('error')
        )
    except Exception as e:
        logger.error(f"URL validation error: {str(e)}")
        return URLValidationResponse(
            valid=False,
            error=str(e)
        )


@app.post("/url-metadata")
async def get_url_metadata(request: URLRequest):
    """Get video metadata from URL without downloading - Public endpoint"""
    try:
        processor = url_processor.URLVideoProcessor()
        metadata = processor.get_video_metadata(request.url)

        if 'error' in metadata:
            raise HTTPException(status_code=400, detail=metadata['error'])

        return metadata
    except Exception as e:
        logger.error(f"Metadata extraction error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Optional authentication dependency


async def get_current_user_optional(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    """Get current user if authenticated, otherwise return None"""
    if not token:
        return None
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            return None
        user = get_user(db, username=username)
        return user
    except JWTError:
        return None


class URLProcessResponse(BaseModel):
    clips: List[ProcessedClipDetail]


def group_words_into_segments(
    word_segments: List[Dict[str, Any]],
    min_duration: float,
    max_duration: float
) -> List[Dict[str, Any]]:
    """
    CORRECTED: Groups word-level segments into larger, coherent segments.
    This version fixes the bug where the last segment of the video was dropped.
    """
    if not word_segments:
        return []

    final_segments = []
    current_segment_words = []
    # Initialize segment_start_time safely
    segment_start_time = word_segments[0]['start']

    logger.info(f"Starting segment grouping with {len(word_segments)} words")

    for i, word in enumerate(word_segments):
        current_segment_words.append(word['text'])
        current_duration = word['end'] - segment_start_time

        is_sentence_end = word['text'].rstrip().endswith(('.', '!', '?'))
        is_long_pause = (i + 1 < len(word_segments) and
                         word_segments[i + 1]['start'] - word['end'] > 1.0)

        # Determine if we should end the segment based on clear, non-overlapping rules
        should_end_segment = False
        if current_duration >= max_duration:
            should_end_segment = True
        elif current_duration >= min_duration and (is_sentence_end or is_long_pause or _is_engaging_content(" ".join(current_segment_words))):
            should_end_segment = True

        if should_end_segment:
            segment_text = " ".join(current_segment_words)
            final_segments.append({
                'text': segment_text,
                'start': segment_start_time,
                'end': word['end']
            })

            # Reset for the next segment
            current_segment_words = []
            if i + 1 < len(word_segments):
                segment_start_time = word_segments[i + 1]['start']
            # If it was the last word that completed the segment, current_segment_words will be empty, and the loop will end.

    if current_segment_words:
        last_word_end_time = word_segments[-1]['end']
        final_segment_duration = last_word_end_time - segment_start_time

        # Add the final segment only if it meets the minimum duration requirement.
        if final_segment_duration >= min_duration:
            segment_text = " ".join(current_segment_words)
            final_segments.append({
                'text': segment_text,
                'start': segment_start_time,
                'end': last_word_end_time
            })
            logger.info(
                f"Added final leftover segment of {final_segment_duration:.2f}s.")
    # --- END OF THE FIX ---

    return final_segments


def _is_engaging_content(text: str) -> bool:
    """Identify engaging content that works well in shorter clips"""
    engaging_indicators = [
        # Emotional triggers
        '!', '?', 'wow', 'amazing', 'incredible', 'shocking', 'unbelievable',
        # Educational hooks
        'secret', 'trick', 'hack', 'tip', 'mistake', 'wrong', 'right',
        # Superlatives
        'best', 'worst', 'first', 'last', 'only', 'never', 'always',
        # Action words
        'watch', 'look', 'see', 'check', 'try', 'do', 'make',
        # Controversy/debate
        'controversial', 'debate', 'argue', 'disagree', 'opinion'
    ]

    text_lower = text.lower()

    # Count engaging indicators
    indicator_count = sum(
        1 for indicator in engaging_indicators if indicator in text_lower)

    # Also check for questions and exclamations
    question_count = text.count('?')
    exclamation_count = text.count('!')

    # Content is engaging if it has multiple indicators or strong emotional markers
    return (indicator_count >= 2 or question_count >= 1 or exclamation_count >= 2)


@app.post("/process-instant", response_model=InstantProcessResponse)
async def process_instant_video_from_url(
    request: InstantProcessURLRequest,
    background_tasks: BackgroundTasks,
    current_user: Optional[models.User] = Depends(get_current_user_optional),
    db: Session = Depends(get_db)
):
    """
    Handles the "one-click" video processing flow FROM A URL.
    1. Downloads video from the URL.
    2. Generates short clips.
    3. Adds mandatory subtitles and optional emojis.
    4. Uploads final clips and saves records.
    """
    # --- Step 1: Setup Paths and Download Video from URL ---
    start_time = time.time()
    url_processor_instance = url_processor.URLVideoProcessor(TEMP_DIR)
    output_dir = os.path.join(TEMP_DIR, f"instant_output_{int(start_time)}")
    os.makedirs(output_dir, exist_ok=True)

    if current_user and current_user.subscription == "free" and db.query(models.Video).filter(models.Video.user_id == current_user.id).count() >= 5:
        raise HTTPException(
            status_code=402, detail="Free subscription limit reached (5 videos)")

    paths_to_cleanup = [output_dir]

    try:
        logger.info(f"Downloading video from URL: {request.url}")
        # Download the video file and get its local path
        video_path, download_metadata = url_processor_instance.download_video(
            request.url)
        if not video_path or not os.path.exists(video_path):
            raise ValueError(
                f"Failed to download video from URL. Metadata: {download_metadata}")

        # Add downloaded file to cleanup list
        paths_to_cleanup.append(video_path)

        video_filename = os.path.basename(video_path)
        logger.info(f"Successfully downloaded video to {video_path}")

        # --- Step 2: Create Main Video Record in Database ---
        if current_user:
            db_video = models.Video(
                user_id=current_user.id,
                filename=video_filename,
                original_path=request.url,
                status="processing"
            )
            db.add(db_video)
            db.commit()
            db.refresh(db_video)

        # --- Step 3: Call the Main Processing Function ---
        process_options = request.options
        logger.info(
            f"Calling process_video_with_enhancements with options: {process_options.dict()}")

        enhancement_options = {
            'add_subtitles': True,
            'add_emojis': process_options.add_emojis,
            'create_short_form': True,
            'platforms': [process_options.platform],
            'subtitle_style': process_options.subtitle_style,
            'max_short_clips': process_options.max_clips,
            'use_ai_features': True
        }

        # Use the downloaded local file path for processing
        results = process_video_with_enhancements(
            video_path=video_path,
            output_dir=output_dir,
            openai_api_key=os.getenv("OPENAI_API_KEY"),
            options=enhancement_options
        )

        if results.get('error'):
            raise ValueError(f"Video processing failed: {results['error']}")

        # --- Step 4: Upload Results and Save to Database ---
        final_clip_details = []
        processed_clips_map = results.get('short_form_clips', {})

        for platform, clip_paths in processed_clips_map.items():
            for clip_path in clip_paths:
                if os.path.exists(clip_path):
                    url = storage.upload_to_cloudinary(clip_path)
                    with VideoFileClip(clip_path) as clip:
                        duration = clip.duration

                    text_for_clip = f"Clip from {video_filename}"

                    if current_user:
                        db_clip = models.VideoClip(
                            video_id=db_video.id, url=url, duration=duration, status="completed")
                        db.add(db_clip)
                        db.commit()

                    final_clip_details.append(ProcessedClipDetail(
                        url=url,
                        text=text_for_clip,
                        start_time=0,
                        end_time=duration,
                        platform=platform
                    ))

        # --- Step 5: Finalize and Cleanup ---
        db_video.status = "completed"
        db_video.processed_count = len(final_clip_details)
        db.commit()

        def cleanup_files():
            for path in paths_to_cleanup:
                try:
                    if os.path.isfile(path):
                        os.remove(path)
                    elif os.path.isdir(path):
                        shutil.rmtree(path)
                except Exception as e:
                    logger.error(f"Error during cleanup of {path}: {e}")

        background_tasks.add_task(cleanup_files)

        logger.info(
            f"Instant processing completed in {time.time() - start_time:.2f} seconds.")
        return InstantProcessResponse(
            success=True,
            message=f"Successfully processed and created {len(final_clip_details)} clips from URL.",
            clips=final_clip_details
        )

    except Exception as e:
        logger.error(f"Error in /process-instant endpoint: {e}", exc_info=True)
        # Cleanup logic here as well
        raise HTTPException(status_code=500, detail=str(e))


# @app.post("/process-url", response_model=URLProcessResponse)
# async def process_video_url(
#     request: URLProcessRequest,
#     background_tasks: BackgroundTasks,
#     current_user: Optional[models.User] = Depends(get_current_user_optional),
#     db: Session = Depends(get_db)
# ):
#     """Download and process video from URL - Works with or without authentication"""
#     # Check subscription limits only if user is authenticated
#     if current_user and current_user.subscription == "free" and db.query(models.Video).filter(models.Video.user_id == current_user.id).count() >= 5:
#         raise HTTPException(
#             status_code=402, detail="Free subscription limit reached (5 videos)")

#     paths_to_cleanup = []
#     try:
#         logger.info(f"Request: {request}")
#         print(f"Request: {request}")
#         # Validate URL first
#         processor = url_processor.URLVideoProcessor(TEMP_DIR)
#         validation = processor.validate_url(request.url)

#         if not validation['valid']:
#             raise HTTPException(status_code=400, detail=validation['error'])
#         request.analyze_virality = True

#         # Download video
#         logger.info(f"Processing URL: {request.url}")
#         print(f"Processing URL: {request.url}")
#         video_path, download_metadata = processor.download_video(
#             request.url, request.quality)

#         paths_to_cleanup.append(video_path)

#         with VideoFileClip(video_path) as clip:
#             video_duration = clip.duration
#         logger.info(f"Actual video duration is: {video_duration:.2f} seconds.")

#         processing_video_path = video_path
#         if request.subscription == 'free':
#             logger.info(
#                 "Payload indicates 'free' subscription. Applying watermark to the main downloaded video.")

#             # Define a new path for the watermarked video.
#             watermarked_path = os.path.join(
#                 TEMP_DIR, f"wm_main_{os.path.basename(video_path)}")

#             # Apply the watermark filter.
#             video_processing.add_watermark(
#                 video_path, watermarked_path, "SmartClips.io")

#             # All subsequent processing will use the watermarked version.
#             processing_video_path = watermarked_path
#             paths_to_cleanup.append(watermarked_path)
#             logger.info(f"Watermarked video created at: {watermarked_path}")

#         # Create video record only if user is authenticated
#         db_video = None
#         if current_user:
#             db_video = models.Video(
#                 user_id=current_user.id,
#                 filename=f"url_{download_metadata['platform']}_{download_metadata['video_id']}.mp4",
#                 original_path=video_path,
#                 duration=download_metadata.get('duration', 0),
#                 resolution=download_metadata.get('resolution', 'unknown'),
#                 status="processing"
#             )
#             db.add(db_video)
#             db.commit()
#             db.refresh(db_video)

#         # Process the video using existing pipeline
#         transcript, timestamps = video_processing.transcribe_video(
#             processing_video_path, TEMP_DIR)

#         word_level_segments = video_processing.segment_transcript(
#             transcript,
#             timestamps,
#             min_duration=request.min_duration,
#             max_duration=request.max_duration,
#             refine_with_ai=current_user.subscription != "free" if current_user else False
#         )
#         print(f"Word-level segments: {word_level_segments}")
#         logger.info(f"Word-level segments: {word_level_segments}")

#         segments = group_words_into_segments(
#             word_level_segments,
#             min_duration=request.min_duration,
#             max_duration=request.max_duration
#         )

#         print(f"Segments created: {segments}")
#         logger.info(f"Segments created: {segments}")

#         valid_segments = []
#         for s in segments:
#             # Only keep segments that start before the video is over
#             if s['start'] < video_duration:
#                 # IMPORTANT: Trim the end time to not exceed the video's actual duration
#                 s['end'] = min(s['end'], video_duration)
#                 valid_segments.append(s)

#         # Overwrite the old, potentially invalid segments with our clean list
#         segments = valid_segments
#         print(f"Validated segments: {segments}")

#         logger.info(
#             f"Validated {len(segments)} segments to be within video duration.")

#         if request.max_clips:
#             segments = segments[:request.max_clips]

#         logger.info(f"Created {len(segments)} segments from transcript.")

#     # --- Step 4: Analyze each segment's text CONCURRENTLY ---
#         analysis_results = []
#         if request.analyze_virality and segments:
#             logger.info(
#                 f"Analyzing virality for {len(segments)} segments in parallel...")
#             # Create a list of analysis tasks, one for each segment's text
#             analysis_tasks = [analyze_virality_with_openai(
#                 s['text']) for s in segments]
#             # Run all API calls at the same time and wait for them all to complete
#             analysis_results = await asyncio.gather(*analysis_tasks)
#         else:
#             # If not analyzing, create a list of Nones to match the number of segments
#             analysis_results = [None] * len(segments)

#         # --- Step 5: Create local video clips ---
#         clipped_videos_paths = video_processing.clip_video_from_text(
#             processing_video_path, segments, TEMP_DIR
#         )

#         # --- Step 6: Loop through results, upload, and build final response object ---
#         final_clips_details: List[ProcessedClipDetail] = []

#         # Zip combines the corresponding items from each list into tuples
#         for segment, clip_path, analysis_data in zip(segments, clipped_videos_paths, analysis_results):
#             # Upload the physical clip to get its public URL
#             url = storage.upload_to_cloudinary(clip_path)

#             # Create a ViralityAnalysisResult object from the dictionary if it exists
#             virality_obj = ViralityAnalysisResult(
#                 **analysis_data) if analysis_data else None

#             # Combine all information for this one clip

#             clip_duration = segment['end'] - segment['start']

#             final_clips_details.append(
#                 ProcessedClipDetail(
#                     url=url,
#                     text=segment['text'],
#                     start_time=0.0,
#                     end_time=clip_duration,
#                     virality_analysis=virality_obj,
#                     platform=validation.get('platform', 'unknown')
#                 )
#             )

#         # --- Step 7: Cleanup (No change) ---

#         def cleanup():
#             try:
#                 processor.cleanup_downloaded_file(video_path)
#                 for clip_path in clipped_videos_paths:
#                     if os.path.exists(clip_path):
#                         os.remove(clip_path)
#             except Exception as e:
#                 logger.error(f"Cleanup error: {str(e)}")
#         background_tasks.add_task(cleanup)

#         print(f"Final clips details: {final_clips_details}")
#         logging.info(f"Final clips details: {final_clips_details}")

#         # --- Step 8: Return the final, structured response ---
#         return URLProcessResponse(clips=final_clips_details)

#     except Exception as e:
#         logger.error(f"URL processing error: {e}", exc_info=True)
#         # Your DB error handling can go here
#         raise HTTPException(status_code=500, detail=str(e))

def run_processing_with_original_logic(
    request_data: dict,
    user_id: Optional[int],
    user_subscription: Optional[str]
) -> List[ProcessedClipDetail]:
    """
    This worker function contains the exact logic from the original, commented-out endpoint.
    It manages its own database session and file cleanup.
    """
    # Create a new, independent database session for this thread
    db: Session = SessionLocal()
    paths_to_cleanup = []

    # Recreate the request object from the passed data
    request = URLProcessRequest(**request_data)
    processor = url_processor.URLVideoProcessor(TEMP_DIR)

    try:
        # --- LOGIC BLOCK 1: Download and Watermark ---
        # This logic is identical to the original code.
        logger.info(f"Worker thread processing URL: {request.url}")
        video_path, download_metadata = processor.download_video(
            request.url, request.quality)
        paths_to_cleanup.append(video_path)

        # This will raise InsufficientCreditsError if the check fails, stopping the worker
        check_credits_against_video_duration(
            credits_from_request=request.credits,
            video_path=video_path
        )

        with VideoFileClip(video_path) as clip:
            video_duration = clip.duration

        processing_video_path = video_path
        # LOGIC PRESERVED: Watermark check uses the 'subscription' from the request body.
        if request.subscription == 'free':
            logger.info(
                "Payload indicates 'free' subscription. Applying watermark.")
            watermarked_path = os.path.join(
                TEMP_DIR, f"wm_main_{os.path.basename(video_path)}")
            video_processing.add_watermark(
                video_path, watermarked_path, "SmartClips.io")
            processing_video_path = watermarked_path
            paths_to_cleanup.append(watermarked_path)

        # --- LOGIC BLOCK 2: Create Main DB Record ---
        # This logic is identical to the original code.
        db_video = None
        if user_id:
            db_video = models.Video(
                user_id=user_id,
                filename=f"url_{download_metadata['platform']}_{download_metadata['video_id']}.mp4",
                original_path=video_path,
                duration=download_metadata.get('duration', 0),
                resolution=download_metadata.get('resolution', 'unknown'),
                status="processing"
            )
            db.add(db_video)
            db.commit()
            db.refresh(db_video)

        # --- LOGIC BLOCK 3: Transcribe and Segment ---
        # This logic is identical to the original code.
        transcript, timestamps = video_processing.transcribe_video(
            processing_video_path, TEMP_DIR)

        # LOGIC PRESERVED: AI Refinement check uses the user's actual subscription from the DB.
        word_level_segments = video_processing.segment_transcript(
            transcript, timestamps,
            min_duration=request.min_duration,
            max_duration=request.max_duration,
            refine_with_ai=(user_subscription !=
                            "free" if user_subscription is not None else False)
        )
        segments = group_words_into_segments(
            word_level_segments,
            min_duration=request.min_duration,
            max_duration=request.max_duration
        )

        valid_segments = [s for s in segments if s['start'] < video_duration]
        for s in valid_segments:
            s['end'] = min(s['end'], video_duration)
        segments = valid_segments[:request.max_clips] if request.max_clips else valid_segments
        logger.info(f"Created {len(segments)} segments from transcript.")

        # --- LOGIC BLOCK 4: Analyze and Clip ---
        # This logic is identical to the original code.
        analysis_results = []
        request.analyze_virality = True
        if request.analyze_virality and segments:
            async def run_analysis():
                tasks = [analyze_virality_with_openai(
                    s['text']) for s in segments]
                return await asyncio.gather(*tasks)
            analysis_results = asyncio.run(run_analysis())
        else:
            analysis_results = [None] * len(segments)

        clipped_videos_paths = video_processing.clip_video_from_text(
            processing_video_path, segments, TEMP_DIR)
        paths_to_cleanup.extend(clipped_videos_paths)

        # --- LOGIC BLOCK 5: Upload and Finalize ---
        # This logic is identical to the original code.
        final_clips_details: List[ProcessedClipDetail] = []
        for segment, clip_path, analysis_data in zip(segments, clipped_videos_paths, analysis_results):
            url = storage.upload_to_cloudinary(clip_path)
            virality_obj = ViralityAnalysisResult(
                **analysis_data) if analysis_data else None
            clip_duration = segment['end'] - segment['start']

            # NOTE: The original code did not save child `VideoClip` records.
            # This version adds that logic as it was likely intended.
            if db_video:
                db_clip = models.VideoClip(
                    video_id=db_video.id, url=url, duration=clip_duration, status="completed")
                db.add(db_clip)

            final_clips_details.append(
                ProcessedClipDetail(
                    url=url, text=segment['text'], start_time=0.0, end_time=clip_duration,
                    virality_analysis=virality_obj, platform=download_metadata.get(
                        'platform', 'unknown')
                )
            )

        # --- LOGIC BLOCK 6: Update Main DB Record Status ---
        if db_video:
            db_video.status = "completed"
            db_video.processed_count = len(final_clips_details)
            db.commit()

        return final_clips_details

    finally:
        # --- LOGIC BLOCK 7: Cleanup ---
        # This replaces the `background_tasks` from the original code.
        logger.info("Worker task finished. Cleaning up files.")
        for path in paths_to_cleanup:
            try:
                if os.path.exists(path):
                    os.remove(path)
            except Exception as e:
                logger.error(
                    f"Cleanup error in worker for path {path}: {str(e)}")


# ==============================================================================
# 2. ASYNCHRONOUS FASTAPI ENDPOINT (The "Manager")
# ==============================================================================
@app.post("/process-url", response_model=URLProcessResponse)
async def process_video_url(
    request: URLProcessRequest,
    current_user: Optional[models.User] = Depends(get_current_user_optional),
    # Still need DB here for the initial limit check
    db: Session = Depends(get_db),
    executor: ThreadPoolExecutor = Depends(get_executor)
):
    """
    This endpoint offloads the original, unchanged processing logic to a background thread.
    """
    # This initial limit check is preserved from your newer code for security.
    if current_user and current_user.subscription == "free":
        video_count = db.query(models.Video).filter(
            models.Video.user_id == current_user.id).count()
        if video_count >= 5:
            raise HTTPException(
                status_code=402, detail="Free subscription limit reached (5 videos)")

    try:
        loop = asyncio.get_running_loop()

        # Pass all necessary data to the worker function as simple types.
        final_clips = await loop.run_in_executor(
            executor,
            run_processing_with_original_logic,
            request.dict(),
            current_user.id if current_user else None,
            current_user.subscription if current_user else None
        )

        return URLProcessResponse(clips=final_clips)
    except InsufficientCreditsError as e:
        logger.warning(f"Credit check failed for {request.url}: {e}")
        raise HTTPException(status_code=402, detail=str(e))

    except Exception as e:
        logger.error(
            f"URL processing error for {request.url}: {e}", exc_info=True)
        # The exception from the worker is re-raised here
        raise HTTPException(
            status_code=500, detail=f"An error occurred during video processing: {str(e)}")


class JobSubmissionResponse(BaseModel):
    success: bool
    message: str
    video_id: int  # The ID of the video record for status tracking


class VideoStatusResponse(BaseModel):
    video_id: int
    status: str
    error_message: Optional[str] = None
    results: Optional[Dict[str, Any]] = None
    processing_time: Optional[int] = None


# def run_advanced_video_processing_task(
#     video_id: int,
#     user_id: Optional[int],
#     video_path: str,
#     output_dir: str,
#     processing_options: dict,
#     original_filename: str,
#     original_url: str | None
# ):
#     """
#     The synchronous worker function that runs in a thread pool.
#     Handles video processing, uploading, DB updates, and cleanup.
#     """
#     db: Session | None = None
#     db_video = None
#     try:
#         db = SessionLocal()
#         db_video = db.query(models.Video).filter(
#             models.Video.id == video_id).first()
#         if not db_video:
#             logger.error(
#                 f"Task failed: Video record with ID {video_id} not found.")
#             return

#         db_video.status = "processing"
#         db.commit()

#         processor = AdvancedVideoProcessor(
#             openai_api_key=os.getenv("OPENAI_API_KEY"),
#             temp_dir=TEMP_DIR
#         )
#         results = processor.process_video_comprehensive(
#             video_path, output_dir, processing_options
#         )

#         if results.get('status') == 'Error':
#             raise Exception(results.get(
#                 'message', 'Processing function returned an error.'))

#         # --- UPLOAD AND DATABASE LOGIC (CORRECTED WITH METADATA) ---
#         uploaded_clips_info = []
#         processed_videos = results.get('processed_videos', {})

#         if 'chunks' in processed_videos and processed_videos['chunks']:
#             logger.info(
#                 f"Found {len(processed_videos['chunks'])} chunk(s) to upload for video ID {video_id}.")

#             # The 'chunks' key now holds a list of dictionaries
#             for chunk_detail in processed_videos['chunks']:
#                 file_to_upload = chunk_detail['path']
#                 print(f"Preparing to upload chunk: {file_to_upload}")
#                 if os.path.exists(file_to_upload):
#                     url = storage.upload_to_cloudinary(file_to_upload)
#                     uploaded_clips_info.append({
#                         'url': url,
#                         # Use the exact duration
#                         'duration': chunk_detail['end_time']
#                     })
#                 else:
#                     logger.warning(
#                         f"Expected file {file_to_upload} not found for video ID {video_id}. Skipping.")

#         elif 'final_output' in processed_videos:
#             logger.info(
#                 f"Found a single final output to upload for video ID {video_id}.")
#             file_to_upload = processed_videos['final_output']
#             if os.path.exists(file_to_upload):
#                 url = storage.upload_to_cloudinary(file_to_upload)
#                 logger.info(f"Uploading final output: {file_to_upload}")
#                 with VideoFileClip(file_to_upload) as clip:
#                     duration = clip.duration
#                 uploaded_clips_info.append({
#                     'url': url,
#                     'duration': duration
#                 })
#         if not uploaded_clips_info:
#             logger.warning(
#                 f"Processing for video ID {video_id} completed, but no files were uploaded.")
#             db_video.status = "failed"
#             db_video.error_message = "Processing completed but no output files were generated or found."
#         else:
#             db_video.status = "completed"
#             db_video.processed_count = len(uploaded_clips_info)

#             # Save clips to the database with correct duration
#             for clip_info in uploaded_clips_info:
#                 db.add(models.VideoClip(
#                     video_id=db_video.id,
#                     url=clip_info['url'],
#                     # <-- Save the exact duration
#                     duration=clip_info['duration'],
#                     status="completed"
#                 ))

#         db.commit()
#         logger.info(
#             f"Task complete: Successfully processed and uploaded {len(uploaded_clips_info)} clip(s) for video ID {video_id}")

#     except Exception as e:
#         logger.error(
#             f"Task failed for video ID {video_id}: {str(e)}", exc_info=True)
#         if db and db_video:
#             db_video.status = "failed"
#             db_video.error_message = str(e)[:1024]
#             db.commit()

#     finally:
#         try:
#             if video_path and os.path.exists(video_path):
#                 os.remove(video_path)
#             if output_dir and os.path.exists(output_dir):
#                 shutil.rmtree(output_dir)
#             logger.info(f"Cleanup complete for video ID {video_id} resources.")
#         except Exception as e:
#             logger.error(f"Cleanup failed for video ID {video_id}: {str(e)}")

#         if db:
#             db.close()
def run_advanced_video_processing_task(
    video_id: int,
    user_id: Optional[int],
    video_path: str,
    output_dir: str,
    processing_options: dict,
    original_filename: str,
    original_url: str | None
):
    """
    The synchronous worker function that runs in a thread pool.
    Handles video processing, uploading, DB updates, and cleanup.
    This version correctly handles both single and multi-clip outputs and saves virality data.
    """
    db: Session | None = None
    db_video = None
    try:
        # Each background task needs its own database session
        db = SessionLocal()
        db_video = db.query(models.Video).filter(
            models.Video.id == video_id).first()
        if not db_video:
            logger.error(
                f"Task failed: Video record with ID {video_id} not found.")
            return

        # Update status to "processing" so the frontend knows work has begun
        db_video.status = "processing"
        db.commit()

        # Instantiate the main processor class
        processor = AdvancedVideoProcessor(
            openai_api_key=os.getenv("OPENAI_API_KEY"),
            temp_dir=TEMP_DIR
        )

        # This is the main, long-running processing call
        results = processor.process_video_comprehensive(
            video_path, output_dir, processing_options
        )

        # If the processing function itself returned an error, raise it to be caught below
        if results.get('status') == 'Error':
            raise Exception(results.get(
                'message', 'Processing function returned an unspecific error.'))

        # <<< --- THIS IS THE CORRECTED AND SIMPLIFIED LOGIC BLOCK --- >>>

        # 1. Get the list of final clips. This key will always exist on success.
        processed_videos = results.get('processed_videos', {})
        final_clips_details = processed_videos.get('clips', [])

        if not final_clips_details:
            # Handle the case where processing succeeded but produced no clips
            logger.warning(
                f"Processing for video ID {video_id} completed, but no output clips were generated.")
            db_video.status = "failed"
            db_video.error_message = "Processing finished but no output files were generated."
        else:
            logger.info(
                f"Processing complete. Found {len(final_clips_details)} clip(s) to upload for video ID {video_id}.")

            # 2. This single loop now works for both single-video and multi-chunk scenarios
            for clip_detail in final_clips_details:
                file_to_upload = clip_detail.get('path')

                if file_to_upload and os.path.exists(file_to_upload):
                    # Upload the physical clip file to get its public URL
                    url = storage.upload_to_cloudinary(file_to_upload)

                    # Get the actual duration of the final created clip for accuracy
                    with VideoFileClip(file_to_upload) as clip:
                        duration = clip.duration

                    # 3. Safely get the virality analysis data attached to this specific clip
                    analysis = clip_detail.get('virality_analysis')
                    score = analysis.get('score') if analysis else None
                    feedback = analysis.get('feedback') if analysis else None

                    # 4. Create the database record with all its metadata, including virality
                    db.add(models.VideoClip(
                        video_id=db_video.id,
                        url=url,
                        duration=duration,
                        status="completed",
                        virality_score=score,        # Save the score
                        virality_feedback=feedback   # Save the feedback
                    ))
                else:
                    logger.warning(
                        f"Expected file {file_to_upload} not found for video ID {video_id}. Skipping.")

            # Update the main video record's final status
            db_video.status = "completed"
            db_video.processed_count = len(final_clips_details)

        # Commit all database changes (new clips and updated video status)
        db.commit()
        logger.info(f"Task complete: DB updated for video ID {video_id}")

    except Exception as e:
        logger.error(
            f"Task failed for video ID {video_id}: {str(e)}", exc_info=True)
        if db and db_video:
            # If any error occurs, mark the job as failed in the DB
            db_video.status = "failed"
            # Slice error message to prevent DB overflow
            db_video.error_message = str(e)[:1024]
            db.commit()
    finally:
        # This block runs whether the task succeeded or failed
        try:
            # Clean up the original downloaded/uploaded file
            if video_path and os.path.exists(video_path):
                os.remove(video_path)
            # Clean up the entire temporary output directory for this job
            if output_dir and os.path.exists(output_dir):
                shutil.rmtree(output_dir)
            logger.info(
                f"Cleanup complete for video ID {video_id} temporary files.")
        except Exception as cleanup_error:
            logger.error(
                f"Cleanup failed for video ID {video_id}: {str(cleanup_error)}")

        # ALWAYS close the database session to release the connection
        if db:
            db.close()


@app.post("/advanced-process", response_model=JobSubmissionResponse)
async def advanced_video_processing(
    request: str = Form(...),
    file: UploadFile | None = File(default=None),
    current_user: Optional[models.User] = Depends(get_current_user_optional),
    db: Session = Depends(get_db),
    executor: ThreadPoolExecutor = Depends(get_executor)  # Inject the executor
):
    """
    Submits an advanced video processing job to the background queue.
    Returns immediately with a video_id for status tracking.
    """
    try:
        # --- Step 1: Validation and File Handling (this part is fast) ---
        request_data = json.loads(request)
        request_model = AdvancedProcessingRequest(**request_data)

        if current_user and current_user.subscription == "free":
            video_count = db.query(models.Video).filter(
                models.Video.user_id == current_user.id).count()
            if video_count >= 5:
                raise HTTPException(
                    status_code=402, detail="Free subscription limit reached (5 videos)")

        video_path = None
        original_filename = "url_video"

        # Save uploaded file or download from URL to a unique path
        unique_id = uuid.uuid4()
        if file:
            original_filename = file.filename
            video_path = os.path.join(
                TEMP_DIR, f"input_{unique_id}_{file.filename}")
            with open(video_path, "wb") as temp_video:
                shutil.copyfileobj(file.file, temp_video)
        elif request_model.video_url:
            processor = url_processor.URLVideoProcessor(TEMP_DIR)
            video_path, _ = processor.download_video(
                request_model.video_url, quality='best', session_id=str(unique_id))
            if not video_path or not os.path.exists(video_path):
                raise HTTPException(
                    status_code=400, detail="Failed to download video from URL.")
        else:
            raise HTTPException(
                status_code=400, detail="Either file upload or video_url must be provided")

        print('*'*50)
        logger.info(
            f"the incoming credits are: {request_model.options.credits}")

        try:
            # This will raise an error if credits are insufficient
            check_credits_against_video_duration(
                credits_from_request=request_model.options.credits,
                video_path=video_path
            )
        except InsufficientCreditsError as e:
            # If the check fails, clean up the temp file before stopping.
            if os.path.exists(video_path):
                os.remove(video_path)
        # Return the 402 Payment Required error to the user
            raise HTTPException(status_code=402, detail=str(e))

        # --- Step 2: Create Initial DB Record ---
        db_video = models.Video(
            user_id=current_user.id if current_user else None,
            filename=original_filename,
            original_path=request_model.video_url or video_path,
            status="pending"  # Start with a 'pending' status
        )
        db.add(db_video)
        db.commit()
        db.refresh(db_video)

        # --- Step 3: Prepare and Submit the Background Task ---
        output_dir = os.path.join(TEMP_DIR, f"advanced_output_{db_video.id}")
        os.makedirs(output_dir, exist_ok=True)

        processing_options = request_model.options.dict()

        loop = asyncio.get_running_loop()
        loop.run_in_executor(
            executor,
            run_advanced_video_processing_task,  # The worker function
            # --- Arguments for the worker ---
            db_video.id,
            current_user.id if current_user else None,
            video_path,
            output_dir,
            processing_options,
            original_filename,
            request_model.video_url,
        )

        logger.info(f"Job submitted for video ID: {db_video.id}")

        # --- Step 4: Return Immediately ---
        return JobSubmissionResponse(
            success=True,
            message="Video processing job has been submitted.",
            video_id=db_video.id
        )

    except Exception as e:
        logger.error(
            f"Failed to submit advanced processing job: {e}", exc_info=True)
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=500, detail=f"Failed to submit job: {str(e)}")

i = 0


# @app.get("/processing-status/{video_id}", response_model=VideoStatusResponse)
# async def get_processing_status(video_id: int, db: Session = Depends(get_db)):
#     """
#     Checks the status of a video processing job.
#     """
#     global i
#     db_video = db.query(models.Video).filter(
#         models.Video.id == video_id).first()
#     if not db_video:
#         raise HTTPException(status_code=404, detail="Job ID not found.")

#     response_data = {
#         "video_id": db_video.id,
#         "status": db_video.status,
#         "error_message": db_video.error_message if db_video.status == "failed" else None,
#         "results": None,
#         "processing_time": None
#     }

#     if db_video.status == "pending" or db_video.status == "processing":
#         if i < 90:
#             i = i + 2
#         response_data = {
#             "video_id": db_video.id,
#             "status": db_video.status,
#             "error_message": db_video.error_message if db_video.status == "failed" else None,
#             "results": None,
#             "processing_time": i
#         }

#     if db_video.status == "completed":
#         clips = db.query(models.VideoClip).filter(
#             models.VideoClip.video_id == video_id).all()
#         response_data["results"] = {
#             "processed_urls": [clip.url for clip in clips]
#         }
#         i = 0

#         clip_details = []
#         for clip in clips:
#             clip_details.append({
#                 "url": clip.url,
#                 "start_time": 0.0,
#                 "end_time": clip.duration,
#                 "duration": clip.duration,
#             })

#         response_data["results"] = {
#             "clips": clip_details,
#         }
#         response_data["processing_time"] = 100

#     return response_data
@app.get("/processing-status/{video_id}", response_model=VideoStatusResponse)
async def get_processing_status(video_id: int, db: Session = Depends(get_db)):
    """
    Checks the status of a video processing job and returns the results
    with the specified start_time and end_time format for each clip.
    """
    global i  # Assuming 'i' is for a temporary progress mock
    db_video = db.query(models.Video).filter(
        models.Video.id == video_id).first()
    if not db_video:
        raise HTTPException(status_code=404, detail="Job ID not found.")

    response_data = {
        "video_id": db_video.id,
        "status": db_video.status,
        "error_message": db_video.error_message if db_video.status == "failed" else None,
        "results": None,
        "processing_time": None
    }

    if db_video.status in ["pending", "processing"]:
        # This part seems to be for mocking progress, which is fine.
        if i < 90:
            i = i + 2
        response_data["processing_time"] = i
        return response_data

    if db_video.status == "completed":
        # Query the database for all clips associated with this video job
        clips_from_db = db.query(models.VideoClip).filter(
            models.VideoClip.video_id == video_id).all()

        # <<< --- START OF CHANGE --- >>>

        final_clip_details = []
        for clip in clips_from_db:
            # Prepare the virality analysis object if data exists
            virality_data = None
            if clip.virality_score is not None:
                virality_data = {
                    "score": clip.virality_score,
                    "feedback": clip.virality_feedback
                }

            # Build the clip object with the exact structure you want
            final_clip_details.append({
                "url": clip.url,
                "start_time": 0.0,  # Always 0.0 as requested
                "end_time": clip.duration,  # The end time is simply the clip's duration
                "duration": clip.duration,  # Keeping duration for convenience
                "virality_analysis": virality_data
            })

        # <<< --- END OF CHANGE --- >>>

        response_data["results"] = {
            "clips": final_clip_details
        }
        response_data["processing_time"] = 100
        i = 0

    return response_data


class GenerationRequest(BaseModel):
    prompt: str
    image: str
    platform: str
    duration: int  # Duration of the video in seconds


class GenerationResponse(BaseModel):
    script: str


# Set your OpenAI API Key
openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))


os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = 'angular-argon-452914-f1-17bffe088833.json'


@app.post("/generate", response_model=GenerationResponse)
async def generate_video_script(request: GenerationRequest):
    try:

        detailed_prompt = (
            f"Create a highly detailed video script for {request.platform} based on the following idea:\n\n"
            f"Idea: {request.prompt}\n"
            f"Target Duration: {request.duration} seconds.\n\n"
            "The script must be clearly structured, alternating between SCENE descriptions and NARRATOR speeches.\n"
            "Each SCENE should be labeled like 'SCENE 1:', 'SCENE 2:', etc., and be extremely vivid and visual and look like this {request.image}.\n"
            "Each NARRATOR line should be emotional, storytelling-style, and connected to the scene.\n\n"
            "FORMAT STRICTLY LIKE THIS:\n\n"
            "SCENE 1:\n"
            "[Detailed description of the first visual scene — colors, environment, emotions, action.]\n\n"
            "NARRATOR:\n"
            "[Narration for the first scene — short, impactful, and expressive.]\n\n"
            "SCENE 2:\n"
            "[Detailed description of the second visual scene.]\n\n"
            "NARRATOR:\n"
            "[Narration for the second scene.]\n\n"
            "Create at least 3 to 5 SCENE and NARRATOR pairs."
        )

        # New way of calling Chat Completions
        response = openai_client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are a creative video scriptwriter."},
                {"role": "user", "content": detailed_prompt}
            ],
            temperature=0.7,
            max_tokens=800
        )

        script = response.choices[0].message.content

        return GenerationResponse(script=script)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Define the request model for generating audio
class AudioGenerationRequest(BaseModel):
    script_text: str
    # Default to English if not provided
    language_code: Optional[str] = "en-US"
    voice_name: Optional[str] = "en-US-Wavenet-D"  # Default voice option

# Define the response model


class AudioGenerationResponse(BaseModel):
    audio_url: str  # The URL or file path of the generated audio


# Request model for audio and image
class AudioRequest(BaseModel):
    script_text: str
    session_name: str
    scene_number: int
    voice_type: str
    platform: str


class ImageRequest(BaseModel):
    script: dict
    session_name: str
    scene_number: int
    mediaType: str
    platform: str


# Helper function to create the session folder and subfolder structure
def create_session_folder(session_name):
    base_folder = f"static/sessions/{session_name}"

    # Check if the base folder exists, if not create it
    if not os.path.exists(base_folder):
        os.makedirs(base_folder, exist_ok=True)

    # Subfolders for images and audio inside the session folder
    image_folder = os.path.join(base_folder, "images")
    audio_folder = os.path.join(base_folder, "audio")

    # Create subfolders if they don't exist
    os.makedirs(image_folder, exist_ok=True)
    os.makedirs(audio_folder, exist_ok=True)

    return base_folder, image_folder, audio_folder


# ElevenLabs config
ELEVENLABS_API_KEY = os.getenv("ELEVENLABS_API_KEY")  # set this securely
VOICE_ID = "EXAVITQu4vr4xnSDxMaL"  # Example voice ID (you can change this)


def create_session_folder(session_name):
    base_folder = f"static/sessions/{session_name}"
    image_folder = os.path.join(base_folder, "images")
    audio_folder = os.path.join(base_folder, "audio")

    os.makedirs(image_folder, exist_ok=True)
    os.makedirs(audio_folder, exist_ok=True)

    return base_folder, image_folder, audio_folder


@app.post("/generate-audio")
async def generate_audio(request: AudioRequest):
    try:
        session_name = request.session_name
        scene_number = request.scene_number
        script_text = request.script_text
        voice_type = request.voice_type

        print(voice_type)

        # Create folders
        base_folder, image_folder, audio_folder = create_session_folder(
            session_name)

        # ElevenLabs API call
        url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_type}"

        headers = {
            "xi-api-key": ELEVENLABS_API_KEY,
            "Content-Type": "application/json"
        }

        payload = {
            "text": script_text,
            "model_id": "eleven_monolingual_v1",
            "voice_settings": {
                "stability": 0.5,
                "similarity_boost": 0.75
            }
        }

        response = requests.post(url, headers=headers, json=payload)

        if response.status_code != 200:
            raise HTTPException(
                status_code=500, detail=f"ElevenLabs error: {response.text}")

        # Save audio file
        audio_filename = os.path.join(
            audio_folder, f"audio_{scene_number}.mp3")
        with open(audio_filename, "wb") as f:
            f.write(response.content)

        print(f"Audio content written to: {audio_filename}")
        return {"audio_path": audio_filename}

    except Exception as e:
        print(f"Error: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error generating audio: {str(e)}")

# Audio request


@app.post("/generate-google-audio")
async def generate_audio(request: AudioRequest):
    try:
        session_name = request.session_name
        scene_number = request.scene_number
        script_text = request.script_text

        # Create session folder and subfolders
        base_folder, image_folder, audio_folder = create_session_folder(
            session_name)

        # Google Text to Speech setup
        client = get_tts_client()
        if not client:
            raise HTTPException(
                status_code=500, detail="Google Cloud TTS not available")

        synthesis_input = texttospeech.SynthesisInput(text=script_text)
        voice = texttospeech.VoiceSelectionParams(
            language_code="en-US",
            ssml_gender=texttospeech.SsmlVoiceGender.NEUTRAL
        )
        audio_config = texttospeech.AudioConfig(
            audio_encoding=texttospeech.AudioEncoding.MP3
        )

        # Perform speech synthesis
        response = client.synthesize_speech(
            input=synthesis_input,
            voice=voice,
            audio_config=audio_config
        )

        # Ensure we get binary data and write it to a file
        audio_filename = os.path.join(
            audio_folder, f"audio_{scene_number}.mp3")

        # Write the binary audio content to a file
        with open(audio_filename, "wb") as out:
            # Ensure response.audio_content is a binary string
            out.write(response.audio_content)

        print(f"Audio content written to: {audio_filename}")

        # Return the path to the audio file
        return {"audio_path": audio_filename}

    except Exception as e:
        print(f"Error: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error generating audio: {str(e)}")


@app.post("/generate-image")
async def generate_images(request: ImageRequest):
    try:
        session_name = request.session_name
        scene_number = request.scene_number
        prompt = request.script["script"]

        # Reuse the same session folder and subfolders for images
        base_folder, image_folder, audio_folder = create_session_folder(
            session_name)

        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            raise HTTPException(
                status_code=500, detail="Missing OpenAI API key")

        response = requests.post(
            "https://api.openai.com/v1/images/generations",
            headers={"Authorization": f"Bearer {openai_api_key}"},
            json={"prompt": prompt, "n": 1, "size": "1024x1024"}
        )

        if response.status_code != 200:
            raise HTTPException(
                status_code=500, detail=f"Failed to generate images: {response.text}")

        data = response.json()
        image_url = data["data"][0]["url"]

        # Save image in the images subfolder with a unique name per scene
        image_filename = os.path.join(
            image_folder, f"image_{scene_number}.jpg")
        img_data = requests.get(image_url).content
        with open(image_filename, "wb") as img_file:
            img_file.write(img_data)

        return {"image_path": image_filename}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


def create_video_from_audio_and_image(audio_file, image_file, output_video, platform):
    try:
        # Define platform resolutions
        platform_resolutions = {
            "youtube": "1920x1080",  # YouTube 16:9
            "tiktok": "1080x1920",  # TikTok portrait
            "instagram": "1024x1024",  # Instagram square
            "linkedin": "1200x627",  # LinkedIn
            "twitter": "1200x675",  # Twitter
        }

        # Default resolution is 1024x1024
        resolution = platform_resolutions.get(platform.lower(), "1024x1024")

        # Get width and height from the resolution
        width, height = map(int, resolution.split('x'))

        # FFmpeg command to create a video with scaling and cropping
        cmd = [
            "ffmpeg",
            "-loop", "1",  # Loop image to match audio duration
            "-framerate", "1",  # 1 frame per second
            "-t", str(get_audio_duration(audio_file)),  # Duration of audio
            "-i", image_file,  # Input image file
            "-i", audio_file,
            # Scale and crop image
            "-vf", f"scale={width}x{height}:force_original_aspect_ratio=increase,crop={width}:{height}",
            "-c:v", "libx264",
            "-preset", "fast",
            "-c:a", "aac",  # Audio codec
            "-strict", "experimental",  # Experimental codecs
            "-shortest",  # Ensure video duration matches audio
            output_video  # Output video file
        ]

        # Run FFmpeg command
        subprocess.run(cmd, check=True)
        print(f"Video created: {output_video}")

    except subprocess.CalledProcessError as e:
        print(f"Error creating video: {e}")
        raise


def get_audio_duration(audio_file):
    """
    Gets the duration of the audio file using FFmpeg.
    """
    cmd = [
        "ffmpeg",
        "-i", audio_file,  # Input audio file
        "-f", "null",  # Discard output
        "-"
    ]
    result = subprocess.run(cmd, stderr=subprocess.PIPE, text=True)
    duration_line = next(
        line for line in result.stderr.splitlines() if "Duration" in line)
    duration_str = duration_line.split("Duration:")[1].split(",")[0].strip()
    h, m, s = map(float, duration_str.split(":"))
    return h * 3600 + m * 60 + s


class VideoRequest(BaseModel):
    session_name: str  # The session name for the video generation
    audio_files: List[str]  # List of audio file paths
    image_files: List[str]  # List of image file paths
    platform: str


@app.post("/generate-video")
async def generate_video(request: VideoRequest):
    try:
        session_name = request.session_name
        audio_files = request.audio_files
        image_files = request.image_files
        platform = request.platform

        if len(audio_files) != len(image_files):
            raise HTTPException(
                status_code=400, detail="Audio files and image files must have the same length.")

        video_files = []
        for i in range(len(audio_files)):
            audio_file = audio_files[i]
            image_file = image_files[i]
            output_video = os.path.join(
                f"static/sessions/{session_name}", f"scene_{i+1}.mp4")

            # Create video from audio and image with platform aspect ratio
            create_video_from_audio_and_image(
                audio_file, image_file, output_video, platform)
            video_files.append(output_video)

        final_video = os.path.join(
            f"static/sessions/{session_name}", "final_video.mp4")
        concatenate_videos(video_files, final_video)

        return {"final_video_path": final_video}

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error generating video: {str(e)}")


def concatenate_videos(video_files, final_output):
    """
    Concatenates multiple video files into one final video.
    """
    try:
        # Create a text file with all video file paths
        with open("video_list.txt", "w") as f:
            for video_file in video_files:
                f.write(f"file '{video_file}'\n")

        # FFmpeg command to concatenate videos
        cmd = ["ffmpeg", "-f", "concat", "-safe", "0", "-i",
               "video_list.txt", "-c", "copy", final_output]
        subprocess.run(cmd, check=True)

        print(f"Final video created: {final_output}")

        # Clean up the temporary video list file
        os.remove("video_list.txt")

    except subprocess.CalledProcessError as e:
        print(f"Error concatenating videos: {e}")
        raise


@app.get("/get-el-voices")
def get_el_voices():
    try:
        url = "https://api.elevenlabs.io/v1/voices"
        headers = {
            "xi-api-key": ELEVENLABS_API_KEY
        }

        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            voices = response.json().get("voices", [])
            return {"voices": voices}
        else:
            raise HTTPException(
                status_code=response.status_code, detail=response.text)

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error retrieving voices: {str(e)}")


# --- Social Media Sharing Endpoints ---

class ShareRequest(BaseModel):
    clipId: str
    clipUrl: str
    title: str
    description: Optional[str] = None
    tags: Optional[List[str]] = None


class ShareResponse(BaseModel):
    success: bool
    message: str
    platform: str
    share_url: Optional[str] = None
    error: Optional[str] = None


@app.post("/share/youtube", response_model=ShareResponse)
async def share_to_youtube(
    request: ShareRequest,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Share a clip to YouTube"""
    try:
        # TODO: Implement YouTube API integration
        # This would require:
        # 1. YouTube OAuth setup
        # 2. YouTube Data API v3 integration
        # 3. Video upload functionality

        # For now, return a placeholder response
        return ShareResponse(
            success=False,
            message="YouTube sharing is not yet implemented. Please check back later.",
            platform="youtube",
            error="Feature not implemented"
        )
    except Exception as e:
        logger.error(f"YouTube sharing error: {e}")
        return ShareResponse(
            success=False,
            message="Failed to share to YouTube",
            platform="youtube",
            error=str(e)
        )


@app.post("/share/instagram", response_model=ShareResponse)
async def share_to_instagram(
    request: ShareRequest,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Share a clip to Instagram"""
    try:
        # TODO: Implement Instagram Basic Display API integration
        # This would require:
        # 1. Instagram OAuth setup
        # 2. Instagram Basic Display API integration
        # 3. Media upload functionality

        return ShareResponse(
            success=False,
            message="Instagram sharing is not yet implemented. Please check back later.",
            platform="instagram",
            error="Feature not implemented"
        )
    except Exception as e:
        logger.error(f"Instagram sharing error: {e}")
        return ShareResponse(
            success=False,
            message="Failed to share to Instagram",
            platform="instagram",
            error=str(e)
        )


@app.post("/share/tiktok", response_model=ShareResponse)
async def share_to_tiktok(
    request: ShareRequest,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Share a clip to TikTok"""
    try:
        # TODO: Implement TikTok API integration
        # This would require:
        # 1. TikTok OAuth setup
        # 2. TikTok API integration
        # 3. Video upload functionality

        return ShareResponse(
            success=False,
            message="TikTok sharing is not yet implemented. Please check back later.",
            platform="tiktok",
            error="Feature not implemented"
        )
    except Exception as e:
        logger.error(f"TikTok sharing error: {e}")
        return ShareResponse(
            success=False,
            message="Failed to share to TikTok",
            platform="tiktok",
            error=str(e)
        )


@app.post("/share/twitter", response_model=ShareResponse)
async def share_to_twitter(
    request: ShareRequest,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Share a clip to Twitter/X"""
    try:
        # TODO: Implement Twitter API v2 integration
        # This would require:
        # 1. Twitter OAuth 2.0 setup
        # 2. Twitter API v2 integration
        # 3. Media upload functionality

        return ShareResponse(
            success=False,
            message="Twitter/X sharing is not yet implemented. Please check back later.",
            platform="twitter",
            error="Feature not implemented"
        )
    except Exception as e:
        logger.error(f"Twitter sharing error: {e}")
        return ShareResponse(
            success=False,
            message="Failed to share to Twitter/X",
            platform="twitter",
            error=str(e)
        )


# @app.post("/compress-and-upload/")
# async def compress_and_upload(file: UploadFile = File(...)):
#     """
#     Receives a video file, compresses it using FFmpeg, uploads the
#     compressed version to Cloudinary, and returns the secure URL.
#     This is a standalone utility endpoint.
#     """
#     # Use the TEMP_DIR you have already defined
#     temp_dir = TEMP_DIR

#     # Generate unique filenames to handle concurrent requests safely
#     unique_id = uuid.uuid4()
#     temp_input_path = os.path.join(
#         temp_dir, f"{unique_id}_input_{file.filename}")
#     temp_output_path = os.path.join(temp_dir, f"{unique_id}_compressed.mp4")

#     try:
#         # 1. Save the originally uploaded video to a temporary file
#         with open(temp_input_path, "wb") as buffer:
#             shutil.copyfileobj(file.file, buffer)

#         # 2. Compress the video using ffmpeg-python. Your existing FFmpeg
#         logger.info(f"Starting compression for {file.filename}...")
#         logger.info(f"Starting robust compression for {file.filename}...")
#         try:
#             (
#                 ffmpeg
#                 .input(temp_input_path)
#                 .output(
#                     temp_output_path,
#                     vcodec='libx264',
#                     # Use a slightly higher quality (lower CRF is better). 23 is a great balance.
#                     crf=23,
#                     # Slower preset gives better quality and compatibility. 'fast' is also a good option if 'medium' is too slow.
#                     preset='medium',
#                     acodec='aac',
#                     audio_bitrate='128k',
#                     # Force a Constant Frame Rate (CFR) of 30 fps. This is VITAL for moviepy.
#                     vf='fps=30',
#                     # Set a universally compatible pixel format.
#                     pix_fmt='yuv420p',
#                     # Optimizes the video for web streaming.
#                     movflags='+faststart'
#                 )
#                 .run(quiet=True, overwrite_output=True)
#             )
#             logger.info("Robust compression finished successfully.")
#         except ffmpeg.Error as e:
#             # Catch and log specific FFmpeg errors for easier debugging
#             logger.error(f"FFmpeg Error: {e.stderr.decode()}")
#             raise HTTPException(
#                 status_code=500, detail=f"Video compression failed.")

#         # 3. Upload the *compressed* video to Cloudinary using your existing config
#         logger.info(f"Uploading compressed file to Cloudinary...")
#         try:
#             upload_result = cloudinary.uploader.upload(
#                 temp_output_path,
#                 resource_type="video",
#             )
#             logger.info("Upload to Cloudinary complete.")
#         except Exception as e:
#             logger.error(f"Cloudinary upload error: {e}")
#             raise HTTPException(
#                 status_code=500, detail="Cloudinary upload failed.")

#         secure_url = upload_result.get('secure_url')
#         if not secure_url:
#             raise HTTPException(
#                 status_code=500, detail="Cloudinary upload failed, no URL returned.")

#         # 4. Return the final URL to the frontend
#         return {"url": secure_url}

#     except Exception as e:
#         # Catch any other errors during the process
#         raise HTTPException(status_code=500, detail=str(e))

#     finally:
#         # 5. CRITICAL: Clean up all temporary files regardless of success or failure
#         logger.info("Cleaning up temporary compression files...")
#         if os.path.exists(temp_input_path):
#             os.remove(temp_input_path)
#         if os.path.exists(temp_output_path):
#             os.remove(temp_output_path)
def run_compression_and_upload_task(temp_input_path: str, original_filename: str) -> str:
    """
    This synchronous function runs in a background thread.
    It handles compression, uploading, and cleanup, then returns the final URL.
    """
    unique_id = uuid.uuid4()
    temp_output_path = os.path.join(TEMP_DIR, f"{unique_id}_compressed.mp4")

    try:
        logger.info(f"Starting compression for '{original_filename}'...")
        # 1. Compress the video using ffmpeg
        (
            ffmpeg
            .input(temp_input_path)
            .output(
                temp_output_path,
                vcodec='libx264', crf=23, preset='medium',
                acodec='aac', audio_bitrate='128k',
                vf='fps=30', pix_fmt='yuv420p', movflags='+faststart'
            )
            .run(quiet=True, overwrite_output=True)
        )
        logger.info("Compression finished successfully.")

        # 2. Upload the *compressed* video to Cloudinary
        logger.info("Uploading compressed file to Cloudinary...")
        upload_result = cloudinary.uploader.upload(
            temp_output_path,
            resource_type="video",
        )
        secure_url = upload_result.get('secure_url')
        if not secure_url:
            raise Exception("Cloudinary upload failed, no URL returned.")

        logger.info("Upload to Cloudinary complete.")
        return secure_url

    except Exception as e:
        logger.error(f"Error in compression/upload task: {e}", exc_info=True)
        # Re-raise the exception so the main endpoint can catch it and return an HTTP 500
        raise e

    finally:
        # 3. CRITICAL: Clean up all temporary files regardless of success or failure
        logger.info("Cleaning up temporary compression files...")
        if os.path.exists(temp_input_path):
            os.remove(temp_input_path)
        if os.path.exists(temp_output_path):
            os.remove(temp_output_path)


@app.post("/compress-and-upload/", response_model=CompressionResponse)
async def compress_and_upload(
    file: UploadFile = File(...),
    credits: float = Form(...),
    executor: ThreadPoolExecutor = Depends(get_executor)
):
    """
    Receives a video file, runs compression and upload in a background thread,
    waits for the result, and returns the final URL in the same response.
    The server event loop is NOT blocked during this process.
    """
    # Use a unique ID for the initial temporary file to handle concurrent uploads safely
    unique_id = uuid.uuid4()
    temp_input_path = os.path.join(
        TEMP_DIR, f"{unique_id}_input_{file.filename}")

    try:
        # 1. Save the originally uploaded video to a temporary file (this is fast)
        with open(temp_input_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
            # Check if user has enough credits for the video duration
        check_credits_against_video_duration(
            credits_from_request=credits,  # Use the new Form field
            video_path=temp_input_path
        )

        # 2. Offload the heavy work to the ThreadPoolExecutor and await the result
        loop = asyncio.get_running_loop()

        final_url = await loop.run_in_executor(
            executor,
            run_compression_and_upload_task,  # The synchronous worker function
            temp_input_path,                   # First argument for the worker
            file.filename                      # Second argument for the worker
        )

        # 3. Return the final URL to the frontend
        return CompressionResponse(url=final_url)

    except InsufficientCreditsError as e:
        logger.warning(f"Credit check failed for {file.filename}: {e}")
        raise HTTPException(status_code=402, detail=str(e))

    except Exception as e:
        # If the background task raises an exception, it will be caught here.
        # The finally block in the worker will still handle cleanup.
        logger.error(
            f"Failed to process compression job for {file.filename}: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"An error occurred during video processing: {str(e)}")

    finally:
        # Step 5: CRITICAL cleanup. This block executes regardless of success or failure,
        # ensuring the temporary input file is always deleted.
        if os.path.exists(temp_input_path):
            try:
                os.remove(temp_input_path)
                logger.info(
                    f"Cleaned up temporary input file: {temp_input_path}")
            except Exception as cleanup_error:
                logger.error(
                    f"Failed to clean up temporary input file {temp_input_path}: {cleanup_error}")

# --- Facial AI Processing Endpoints ---


# Global facial AI processor instance
facial_ai_processor = FacialAIProcessor(
    faces_api_key="eyJraWQiOm51bGwsImFsZyI6IlJTMjU2In0"
)

# In-memory job storage (in production, use Redis or database)
face_detection_jobs = {}


@app.post("/api/face-detection/analyze", response_model=FaceDetectionResponse)
async def analyze_video_for_faces(
    request: FaceDetectionAnalyzeRequest = None,
    file: UploadFile = File(None),
    current_user: models.User = Depends(get_current_user)
):
    """
    Analyze video for faces and speaking segments
    """
    try:
        logger.info("Starting face detection analysis")

        # Handle file upload or URL
        video_path = None
        temp_file = None

        if file:
            # Handle uploaded file
            temp_file = tempfile.mktemp(suffix=".mp4")
            with open(temp_file, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            video_path = temp_file
        elif request and request.video_url:
            # Handle URL - download video first
            video_path = await url_processor.download_video(request.video_url)
        else:
            raise HTTPException(
                status_code=400, detail="Either file upload or video_url is required")

        if not video_path or not os.path.exists(video_path):
            raise HTTPException(
                status_code=400, detail="Invalid video file or URL")

        # Create enhanced zoom config
        config = ZoomConfig()
        if request and request.config:
            config.zoom_level = request.config.zoom_level
            config.transition_speed = request.config.transition_speed
            config.detection_sensitivity = request.config.detection_sensitivity
            config.voice_threshold = request.config.voice_threshold
            config.padding = request.config.padding
            # Enhanced parameters
            config.include_torso = request.config.include_torso
            config.headroom_ratio = request.config.headroom_ratio
            config.torso_ratio = request.config.torso_ratio
            config.speaker_switch_threshold = request.config.speaker_switch_threshold
            config.transition_smoothness = request.config.transition_smoothness
            config.speaker_memory_duration = request.config.speaker_memory_duration

        # Analyze video
        analysis_result = await facial_ai_processor.analyze_video(video_path, config)

        # Clean up temporary file
        if temp_file and os.path.exists(temp_file):
            os.remove(temp_file)

        return FaceDetectionResponse(
            success=True,
            message="Video analysis completed successfully",
            analysis_data=analysis_result
        )

    except Exception as e:
        logger.error(f"Error in face detection analysis: {str(e)}")
        # Clean up on error
        if temp_file and os.path.exists(temp_file):
            os.remove(temp_file)

        return FaceDetectionResponse(
            success=False,
            message="Face detection analysis failed",
            error=str(e)
        )


@app.post("/api/face-detection/process", response_model=FaceDetectionResponse)
async def process_video_with_face_detection(
    background_tasks: BackgroundTasks,
    request: FaceDetectionProcessRequest = None,
    file: UploadFile = File(None),
    current_user: models.User = Depends(get_current_user)
):
    """
    Process video with dynamic face zoom effects
    """
    try:
        logger.info("Starting face detection video processing")

        # Generate job ID
        job_id = str(uuid.uuid4())

        # Initialize job status
        face_detection_jobs[job_id] = {
            "status": "pending",
            "progress": 0.0,
            "created_at": datetime.now().isoformat(),
            "user_id": current_user.id
        }

        # Handle file upload or URL
        video_path = None
        temp_file = None

        if file:
            # Handle uploaded file
            temp_file = tempfile.mktemp(suffix=".mp4")
            with open(temp_file, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            video_path = temp_file
        elif request and request.video_url:
            # Handle URL - download video first
            video_path = await url_processor.download_video(request.video_url)
        else:
            raise HTTPException(
                status_code=400, detail="Either file upload or video_url is required")

        if not video_path or not os.path.exists(video_path):
            raise HTTPException(
                status_code=400, detail="Invalid video file or URL")

        # Create enhanced zoom config
        config = ZoomConfig()
        if request and request.config:
            config.zoom_level = request.config.zoom_level
            config.transition_speed = request.config.transition_speed
            config.detection_sensitivity = request.config.detection_sensitivity
            config.voice_threshold = request.config.voice_threshold
            config.padding = request.config.padding
            # Enhanced parameters
            config.include_torso = request.config.include_torso
            config.headroom_ratio = request.config.headroom_ratio
            config.torso_ratio = request.config.torso_ratio
            config.speaker_switch_threshold = request.config.speaker_switch_threshold
            config.transition_smoothness = request.config.transition_smoothness
            config.speaker_memory_duration = request.config.speaker_memory_duration

        # Start background processing
        background_tasks.add_task(
            process_face_detection_background,
            job_id,
            video_path,
            config,
            current_user.id,
            temp_file
        )

        return FaceDetectionResponse(
            success=True,
            message="Face detection processing started",
            job_id=job_id
        )

    except Exception as e:
        logger.error(f"Error starting face detection processing: {str(e)}")
        return FaceDetectionResponse(
            success=False,
            message="Failed to start face detection processing",
            error=str(e)
        )


@app.get("/api/face-detection/status/{job_id}", response_model=FaceDetectionStatusResponse)
async def get_face_detection_status(
    job_id: str,
    current_user: models.User = Depends(get_current_user)
):
    """
    Check processing status of face detection job
    """
    if job_id not in face_detection_jobs:
        raise HTTPException(status_code=404, detail="Job not found")

    job = face_detection_jobs[job_id]

    # Check if user owns this job
    if job.get("user_id") != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    return FaceDetectionStatusResponse(
        job_id=job_id,
        status=job["status"],
        progress=job["progress"],
        result_url=job.get("result_url"),
        error=job.get("error"),
        created_at=job["created_at"],
        completed_at=job.get("completed_at")
    )


@app.post("/api/face-detection/preview", response_model=FaceDetectionResponse)
async def generate_face_detection_preview(
    request: FaceDetectionPreviewRequest = None,
    file: UploadFile = File(None),
    current_user: models.User = Depends(get_current_user)
):
    """
    Generate preview with face tracking overlay
    """
    try:
        logger.info("Generating face detection preview")

        # Handle file upload or URL
        video_path = None
        temp_file = None

        if file:
            # Handle uploaded file
            temp_file = tempfile.mktemp(suffix=".mp4")
            with open(temp_file, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            video_path = temp_file
        elif request and request.video_url:
            # Handle URL - download video first
            video_path = await url_processor.download_video(request.video_url)
        else:
            raise HTTPException(
                status_code=400, detail="Either file upload or video_url is required")

        if not video_path or not os.path.exists(video_path):
            raise HTTPException(
                status_code=400, detail="Invalid video file or URL")

        # Create enhanced zoom config
        config = ZoomConfig()
        if request and request.config:
            config.zoom_level = request.config.zoom_level
            config.transition_speed = request.config.transition_speed
            config.detection_sensitivity = request.config.detection_sensitivity
            config.voice_threshold = request.config.voice_threshold
            config.padding = request.config.padding
            # Enhanced parameters
            config.include_torso = request.config.include_torso
            config.headroom_ratio = request.config.headroom_ratio
            config.torso_ratio = request.config.torso_ratio
            config.speaker_switch_threshold = request.config.speaker_switch_threshold
            config.transition_smoothness = request.config.transition_smoothness
            config.speaker_memory_duration = request.config.speaker_memory_duration

        # Generate preview
        preview_data = await facial_ai_processor.generate_preview(video_path, config)

        # Clean up temporary file
        if temp_file and os.path.exists(temp_file):
            os.remove(temp_file)

        return FaceDetectionResponse(
            success=True,
            message="Preview generated successfully",
            preview_data=preview_data
        )

    except Exception as e:
        logger.error(f"Error generating face detection preview: {str(e)}")
        # Clean up on error
        if temp_file and os.path.exists(temp_file):
            os.remove(temp_file)

        return FaceDetectionResponse(
            success=False,
            message="Preview generation failed",
            error=str(e)
        )


async def process_face_detection_background(
    job_id: str,
    video_path: str,
    config: ZoomConfig,
    user_id: int,
    temp_file: str = None
):
    """
    Background task for processing video with face detection
    """
    try:
        # Update job status
        face_detection_jobs[job_id]["status"] = "processing"
        face_detection_jobs[job_id]["progress"] = 0.1

        # Generate output path
        output_filename = f"face_enhanced_{job_id}.mp4"
        output_path = os.path.join("backend/temp", output_filename)

        # Ensure temp directory exists
        os.makedirs("backend/temp", exist_ok=True)

        # Update progress
        face_detection_jobs[job_id]["progress"] = 0.3

        # Process video with face detection
        processed_path = await facial_ai_processor.process_video_with_face_zoom(
            video_path, output_path, config
        )

        # Update progress
        face_detection_jobs[job_id]["progress"] = 0.8

        # Upload to Cloudinary
        upload_result = cloudinary.uploader.upload(
            processed_path,
            resource_type="video",
            folder="smartclips/face_enhanced",
            public_id=f"face_enhanced_{job_id}"
        )

        result_url = upload_result["secure_url"]

        # Update job completion
        face_detection_jobs[job_id].update({
            "status": "completed",
            "progress": 1.0,
            "result_url": result_url,
            "completed_at": datetime.now().isoformat()
        })

        # Clean up temporary files
        if temp_file and os.path.exists(temp_file):
            os.remove(temp_file)
        if os.path.exists(processed_path):
            os.remove(processed_path)

        logger.info(f"Face detection processing completed for job {job_id}")

    except Exception as e:
        logger.error(
            f"Error in background face detection processing: {str(e)}")

        # Update job with error
        face_detection_jobs[job_id].update({
            "status": "failed",
            "error": str(e),
            "completed_at": datetime.now().isoformat()
        })

        # Clean up on error
        if temp_file and os.path.exists(temp_file):
            os.remove(temp_file)


# --- GPT Editor API Endpoints ---

# Import GPT Editor modules
from gpt_editor_jobs import job_manager
from gpt_editor_service import GPTEditorService
from template_system import TemplateSystemIntegration
from chat_history_service import chat_service

# Initialize GPT Editor services
gpt_editor_service = GPTEditorService(openai_api_key=os.getenv("OPENAI_API_KEY"))
template_integration = TemplateSystemIntegration(openai_api_key=os.getenv("OPENAI_API_KEY"))

@app.post("/api/gpt-editor/process", response_model=GPTEditingResponse)
async def process_gpt_editing_request(
    request: GPTEditingRequest,
    current_user: models.User = Depends(get_current_user)
):
    """
    Process a natural language video editing request
    """
    try:
        # Create a new job
        job_id = job_manager.create_job(
            command=request.command,
            video_url=request.video_url,
            video_file=request.video_file,
            template=request.template,
            options=request.options,
            user_id=current_user.id,
            session_id=request.options.get("session_id") if request.options else None
        )

        # Parse the command to provide immediate feedback
        parsed_commands = await gpt_editor_service.parse_editing_command(request.command)

        # Estimate processing time
        estimated_time = gpt_editor_service.estimate_processing_time(parsed_commands)

        # Start background processing
        await job_manager.start_job_processing(job_id)

        return GPTEditingResponse(
            job_id=job_id,
            status="processing",
            message="Video editing job started successfully",
            parsed_commands=parsed_commands,
            estimated_time=estimated_time
        )

    except Exception as e:
        logger.error(f"Error processing GPT editing request: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/gpt-editor/upload-and-process")
async def upload_and_process_video(
    file: UploadFile = File(...),
    command: str = Form(...),
    template: Optional[str] = Form(None),
    current_user: models.User = Depends(get_current_user)
):
    """
    Upload a video file and process it with GPT editing
    """
    try:
        # Save uploaded file
        temp_path = os.path.join(TEMP_DIR, f"upload_{datetime.now().timestamp()}_{file.filename}")
        with open(temp_path, "wb") as temp_file:
            shutil.copyfileobj(file.file, temp_file)

        # Create processing job
        job_id = job_manager.create_job(
            command=command,
            video_file=temp_path,
            template=template
        )

        # Start processing
        await job_manager.start_job_processing(job_id)

        return {
            "job_id": job_id,
            "status": "processing",
            "message": "Video uploaded and processing started"
        }

    except Exception as e:
        logger.error(f"Error in upload and process: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/gpt-editor/job/{job_id}", response_model=GPTJobStatus)
async def get_job_status(
    job_id: str,
    current_user: models.User = Depends(get_current_user)
):
    """
    Get the status of a GPT editing job
    """
    job_status = job_manager.get_job_status(job_id)

    if not job_status:
        raise HTTPException(status_code=404, detail="Job not found")

    return GPTJobStatus(
        job_id=job_status["job_id"],
        status=job_status["status"],
        progress=job_status["progress"],
        result_url=job_status.get("result_url"),
        error_message=job_status.get("error_message"),
        processing_log=[log["message"] for log in job_status.get("processing_log", [])]
    )

@app.delete("/api/gpt-editor/job/{job_id}")
async def cancel_job(
    job_id: str,
    current_user: models.User = Depends(get_current_user)
):
    """
    Cancel a running GPT editing job
    """
    success = job_manager.cancel_job(job_id)

    if not success:
        raise HTTPException(status_code=404, detail="Job not found")

    return {"message": "Job cancelled successfully"}

@app.get("/api/gpt-editor/templates")
async def get_available_templates(
    current_user: models.User = Depends(get_current_user)
):
    """
    Get all available video templates
    """
    templates = template_integration.get_available_templates()
    return {"templates": templates}

@app.post("/api/gpt-editor/analyze-video")
async def analyze_video_for_template(
    request: dict,
    current_user: models.User = Depends(get_current_user)
):
    """
    Analyze a video and recommend the best template
    """
    video_url = request.get("video_url")
    video_file = request.get("video_file")

    if not video_url and not video_file:
        raise HTTPException(status_code=400, detail="Either video_url or video_file must be provided")

    try:
        # For URL, download temporarily for analysis
        if video_url:
            import requests
            temp_path = os.path.join(TEMP_DIR, f"analysis_{datetime.now().timestamp()}.mp4")
            response = requests.get(video_url, stream=True)
            response.raise_for_status()

            with open(temp_path, 'wb') as f:
                shutil.copyfileobj(response.raw, f)

            analysis_path = temp_path
        else:
            analysis_path = video_file

        # Analyze video
        recommendation = await template_integration.analyze_and_recommend_template(analysis_path)

        # Clean up temporary file if created
        if video_url and os.path.exists(temp_path):
            os.remove(temp_path)

        return recommendation

    except Exception as e:
        logger.error(f"Error analyzing video: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/gpt-editor/stats")
async def get_processing_stats(
    current_user: models.User = Depends(get_current_user)
):
    """
    Get statistics about GPT editor job processing
    """
    stats = job_manager.get_job_statistics()
    return stats

# --- Chat History and Presets API Endpoints ---

@app.get("/api/gpt-editor/chat-history")
async def get_chat_history(
    session_id: Optional[str] = None,
    limit: int = 50,
    current_user: models.User = Depends(get_current_user)
):
    """
    Get chat history for the current user
    """
    try:
        history = chat_service.get_chat_history(
            user_id=current_user.id,
            session_id=session_id,
            limit=limit
        )
        return {"chat_history": history}

    except Exception as e:
        logger.error(f"Error getting chat history: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/gpt-editor/create-preset")
async def create_preset_from_job(
    request: dict,
    current_user: models.User = Depends(get_current_user)
):
    """
    Create a preset from a successful job
    """
    job_id = request.get("job_id")
    preset_name = request.get("name")
    description = request.get("description", "")
    is_public = request.get("is_public", False)

    if not job_id or not preset_name:
        raise HTTPException(status_code=400, detail="job_id and name are required")

    try:
        preset_id = chat_service.create_preset_from_successful_job(
            job_id=job_id,
            preset_name=preset_name,
            description=description,
            is_public=is_public
        )

        if preset_id:
            return {"preset_id": preset_id, "message": "Preset created successfully"}
        else:
            raise HTTPException(status_code=404, detail="Job not found or not successful")

    except Exception as e:
        logger.error(f"Error creating preset: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/gpt-editor/presets")
async def get_presets(
    category: Optional[str] = None,
    is_public: Optional[bool] = None,
    my_presets: bool = False,
    limit: int = 20,
    current_user: models.User = Depends(get_current_user)
):
    """
    Get editing presets
    """
    try:
        user_id = current_user.id if my_presets else None

        presets = chat_service.get_presets(
            user_id=user_id,
            category=category,
            is_public=is_public,
            limit=limit
        )

        return {"presets": presets}

    except Exception as e:
        logger.error(f"Error getting presets: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/gpt-editor/use-preset/{preset_id}")
async def use_preset(
    preset_id: str,
    current_user: models.User = Depends(get_current_user)
):
    """
    Use a preset to create a new editing job
    """
    try:
        preset_data = chat_service.use_preset(preset_id, current_user.id)

        if not preset_data:
            raise HTTPException(status_code=404, detail="Preset not found")

        # Create a new job using the preset
        job_id = job_manager.create_job(
            command=preset_data["original_prompt"],
            template=preset_data.get("template_config", {}).get("template"),
            options={"preset_id": preset_id, "preset_name": preset_data["name"]},
            user_id=current_user.id
        )

        # Start processing
        await job_manager.start_job_processing(job_id)

        return {
            "job_id": job_id,
            "preset_name": preset_data["name"],
            "message": "Job created from preset successfully"
        }

    except Exception as e:
        logger.error(f"Error using preset: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
