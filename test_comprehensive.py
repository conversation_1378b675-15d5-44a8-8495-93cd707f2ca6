#!/usr/bin/env python3
"""
Comprehensive test suite for SmartClips after cleanup
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:8081"

def test_backend_health():
    """Test backend health and basic functionality"""
    print("🔍 Testing Backend Health...")
    
    try:
        response = requests.get(f"{BASE_URL}/", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ Backend running: {data['message']}")
            return True
        else:
            print(f"  ❌ Backend error: Status {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ Backend connection failed: {str(e)}")
        return False

def test_api_endpoints():
    """Test key API endpoints"""
    print("\n🔍 Testing API Endpoints...")
    
    endpoints = [
        ("/docs", "GET", "API Documentation"),
        ("/openapi.json", "GET", "OpenAPI Schema"),
        ("/validate-url", "POST", "URL Validation"),
    ]
    
    passed = 0
    for endpoint, method, description in endpoints:
        try:
            if method == "GET":
                response = requests.get(f"{BASE_URL}{endpoint}", timeout=5)
            else:
                response = requests.post(f"{BASE_URL}{endpoint}", 
                                       json={"url": "https://youtube.com/watch?v=test"}, 
                                       timeout=5)
            
            if response.status_code in [200, 401, 422]:
                print(f"  ✅ {description}: Status {response.status_code}")
                passed += 1
            else:
                print(f"  ❌ {description}: Status {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ {description}: {str(e)}")
    
    print(f"API Endpoints: {passed}/{len(endpoints)} passed")
    return passed == len(endpoints)

def test_frontend_accessibility():
    """Test frontend accessibility"""
    print("\n🔍 Testing Frontend Accessibility...")
    
    try:
        response = requests.get(FRONTEND_URL, timeout=10)
        if response.status_code == 200:
            print(f"  ✅ Frontend accessible at {FRONTEND_URL}")
            
            # Check if it's serving the React app
            content = response.text
            if "<!doctype html>" in content.lower() and "vite" in content.lower():
                print("  ✅ React app served correctly")
                return True
            else:
                print("  ⚠️  Frontend accessible but may not be React app")
                return False
        else:
            print(f"  ❌ Frontend error: Status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ Frontend connection failed: {str(e)}")
        return False

def test_ai_clipper_integration():
    """Test AI clipper integration through API"""
    print("\n🔍 Testing AI Clipper Integration...")
    
    try:
        # Test the process-url endpoint structure
        response = requests.post(f"{BASE_URL}/process-url", 
                               json={"url": "invalid-url"}, 
                               timeout=10)
        
        # Should return 422 for validation error or 401 for auth
        if response.status_code in [401, 422]:
            print("  ✅ Process URL endpoint accessible")
            
            # Test the advanced-process endpoint
            response2 = requests.post(f"{BASE_URL}/advanced-process", 
                                    json={"url": "invalid-url"}, 
                                    timeout=10)
            
            if response2.status_code in [401, 422]:
                print("  ✅ Advanced process endpoint accessible")
                print("  ✅ AI clipper endpoints properly configured")
                return True
            else:
                print(f"  ❌ Advanced process endpoint error: {response2.status_code}")
                return False
        else:
            print(f"  ❌ Process URL endpoint error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ AI clipper integration test failed: {str(e)}")
        return False

def test_video_ordering_api():
    """Test video ordering API functionality"""
    print("\n🔍 Testing Video Ordering API...")
    
    try:
        # Test user clips endpoint (should require auth)
        response = requests.get(f"{BASE_URL}/user/clips", timeout=10)
        
        if response.status_code == 401:
            print("  ✅ User clips endpoint properly protected")
            
            # Test with query parameters for ordering
            response2 = requests.get(f"{BASE_URL}/user/clips?skip=0&limit=10", timeout=10)
            
            if response2.status_code == 401:
                print("  ✅ Video ordering parameters accepted")
                print("  ✅ Video ordering API properly configured")
                return True
            else:
                print(f"  ❌ Video ordering query error: {response2.status_code}")
                return False
        else:
            print(f"  ❌ User clips endpoint error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ Video ordering API test failed: {str(e)}")
        return False

def test_cors_configuration():
    """Test CORS configuration for frontend integration"""
    print("\n🔍 Testing CORS Configuration...")
    
    try:
        response = requests.options(f"{BASE_URL}/", 
                                  headers={
                                      "Origin": "http://localhost:8081",
                                      "Access-Control-Request-Method": "POST"
                                  }, 
                                  timeout=10)
        
        cors_origin = response.headers.get("Access-Control-Allow-Origin")
        cors_methods = response.headers.get("Access-Control-Allow-Methods")
        
        if cors_origin and cors_methods:
            print(f"  ✅ CORS configured: Origin={cors_origin}")
            print(f"  ✅ Methods allowed: {cors_methods}")
            return True
        else:
            print("  ❌ CORS headers missing")
            return False
            
    except Exception as e:
        print(f"  ❌ CORS test failed: {str(e)}")
        return False

def test_database_connectivity():
    """Test database connectivity through API"""
    print("\n🔍 Testing Database Connectivity...")
    
    try:
        # Test user stats endpoint (should require auth but indicate DB connection)
        response = requests.get(f"{BASE_URL}/user/stats", timeout=10)
        
        if response.status_code == 401:
            print("  ✅ Database-dependent endpoint accessible")
            
            # Test video count endpoint
            response2 = requests.get(f"{BASE_URL}/user/video-count", timeout=10)
            
            if response2.status_code == 401:
                print("  ✅ Video count endpoint accessible")
                print("  ✅ Database connectivity confirmed")
                return True
            else:
                print(f"  ❌ Video count endpoint error: {response2.status_code}")
                return False
        else:
            print(f"  ❌ User stats endpoint error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ Database connectivity test failed: {str(e)}")
        return False

def generate_cleanup_report():
    """Generate final cleanup and testing report"""
    print("\n" + "="*60)
    print("🧹 SMARTCLIPS CLEANUP & TESTING REPORT")
    print("="*60)
    
    print(f"📅 Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔧 Backend URL: {BASE_URL}")
    print(f"🌐 Frontend URL: {FRONTEND_URL}")
    
    # Run all tests
    tests = [
        ("Backend Health", test_backend_health),
        ("API Endpoints", test_api_endpoints),
        ("Frontend Accessibility", test_frontend_accessibility),
        ("AI Clipper Integration", test_ai_clipper_integration),
        ("Video Ordering API", test_video_ordering_api),
        ("CORS Configuration", test_cors_configuration),
        ("Database Connectivity", test_database_connectivity),
    ]
    
    results = {}
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = "✅ PASS" if result else "❌ FAIL"
            if result:
                passed += 1
        except Exception as e:
            results[test_name] = f"❌ ERROR: {str(e)}"
    
    # Summary
    print(f"\n📊 TEST SUMMARY:")
    print(f"   Total Tests: {total}")
    print(f"   Passed: {passed}")
    print(f"   Failed: {total - passed}")
    print(f"   Success Rate: {(passed/total)*100:.1f}%")
    
    print(f"\n📋 DETAILED RESULTS:")
    for test_name, result in results.items():
        print(f"   {test_name}: {result}")
    
    # Overall status
    if passed == total:
        print(f"\n🎉 EXCELLENT! All systems operational!")
        overall_status = "EXCELLENT"
    elif passed >= total * 0.8:
        print(f"\n✅ GOOD! Most systems working correctly.")
        overall_status = "GOOD"
    else:
        print(f"\n⚠️  NEEDS ATTENTION! Several issues detected.")
        overall_status = "NEEDS ATTENTION"
    
    print(f"\n🏆 OVERALL STATUS: {overall_status}")
    
    return passed == total

if __name__ == "__main__":
    generate_cleanup_report()
