#!/usr/bin/env python3
"""
Environment Configuration Verification
Tests all environment variables and API key configurations
"""

import os
import requests
import json
import subprocess
from typing import Dict, Any, Optional

BASE_URL = "http://localhost:8000"

class EnvironmentTester:
    def __init__(self):
        self.required_env_vars = {
            # Database
            "DATABASE_URL": "Database connection string",
            
            # API Keys
            "OPENAI_API_KEY": "OpenAI API key for AI features",
            "CLOUDINARY_CLOUD_NAME": "Cloudinary cloud name",
            "CLOUDINARY_API_KEY": "Cloudinary API key", 
            "CLOUDINARY_API_SECRET": "Cloudinary API secret",
            "ELEVENLABS_API_KEY": "ElevenLabs API key for voice synthesis",
            
            # Authentication
            "SECRET_KEY": "JWT secret key",
            "ALGORITHM": "JWT algorithm",
            "ACCESS_TOKEN_EXPIRE_MINUTES": "Token expiration time",
            
            # Optional
            "SUPABASE_URL": "Supabase URL (optional)",
            "SUPABASE_KEY": "Supabase key (optional)",
            "GOOGLE_CLIENT_ID": "Google OAuth client ID (optional)",
            "GOOGLE_CLIENT_SECRET": "Google OAuth client secret (optional)"
        }
        
        self.optional_env_vars = {
            "FFMPEG_BINARY": "FFmpeg binary path",
            "TEMP_DIR": "Temporary directory for processing",
            "AWS_ACCESS_KEY_ID": "AWS access key (optional)",
            "AWS_SECRET_ACCESS_KEY": "AWS secret key (optional)"
        }
    
    def test_environment_variables(self) -> Dict[str, Any]:
        """Test all environment variables"""
        print("🔧 Testing Environment Variables...")
        
        results = {
            "required": {},
            "optional": {},
            "missing_required": [],
            "missing_optional": []
        }
        
        # Test required variables
        print("\n  Required Environment Variables:")
        for var_name, description in self.required_env_vars.items():
            value = os.getenv(var_name)
            if value:
                # Mask sensitive values
                display_value = value[:10] + "..." if len(value) > 10 else value
                if "key" in var_name.lower() or "secret" in var_name.lower():
                    display_value = "*" * min(len(value), 20)
                
                print(f"    ✅ {var_name}: {display_value}")
                results["required"][var_name] = True
            else:
                print(f"    ❌ {var_name}: Not set - {description}")
                results["required"][var_name] = False
                results["missing_required"].append(var_name)
        
        # Test optional variables
        print("\n  Optional Environment Variables:")
        for var_name, description in self.optional_env_vars.items():
            value = os.getenv(var_name)
            if value:
                display_value = value[:20] + "..." if len(value) > 20 else value
                print(f"    ✅ {var_name}: {display_value}")
                results["optional"][var_name] = True
            else:
                print(f"    ⚠️  {var_name}: Not set - {description}")
                results["optional"][var_name] = False
                results["missing_optional"].append(var_name)
        
        return results
    
    def test_api_service_connectivity(self) -> Dict[str, bool]:
        """Test connectivity to external API services"""
        print("\n🌐 Testing API Service Connectivity...")
        
        results = {}
        
        # Test OpenAI API
        print("\n  Testing OpenAI API...")
        try:
            openai_key = os.getenv("OPENAI_API_KEY")
            if openai_key:
                headers = {"Authorization": f"Bearer {openai_key}"}
                response = requests.get(
                    "https://api.openai.com/v1/models",
                    headers=headers,
                    timeout=10
                )
                if response.status_code == 200:
                    print("    ✅ OpenAI API: Connected")
                    results["openai"] = True
                else:
                    print(f"    ❌ OpenAI API: Error {response.status_code}")
                    results["openai"] = False
            else:
                print("    ⚠️  OpenAI API: No API key configured")
                results["openai"] = False
        except Exception as e:
            print(f"    ❌ OpenAI API: Connection failed - {str(e)}")
            results["openai"] = False
        
        # Test Cloudinary
        print("\n  Testing Cloudinary...")
        try:
            cloud_name = os.getenv("CLOUDINARY_CLOUD_NAME")
            if cloud_name:
                response = requests.get(
                    f"https://res.cloudinary.com/{cloud_name}/image/upload/sample.jpg",
                    timeout=10
                )
                if response.status_code == 200:
                    print("    ✅ Cloudinary: Connected")
                    results["cloudinary"] = True
                else:
                    print(f"    ❌ Cloudinary: Error {response.status_code}")
                    results["cloudinary"] = False
            else:
                print("    ⚠️  Cloudinary: No cloud name configured")
                results["cloudinary"] = False
        except Exception as e:
            print(f"    ❌ Cloudinary: Connection failed - {str(e)}")
            results["cloudinary"] = False
        
        # Test ElevenLabs
        print("\n  Testing ElevenLabs...")
        try:
            elevenlabs_key = os.getenv("ELEVENLABS_API_KEY")
            if elevenlabs_key:
                headers = {"xi-api-key": elevenlabs_key}
                response = requests.get(
                    "https://api.elevenlabs.io/v1/voices",
                    headers=headers,
                    timeout=10
                )
                if response.status_code == 200:
                    print("    ✅ ElevenLabs API: Connected")
                    results["elevenlabs"] = True
                else:
                    print(f"    ❌ ElevenLabs API: Error {response.status_code}")
                    results["elevenlabs"] = False
            else:
                print("    ⚠️  ElevenLabs API: No API key configured")
                results["elevenlabs"] = False
        except Exception as e:
            print(f"    ❌ ElevenLabs API: Connection failed - {str(e)}")
            results["elevenlabs"] = False
        
        return results
    
    def test_system_dependencies(self) -> Dict[str, bool]:
        """Test system dependencies like FFmpeg"""
        print("\n🔧 Testing System Dependencies...")
        
        results = {}
        
        # Test FFmpeg
        print("\n  Testing FFmpeg...")
        try:
            result = subprocess.run(
                ["ffmpeg", "-version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode == 0:
                version_line = result.stdout.split('\n')[0]
                print(f"    ✅ FFmpeg: Available - {version_line}")
                results["ffmpeg"] = True
            else:
                print("    ❌ FFmpeg: Not working properly")
                results["ffmpeg"] = False
        except FileNotFoundError:
            print("    ❌ FFmpeg: Not found in PATH")
            results["ffmpeg"] = False
        except Exception as e:
            print(f"    ❌ FFmpeg: Error - {str(e)}")
            results["ffmpeg"] = False
        
        # Test Python packages
        print("\n  Testing Critical Python Packages...")
        critical_packages = [
            "fastapi", "uvicorn", "sqlalchemy", "cloudinary", 
            "openai", "moviepy", "opencv-python", "requests"
        ]
        
        package_results = []
        for package in critical_packages:
            try:
                __import__(package.replace("-", "_"))
                print(f"    ✅ {package}: Available")
                package_results.append(True)
            except ImportError:
                print(f"    ❌ {package}: Not installed")
                package_results.append(False)
        
        results["python_packages"] = all(package_results)
        
        return results
    
    def test_backend_health(self) -> Dict[str, Any]:
        """Test backend health endpoint for service status"""
        print("\n🏥 Testing Backend Health Status...")
        
        try:
            response = requests.get(f"{BASE_URL}/health", timeout=10)
            
            if response.status_code == 200:
                health_data = response.json()
                print("    ✅ Backend health endpoint accessible")
                
                print("\n  Service Status from Backend:")
                services = health_data.get("services", {})
                for service, status in services.items():
                    status_icon = "✅" if status else "❌"
                    print(f"    {status_icon} {service}: {status}")
                
                return {
                    "accessible": True,
                    "services": services,
                    "ffmpeg_available": health_data.get("ffmpeg_available", False),
                    "database": health_data.get("database") == "connected"
                }
            else:
                print(f"    ❌ Backend health check failed: {response.status_code}")
                return {"accessible": False}
                
        except Exception as e:
            print(f"    ❌ Backend health check failed: {str(e)}")
            return {"accessible": False}
    
    def run_all_tests(self):
        """Run all environment tests"""
        print("🚀 Starting Environment Configuration Verification...")
        print("="*70)
        
        # Run all tests
        env_results = self.test_environment_variables()
        api_results = self.test_api_service_connectivity()
        system_results = self.test_system_dependencies()
        health_results = self.test_backend_health()
        
        # Generate summary
        print("\n" + "="*70)
        print("📊 ENVIRONMENT VERIFICATION SUMMARY")
        print("="*70)
        
        # Environment variables summary
        required_count = sum(env_results["required"].values())
        total_required = len(env_results["required"])
        optional_count = sum(env_results["optional"].values())
        total_optional = len(env_results["optional"])
        
        print(f"\n📋 Environment Variables:")
        print(f"  Required: {required_count}/{total_required} configured")
        print(f"  Optional: {optional_count}/{total_optional} configured")
        
        if env_results["missing_required"]:
            print(f"  ❌ Missing required: {', '.join(env_results['missing_required'])}")
        
        # API services summary
        api_count = sum(api_results.values())
        total_apis = len(api_results)
        print(f"\n🌐 API Services:")
        print(f"  Connected: {api_count}/{total_apis}")
        
        # System dependencies summary
        system_count = sum(system_results.values())
        total_system = len(system_results)
        print(f"\n🔧 System Dependencies:")
        print(f"  Available: {system_count}/{total_system}")
        
        # Backend health summary
        print(f"\n🏥 Backend Health:")
        if health_results.get("accessible"):
            backend_services = health_results.get("services", {})
            backend_count = sum(backend_services.values())
            backend_total = len(backend_services)
            print(f"  Services: {backend_count}/{backend_total} working")
        else:
            print("  ❌ Backend not accessible")
        
        # Overall assessment
        print(f"\n🎯 Overall Assessment:")
        
        critical_issues = []
        if required_count < total_required:
            critical_issues.append("Missing required environment variables")
        if not system_results.get("ffmpeg", False):
            critical_issues.append("FFmpeg not available")
        if not health_results.get("accessible", False):
            critical_issues.append("Backend not accessible")
        
        if not critical_issues:
            print("  ✅ Environment is properly configured for deployment")
            print("  🚀 Ready for production deployment")
        else:
            print("  ⚠️  Critical issues found:")
            for issue in critical_issues:
                print(f"    • {issue}")
            print("  🔧 Please resolve these issues before deployment")

if __name__ == "__main__":
    tester = EnvironmentTester()
    tester.run_all_tests()
