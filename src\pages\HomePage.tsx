import React, { useState, useEffect, useRef } from 'react';
import { Navigate, Link } from 'react-router-dom';
import DashboardLayout from '@/components/Dashboard';
import { useAuth } from '@/context/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Scissors,
  Upload,
  Zap,
  Gift,
  Link2,
  ArrowUp,
  ArrowDown,
  Video,
  Download,
} from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  getUserSocialPlatforms,
  type UserClip,
  type UserStats,
  type SocialPlatform,
} from '@/services/userService';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';
import { deductSecondsFromUser } from '@/lib/user';
import PaymentRequiredModal from '@/components/PaymentRequiredModal';
interface ProcessedClip {
  url: string;
  start_time: number;
  end_time: number;
  duration: number;
  virality_analysis: {
    score: number;
    feedback: string;
  } | null;
}

const HomePage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const {
    isAuthenticated,
    user,
    isLoading,
    markWelcomeModalAsSeen,
    reloadProfile,
  } = useAuth();
  const [showWelcomeModal, setShowWelcomeModal] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [clips, setClips] = useState<UserClip[]>([]);
  const [stats, setStats] = useState<UserStats>({
    totalViews: 0,
    totalVideos: 0,
    totalClips: 0,
    totalSubscribers: 0,
    watchTime: 0,
    credits: 0,
  });
  const [socialPlatforms, setSocialPlatforms] = useState<SocialPlatform[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [videoUrl, setVideoUrl] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeInputTab, setActiveInputTab] = useState('url');
  const [isUploading, setIsUploading] = useState(false);
  const [selectedName, setSelectedName] = useState<string>('');
  const [clippingTab, setClippingTab] = useState('clip');
  const [clipLength, setClipLength] = useState('180');
  const [progressPercent, setProgressPercent] = useState(0);
  const [progressMessage, setProgressMessage] = useState('Starting job...');
  const [clipMode, setClipMode] = useState('ai_best_edits');

  const [processedVideoResult, setProcessedVideoResult] = useState<{
    clips: ProcessedClip[];
  } | null>(null);

  // --- Refs for intervals to ensure proper cleanup ---
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const userRef = useRef(user); // Ref to hold the latest user object for intervals

  const [userCredits, setUserCredits] = useState<number>(() => {
    const stored = localStorage.getItem('userCredits');
    return stored ? parseFloat(stored) : 0;
  });
  const [paymentErrorOpen, setPaymentErrorOpen] = useState(false);
  const [paymentErrorMsg, setPaymentErrorMsg] = useState('');
  useEffect(() => {
    if (typeof user?.credits === 'number') {
      setUserCredits(user.credits);
      localStorage.setItem('userCredits', String(user.credits));
    }
  }, [user?.credits]);

  const handleGoVip = () => {
    setPaymentErrorOpen(false);
    navigate('/payment');
  };

  /** 2) First load: ensure we have fresh profile (optional but recommended) */
  useEffect(() => {
    if (!isLoading && user?.id) {
      // fetch latest credits from DB into context; fetchProfile already updates localStorage
      // reloadProfile?.();
    }
  }, [isLoading, user?.id, reloadProfile]);

  useEffect(() => {
    userRef.current = user;
  }, [user]);

  // --- Helper Functions for State Management ---

  const clearJobState = () => {
    if (pollingIntervalRef.current) clearInterval(pollingIntervalRef.current);
    if (progressIntervalRef.current) clearInterval(progressIntervalRef.current);
    pollingIntervalRef.current = null;
    progressIntervalRef.current = null;
    localStorage.removeItem('processingVideoId');
    setIsProcessing(false);
  };

  const startFakeProgress = () => {
    if (progressIntervalRef.current) return; // Prevent multiple intervals
    progressIntervalRef.current = setInterval(() => {
      setProgressPercent((p) => Math.min(p + 1, 90)); // Tick up, but cap at 90%
    }, 2000);
  };

  const stopFakeProgress = () => {
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
  };

  // --- Main Polling Logic ---

  // const pollForJobStatus = () => {
  //   if (pollingIntervalRef.current) clearInterval(pollingIntervalRef.current);

  //   pollingIntervalRef.current = setInterval(async () => {
  //     const processingId = localStorage.getItem("processingVideoId");
  //     const currentUser = userRef.current; // Use the ref to get the latest user

  //     if (!processingId || !currentUser) {
  //       // If ID is gone or user logs out, stop everything.
  //       clearJobState();
  //       setProgressPercent(0);
  //       return;
  //     }

  //     try {
  //       const response = await fetch(
  //         `${import.meta.env.VITE_API_URL}/processing-status/${processingId}`
  //       );

  //       if (!response.ok) {
  //         throw new Error("Could not check job status. Server might be busy.");
  //       }

  //       const data = await response.json();

  //       switch (data.status) {
  //         case "pending":
  //           setProgressMessage("Job is queued...");
  //           break;
  //         case "processing":
  //           setProgressMessage("Processing video... this may take a moment.");
  //           break;
  //         case "failed":
  //           stopFakeProgress();
  //           setError(
  //             data.error_message || "Processing failed with an unknown error."
  //           );
  //           toast({
  //             title: "Processing Failed",
  //             description:
  //               data.error_message || "Please check the console for details.",
  //             variant: "destructive",
  //           });
  //           clearJobState();
  //           setProgressPercent(0);
  //           break;
  //         case "completed":
  //           stopFakeProgress();
  //           setProgressMessage("Job completed!");
  //           setProgressPercent(100);

  //           const finalClips = data.results?.clips || [];
  //           if (finalClips.length > 0) {
  //             const clipsToInsert = finalClips.map((clip: ProcessedClip) => ({
  //               user_id: currentUser.id,
  //               url: clip.url,
  //               text: "AI-generated clip.",
  //               start_time: clip.start_time,
  //               end_time: clip.end_time,
  //               score: clip.virality_analysis?.score ?? 0,
  //               feedback: clip.virality_analysis?.feedback ?? "No feedback.",
  //               platform: "Cloudinary",
  //             }));

  //             await supabase.from("myclip").insert(clipsToInsert);
  //             setProcessedVideoResult({ clips: finalClips });
  //             setActiveTab("result");
  //             toast({
  //               title: "Success!",
  //               description: "Your clips are ready and saved to your library.",
  //             });
  //             fetchAllUserData();
  //           } else {
  //             toast({
  //               title: "Processing Warning",
  //               description: "The job finished, but no clips were generated.",
  //               variant: "destructive",
  //             });
  //           }

  //           clearJobState();
  //           setTimeout(() => setProgressPercent(0), 5000);
  //           break;
  //       }
  //     } catch (err: any) {
  //       setError(err.message);
  //       stopFakeProgress();
  //       clearJobState();
  //       setProgressPercent(0);
  //     }
  //   }, 10000); // Check every 10 seconds
  // };

  const pollForJobStatus = () => {
    if (pollingIntervalRef.current) clearInterval(pollingIntervalRef.current);

    pollingIntervalRef.current = setInterval(async () => {
      const processingId = localStorage.getItem('processingVideoId');
      const currentUser = userRef.current; // latest user

      if (!processingId || !currentUser) {
        clearJobState();
        setProgressPercent(0);
        return;
      }

      try {
        const response = await fetch(
          `${import.meta.env.VITE_API_URL}/processing-status/${processingId}`
        );
        if (!response.ok) {
          throw new Error('Could not check job status. Server might be busy.');
        }

        const data = await response.json();

        switch (data?.status) {
          case 'pending':
            setProgressMessage('Job is queued...');
            break;

          case 'processing':
            setProgressMessage('Processing video... this may take a moment.');
            break;

          case 'failed':
            stopFakeProgress();
            setError(
              data?.error_message || 'Processing failed with an unknown error.'
            );
            toast({
              title: 'Processing Failed',
              description:
                data?.error_message || 'Please check the console for details.',
              variant: 'destructive',
            });
            clearJobState();
            navigate('/', { replace: true });
            setProgressPercent(0);
            break;

          case 'completed': {
            stopFakeProgress();
            setProgressMessage('Job completed!');
            setProgressPercent(100);
            reloadProfile?.();

            const finalClips: any[] = Array.isArray(data?.results?.clips)
              ? data.results.clips
              : [];

            // ---------- DEDUCT CREDITS (inline, once per job) ----------
            try {
              const chargedKey = `charged:${processingId}`;
              const alreadyCharged = localStorage.getItem(chargedKey) === '1';

              if (!alreadyCharged) {
                // Prefer clip.duration; fallback to end_time - start_time
                const totalSeconds = finalClips.reduce((sum, clip) => {
                  const d = Number(clip?.duration);
                  if (!Number.isNaN(d) && d > 0) return sum + Math.ceil(d);
                  const s = Number(clip?.start_time) || 0;
                  const e = Number(clip?.end_time) || 0;
                  const delta = e - s;
                  return sum + (delta > 0 ? Math.ceil(delta) : 0);
                }, 0);

                if (totalSeconds > 0) {
                  const ok = await deductSecondsFromUser(
                    currentUser.id,
                    totalSeconds
                  );
                  if (!ok) {
                    toast({
                      title: 'Insufficient credits',
                      description:
                        'Your job finished, but we couldn’t deduct credits. Please add credits and try again.',
                      variant: 'destructive',
                    });
                    clearJobState();
                    setProgressPercent(0);
                    return; // do not save clips if not charged
                  }
                  localStorage.setItem(chargedKey, '1'); // mark charged (idempotent)
                  fetchAllUserData?.(); // any local UI refresh you already have

                  // NEW: reload profile so AuthContext (credits/seconds) updates immediately
                  await reloadProfile?.(); // <-- refresh profile in context + localStorage
                }
              } else {
                // Optional: if you want to ensure context is fresh even when already charged:
                await reloadProfile?.(); // safe no-op if already up-to-date
              }
            } catch (chargeErr: any) {
              toast({
                title: 'Charging failed',
                description:
                  chargeErr?.message ||
                  'Could not deduct credits for this job.',
                variant: 'destructive',
              });
              clearJobState();
              setProgressPercent(0);
              return;
            }
            // ---------- END DEDUCT CREDITS ----------

            if (finalClips.length > 0) {
              const clipsToInsert = finalClips.map((clip: any) => ({
                user_id: currentUser.id,
                url: clip.url,
                text: 'AI-generated clip.',
                start_time: clip.start_time,
                end_time: clip.end_time,
                duration:
                  Number(clip?.duration) && Number(clip.duration) > 0
                    ? Number(clip.duration)
                    : Math.max(
                        (Number(clip?.end_time) || 0) -
                          (Number(clip?.start_time) || 0),
                        0
                      ),
                score: clip?.virality_analysis?.score ?? 0,
                feedback: clip?.virality_analysis?.feedback ?? 'No feedback.',
                platform: clip?.platform ?? 'Cloudinary',
              }));

              await supabase.from('myclip').insert(clipsToInsert);

              setProcessedVideoResult({ clips: finalClips });
              setActiveTab('result');
              toast({
                title: 'Success!',
                description: 'Your clips are ready and saved to your library.',
              });
              fetchAllUserData?.();
            } else {
              toast({
                title: 'Processing Warning',
                description: 'The job finished, but no clips were generated.',
                variant: 'destructive',
              });
            }

            clearJobState();
            setTimeout(() => setProgressPercent(0), 5000);
            break;
          }

          default:
            // Unknown status
            break;
        }
      } catch (err: any) {
        setError(err?.message || 'Something went wrong while polling.');
        stopFakeProgress();
        clearJobState();
        setProgressPercent(0);
      }
    }, 10000); // check every 10s
  };

  // --- Component Effects ---

  // On mount, check if a job needs to be resumed
  useEffect(() => {
    const processingId = localStorage.getItem('processingVideoId');

    // BUG FIX: More robust check for a valid ID.
    // This prevents starting the progress bar if the value is null, undefined, or the literal string "undefined".
    if (
      processingId &&
      processingId !== 'null' &&
      processingId !== 'undefined'
    ) {
      setIsProcessing(true);
      setProgressMessage('Resuming status check for ongoing job...');
      // UX IMPROVEMENT: Immediately start the progress bar for a better resume experience.
      setProgressPercent(1);
      startFakeProgress();
      pollForJobStatus();
    }

    // Cleanup intervals on component unmount
    return () => {
      if (pollingIntervalRef.current) clearInterval(pollingIntervalRef.current);
      if (progressIntervalRef.current)
        clearInterval(progressIntervalRef.current);
    };
  }, []);

  useEffect(() => {
    if (isAuthenticated && user) {
      fetchAllUserData();
      if (user && !user.seen_welcome) {
        setShowWelcomeModal(true);
        markWelcomeModalAsSeen();
      }
    }
  }, [isAuthenticated, user, markWelcomeModalAsSeen]);

  // --- Data Fetching and Event Handlers ---

  const handleUpload = async (file: File) => {
    // ... (This function is fine, no changes needed)
    if (!file) return;
    setIsUploading(true);
    setError(null);
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('upload_preset', 'smartclips');
      formData.append(
        'credits',
        JSON.parse(localStorage.getItem('userProfile'))?.credits || 0
      );

      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/compress-and-upload/`,
        { method: 'POST', body: formData }
      );
      // if (!response.ok) {
      //   const errorData = await response.json();
      //   throw new Error(errorData.detail || 'Processing failed on the server.');
      // }
      if (response.status === 402) {
        let errorData: any = {};
        try {
          errorData = await response.json();
        } catch {}
        const msg =
          errorData?.detail ||
          errorData?.message ||
          'Payment required. Please upgrade to continue.';

        setPaymentErrorMsg(msg);
        setPaymentErrorOpen(true);

        stopFakeProgress();
        clearJobState();
        setIsProcessing(false);
        setProgressPercent(0);
        return;
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.detail ||
            errorData.message ||
            'Failed to submit processing job.'
        );
      }
      const result = await response.json();
      setVideoUrl(result.url);
      toast({
        title: 'Upload Successful',
        description: 'Your video was compressed and uploaded.',
      });
      setActiveInputTab('url');
    } catch (err: any) {
      setError(err.message);
      toast({
        title: 'Upload Failed',
        description: err.message,
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) handleUpload(file);
  };

  const handleGetClips = async () => {
    if (!videoUrl) {
      setError('Please provide a video URL or upload a file.');
      return;
    }

    setError(null);
    setIsProcessing(true);
    setProcessedVideoResult(null);
    setProgressMessage('Submitting job...');
    setProgressPercent(1); // Start progress bar immediately
    startFakeProgress();

    try {
      const norm = (v: any) =>
        String(v ?? '')
          .trim()
          .toLowerCase();
      const isNoSubtitle =
        norm(selectedName) === 'none-subtitle' ||
        norm(selectedName) === 'no subtitle' ||
        norm(selectedName) === 'none' ||
        norm(selectedName) === 'no_subtitle';
      const payload = {
        video_url: videoUrl,
        options: {
          // add_subtitles: true,
          ...(isNoSubtitle
            ? { add_subtitles: false, subtitle_style: selectedName }
            : { add_subtitles: true, subtitle_style: selectedName }),
          create_short_form: clippingTab === 'clip',
          // subtitle_style: selectedName,
          max_short_clips: 5,
          chunk_duration_seconds: parseInt(clipLength, 10),
          clipmode: clipMode,
          platforms: ['tiktok'],
          add_emojis: false,
          add_watermark:
            JSON.parse(localStorage.getItem('userProfile'))?.subscription ===
            'free',
          credits: JSON.parse(localStorage.getItem('userProfile'))?.credits,
        },
      };

      const formData = new FormData();
      formData.append('request', JSON.stringify(payload));

      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/advanced-process`,
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('auth_token')}`,
          },
          body: formData,
        }
      );

      // if (!response.ok) {
      //   const errorData = await response.json();
      //   throw new Error(errorData.detail || 'Failed to submit processing job.');
      // }
      if (response.status === 402) {
        let errorData: any = {};
        try {
          errorData = await response.json();
        } catch {}
        const msg =
          errorData?.detail ||
          errorData?.message ||
          'Payment required. Please upgrade to continue.';

        setPaymentErrorMsg(msg);
        setPaymentErrorOpen(true);

        stopFakeProgress();
        clearJobState();
        setIsProcessing(false);
        setProgressPercent(0);
        return;
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.detail ||
            errorData.message ||
            'Failed to submit processing job.'
        );
      }
      const data = await response.json();
      if (!data.success || !data.video_id) {
        throw new Error(
          data.message || 'Failed to get a valid job ID from the server.'
        );
      }
      toast({
        title: 'Job Submitted!',
        description: 'Your video is now being processed.',
      });

      localStorage.setItem('processingVideoId', data.video_id.toString());
      // LOGIC FIX: Call pollForJobStatus without arguments, as it reads from localStorage.
      pollForJobStatus();
    } catch (err: any) {
      const errorMessage = err.message || 'An unknown error occurred.';
      setError(errorMessage);
      toast({
        title: 'Submission Failed',
        description: errorMessage,
        variant: 'destructive',
      });
      stopFakeProgress();
      // STATE FIX: Ensure all job state is cleared on submission failure.
      clearJobState();
      setProgressPercent(0);
    }
  };

  const fetchAllUserData = async () => {
    // ... (This function is fine, no changes needed)
    if (!user) return;
    setIsLoadingData(true);
    try {
      const {
        data: rawClips,
        error: clipsError,
        count,
      } = await supabase
        .from('myclip')
        .select('id, url, created_at, platform', { count: 'exact' })
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(20);

      if (clipsError) throw clipsError;
      const formattedClips: UserClip[] = (rawClips || []).map((clip) => ({
        id: clip.id,
        url: clip.url,
        title: `Clip from ${new Date(clip.created_at).toLocaleDateString()}`,
        status: 'published',
        duration: '0:30',
        platform: clip.platform || 'Cloudinary',
        views: 0,
        likes: 0,
        comments: 0,
        createdAt: new Date(clip.created_at).toLocaleDateString(),
      }));
      setClips(formattedClips);
      setStats((prev) => ({
        ...prev,
        totalClips: count || 0,
        credits: user.credits || 0,
      }));
      const platformsData = await getUserSocialPlatforms();
      setSocialPlatforms(platformsData);
    } catch (error) {
      console.error('Error fetching user data:', error);
    } finally {
      setIsLoadingData(false);
    }
  };

  // --- Render Logic ---
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }
  if (!isAuthenticated) return <Navigate to="/landing" />;

  const handleSelect = (name: string) => {
    setSelectedName(name);
  };

  // ... (Rest of your component's return JSX is fine, pasting it below for completeness)
  const performanceStats = [
    {
      title: 'Clips Generated',
      value: stats.totalClips.toString(),
      trend: '',
      icon: Scissors,
      color: 'text-purple-500',
    },
    {
      title: 'Credits Available',
      value: parseFloat(userCredits.toFixed(2)),
      change: 'Available',
      trend: 'neutral' as const,
      icon: Zap,
      color: 'text-yellow-500',
    },
  ];

  interface FontStyle {
    id: string;
    name: string;
    image: string;
  }

  const fontStyles: FontStyle[] = [
    {
      id: 'karaoke_pop',
      name: 'Karaoke Pop',
      image: '/preset-fancy-Karaoke-preview.png',
    },
    {
      id: 'popline',
      name: 'Popline',
      image: '/preset-fancy-Popline-preview.png',
    },
    {
      id: 'viral_style',
      name: 'Simple',
      image: '/preset-fancy-Simple-preview.png',
    },
    {
      id: 'glitch_zoom',
      name: 'Glitch Zoom',
      image: '/glitch-infinite-zoom-preview.png',
    },
    { id: 'beasty', name: 'Beasty', image: '/preset-fancy-Beasty-preview.png' },

    { id: 'none-subtitle', name: 'No Subtitle', image: '/none.png' },
  ];

  const buttons = [
    {
      label: 'Let it AI',
      description: 'AI chooses best edits',
      status: 'ai_best_edits',
      gradient: 'from-purple-600 to-blue-500',
    },
    {
      label: 'Podcast',
      description: 'Zooms in on people with subtitles',
      status: 'podcast_mode',
      gradient: 'from-green-600 to-teal-500',
    },
    {
      label: 'Streamer',
      description: 'Only enhancements',
      status: 'streamer_mode',
      gradient: 'from-pink-600 to-orange-500',
    },
  ];

  const handleDownload = async (url: string, title: string) => {
    try {
      const downloadUrl = url.replace('/upload/', '/upload/fl_attachment/');

      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `${title.replace(/\s+/g, '-')}.mp4`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: 'Download started',
        description: `Downloading "${title}"...`,
      });

      // Optional: Update download count locally (if you pass clipId)
      // setClips(...)
    } catch (error) {
      console.error('Download error:', error);
      toast({
        title: 'Download failed',
        description: 'Failed to download the clip',
        variant: 'destructive',
      });
    }
  };
  return (
    <DashboardLayout>
      <Dialog open={showWelcomeModal} onOpenChange={setShowWelcomeModal}>
        <DialogContent className="sm:max-w-md text-center">
          <DialogHeader>
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100 mb-4">
              <Gift className="h-6 w-6 text-green-600" aria-hidden="true" />
            </div>
            <DialogTitle className="text-2xl font-semibold">
              Welcome to SmartClips!
            </DialogTitle>
            <DialogDescription className="mt-2 text-base text-muted-foreground">
              We're excited to have you on board! To get you started, we've
              added<strong> 10 free credits</strong> to your account. Enjoy
              creating!
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="sm:justify-center">
            <Button type="button" onClick={() => setShowWelcomeModal(false)}>
              Get Started
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="flex flex-col items-center justify-center min-h-screen w-full">
        <div className=" w-full   p-6 sm:p-6 rounded-xl shadow-2xl border ">
          {/* ... Rest of your JSX is fine, only the result rendering part needs update ... */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 mb-4 rounded-full bg-gradient-to-br from-purple-600 to-blue-500">
              <Zap className="w-8 h-8 " />
            </div>
            <h2 className="text-2xl font-bold ">Create a New Clip</h2>
            <p className=" mt-1">
              Paste a URL or upload a video to get started.
            </p>
          </div>

          <Tabs
            value={activeInputTab}
            onValueChange={setActiveInputTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-2  border-gray-700">
              <TabsTrigger value="url">From URL </TabsTrigger>
              <TabsTrigger value="upload">Upload File</TabsTrigger>
            </TabsList>
            <TabsContent value="url" className="mt-4">
              <div className="relative">
                <Link2 className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 " />
                <Input
                  placeholder="https://x.com/user/status/12345"
                  className="pl-12 h-14  border-2 border-gray-700 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-base  placeholder:text-gray-500 rounded-lg"
                  value={videoUrl}
                  onChange={(e) => setVideoUrl(e.target.value)}
                  disabled={isProcessing || isUploading}
                />
              </div>
            </TabsContent>
            <TabsContent value="upload" className="mt-4">
              <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-600 border-dashed rounded-lg cursor-pointer  ">
                <div className="flex flex-col items-center justify-center text-center">
                  {isUploading ? (
                    <>
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mb-2"></div>
                      <p className="text-sm text-gray-400">Uploading...</p>
                    </>
                  ) : (
                    <>
                      <Upload className="w-8 h-8 mb-2 " />
                      <p className="text-sm ">
                        <span className="font-semibold">Click to upload</span>{' '}
                        or drag & drop
                      </p>
                      <p className="text-xs ">MP4, MOV (MAX. 500MB)</p>
                    </>
                  )}
                </div>
                <input
                  id="file-upload"
                  type="file"
                  className="hidden"
                  onChange={handleFileChange}
                  accept="video/mp4,video/quicktime,video/avi"
                  disabled={isUploading || isProcessing}
                />
              </label>
            </TabsContent>
          </Tabs>
          <div className="grid gap-4 md:grid-cols-3 my-4">
            {buttons.map((btn) => {
              const isActive = clipMode === btn.status;
              return (
                <Button
                  key={btn.status}
                  className={`w-full h-14 text-lg font-bold bg-gradient-to-r ${
                    btn.gradient
                  } text-white hover:opacity-90 disabled:opacity-70 flex items-center justify-center ${
                    isActive ? 'ring-1 ring-offset-2 ring-blue-400' : ''
                  }`}
                  onClick={() => setClipMode(btn.status)}
                  disabled={isProcessing || isUploading}
                >
                  {btn.label}
                </Button>
              );
            })}
          </div>
          <Tabs
            value={clippingTab}
            onValueChange={setClippingTab}
            className="w-full my-3"
          >
            <TabsList className="grid w-full grid-cols-2 border border-gray-700 mb-4">
              <TabsTrigger value="clip" disabled={isProcessing}>
                AI Clipping
              </TabsTrigger>
              <TabsTrigger value="no-clip" disabled={isProcessing}>
                Don't Clip
              </TabsTrigger>
            </TabsList>

            <TabsContent value="clip">
              <div className=" p-4 border border-gray-700 rounded-lg">
                <div className="grid grid-cols-1 sm:grid-cols-1 gap-4">
                  <div className="flex flex-col items-start  w-full">
                    <label className="block text-sm font-medium  mb-1">
                      Clip Length
                    </label>
                    <select
                      value={clipLength}
                      onChange={(e) => setClipLength(e.target.value)}
                      className="w-full p-2 rounded-md bg-[#2c2c2c] text-white border border-gray-600"
                      disabled={isProcessing}
                    >
                      <option value="15">15 sec less</option>
                      <option value="30">30 sec less</option>
                      <option value="60">1 min less</option>
                      <option value="180">3 mins less</option>
                      <option value="300">5 mins less</option>
                      <option value="600">10 mins less</option>
                      <option value="900">15 mins less</option>
                    </select>
                  </div>
                </div>

                {(progressPercent > 0 || isProcessing) && (
                  <div className="my-2 flex flex-col items-start w-full">
                    <div className="flex justify-between w-full mb-1">
                      <label className="block text-sm font-medium">
                        Processing Status
                      </label>
                      <p className="text-sm text-gray-400">{progressMessage}</p>
                    </div>
                    <div className="w-full h-4 bg-gray-700 rounded-md overflow-hidden">
                      <div
                        className="h-full bg-gradient-to-r from-purple-600 to-blue-500 transition-all duration-500 ease-in-out"
                        style={{ width: `${progressPercent}%` }}
                      />
                    </div>
                    <div className="flex items-center justify-end text-sm text-white w-full">
                      <p>{Math.round(progressPercent)}%</p>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="no-clip"></TabsContent>
          </Tabs>
          {(clipMode === 'podcast_mode' || clipMode === 'ai_best_edits') && (
            <div
              className="grid gap-2 rounded-[10px] mt-[15px] 
             grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-6 xl:grid-cols-6"
            >
              {fontStyles.map((style) => {
                const isSelected = selectedName === style.id;
                return (
                  <div
                    key={style.id}
                    onClick={() => !isProcessing && handleSelect(style.id)}
                    className={`flex flex-col items-center gap-2 p-2 border rounded-lg transition-all duration-200 ease-in-out ${
                      isProcessing
                        ? 'cursor-not-allowed opacity-50'
                        : 'cursor-pointer'
                    } ${
                      isSelected
                        ? 'border-2 border-blue-500 shadow-lg'
                        : 'border-gray-700'
                    }`}
                  >
                    <img
                      src={style.image}
                      alt={style.name}
                      className="w-full h-20 object-fill rounded"
                    />
                    <span className="font-medium text-xs">{style.name}</span>
                  </div>
                );
              })}
            </div>
          )}

          {error ? (
            <p className="text-sm text-red-500 text-center mt-4">{error}</p>
          ) : userCredits === 0 ? (
            <p className="text-sm text-red-500 text-center mt-4">
              You have <strong>0</strong> credits. Please{' '}
              <Link to="/credits" className="underline">
                purchase credits
              </Link>{' '}
              first.
            </p>
          ) : null}
          <DialogFooter className="mt-8">
            <Button
              className="w-full h-14 text-lg font-bold bg-gradient-to-r from-purple-600 to-blue-500 text-white hover:opacity-90 disabled:opacity-70"
              onClick={handleGetClips}
              disabled={isProcessing || isUploading || userCredits === 0}
            >
              {isProcessing || isUploading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                  {isProcessing ? 'Processing...' : 'Uploading...'}
                </>
              ) : (
                <>
                  <Scissors className="w-5 h-5 mr-2" />
                  Get Clips
                </>
              )}
            </Button>
          </DialogFooter>
        </div>
      </div>

      <div className="space-y-6 mt-12">
        <div>
          <h1 className="text-xl md:text-2xl font-semibold">
            Channel dashboard
          </h1>
          <p className="text-sm md:text-base text-muted-foreground">
            Welcome back, {user?.username || 'Creator'}! Here's your content.
          </p>
        </div>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4">
          {performanceStats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-3 md:p-6">
                <div className="flex items-center justify-between mb-2 md:mb-4">
                  <div
                    className={`p-1.5 md:p-2 rounded-lg ${
                      stat.color
                        .replace('text-', 'bg-')
                        .replace('-500', '-50 dark:bg-') + '-900/20'
                    }`}
                  >
                    <stat.icon
                      className={`h-4 w-4 md:h-5 md:w-5 ${stat.color}`}
                    />
                  </div>
                  {stat.trend !== 'neutral' && stat.trend !== '' && (
                    <div
                      className={`flex items-center text-xs ${
                        stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                      }`}
                    >
                      {stat.trend === 'up' ? (
                        <ArrowUp className="h-3 w-3 mr-1" />
                      ) : (
                        <ArrowDown className="h-3 w-3 mr-1" />
                      )}
                      <span className="hidden sm:inline">{stat.change}</span>
                    </div>
                  )}
                </div>
                <div>
                  <p className="text-lg md:text-2xl font-bold mb-1">
                    {stat.value}
                  </p>
                  <p className="text-xs md:text-sm text-muted-foreground">
                    {stat.title}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-4 md:space-y-6"
        >
          <TabsList className="grid w-full grid-cols-2 h-auto p-1">
            <TabsTrigger value="overview" className="text-xs sm:text-sm py-2">
              Overview
            </TabsTrigger>
            {processedVideoResult && (
              <TabsTrigger
                value="result"
                className="text-xs sm:text-sm py-2 flex items-center gap-2"
              >
                <Video className="h-4 w-4" />
                Latest Result
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div>
              <div className="flex items-center justify-between mb-3 md:mb-4">
                <h2 className="text-lg md:text-xl font-semibold">
                  Latest uploads
                </h2>
                <Link to="/clip-results">
                  <Button variant="outline" size="sm">
                    View all
                  </Button>
                </Link>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4">
                {clips.length > 0 ? (
                  clips.slice(0, 4).map((clip) => (
                    <Card
                      key={clip.id}
                      className="group hover:shadow-md transition-all"
                    >
                      <div className="relative">
                        <video
                          src={clip.url}
                          className="w-full aspect-video rounded-t-lg object-cover"
                          poster={clip.thumbnail}
                          controls
                          playsInline
                          preload="metadata"
                        />
                        {/* Download button (mobile par hamesha dikhayein) */}

                        <Button
                          variant="outline"
                          className="absolute h-8 right-2 top-2 rounded-md bg-black/60 px-2 text-[12px] text-white backdrop-blur
             hover:bg-black/70 md:hidden"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDownload(clip.url, clip.title || 'clip');
                          }}
                        >
                          <Download className="h-3 w-3 mr-1" />
                        </Button>
                      </div>
                      <CardContent className="p-3">
                        <h3 className="font-medium text-sm line-clamp-2">
                          {clip.title}
                        </h3>
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  <p className="col-span-4 text-muted-foreground text-center py-4">
                    No clips found. Create one above to get started!
                  </p>
                )}
              </div>
            </div>
          </TabsContent>

          {processedVideoResult && (
            <TabsContent value="result">
              <Card>
                <CardHeader className="flex items-start">
                  <CardTitle>Your Processed Clips</CardTitle>
                  <p className="text-sm text-muted-foreground mt-4">
                    These clips have been processed and saved to your library.
                  </p>
                </CardHeader>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 px-3 my-1">
                  {processedVideoResult?.clips.length > 0 ? (
                    processedVideoResult.clips.map((clip, index) => (
                      <div key={index}>
                        <p className="font-semibold mb-2 flex items-start px-2">
                          Clip {index + 1}
                        </p>
                        <Card className="group hover:shadow-md transition-all">
                          <div className="relative">
                            <video
                              src={clip.url}
                              className="w-full aspect-video rounded-t-lg object-cover"
                              // poster={clip.thumbnail}
                              controls
                              playsInline
                              preload="metadata"
                            />
                            {/* Download button (mobile par hamesha dikhayein) */}

                            <Button
                              variant="outline"
                              className="absolute h-8 right-2 top-2 rounded-md bg-black/60 px-2 text-[12px] text-white backdrop-blur
                                hover:bg-black/70 md:hidden"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDownload(clip.url, clip.title || 'clip');
                              }}
                            >
                              <Download className="h-3 w-3 mr-1" />
                            </Button>
                          </div>
                          {/* <CardContent className="p-3">
          <h3 className="font-medium text-sm line-clamp-2">{clip.title}</h3>
        </CardContent> */}
                        </Card>
                      </div>
                    ))
                  ) : (
                    <p className="col-span-4 text-muted-foreground text-center py-4">
                      No clips found. Create one above to get started!
                    </p>
                  )}
                </div>
              </Card>
            </TabsContent>
          )}
        </Tabs>
      </div>
      <div className=" w-full">
        <PaymentRequiredModal
          open={paymentErrorOpen}
          onOpenChange={setPaymentErrorOpen}
          title="Upgrade Required"
          description="To continue processing your video, please upgrade to VIP."
          errorMessage={paymentErrorMsg} // text ab modal ke andar aayega, wrap bhi hoga
          primaryButtonText="Go to VIP" // alag se button niche
          onAction={handleGoVip}
          showCancel={true}
        />
      </div>
    </DashboardLayout>
  );
};

export default HomePage;
