import React from "react";
import { Link } from "react-router-dom";
import {
  X,
  LogIn,
  UserPlus,
  Scissors,
  Image,
  BookOpen,
  Film,
  User,
  VideoIcon,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import ThemeToggle from "./ThemeToggle";
import { useAuth } from "@/context/AuthContext";

interface MobileNavProps {
  isOpen: boolean;
  onClose: () => void;
}

const MobileNav: React.FC<MobileNavProps> = ({ isOpen, onClose }) => {
  const { isAuthenticated, logout } = useAuth();

  if (!isOpen) return null;

  const handleLogout = async () => {
    try {
      await logout();
      onClose();
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  return (
    <div className="fixed inset-0 bg-background z-50 flex flex-col p-6 animate-fade-in">
      <div className="flex justify-between bg-background">
        <span className="font-bold text-lg gradient-text">SmartClips.io</span>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <X className="h-6 w-6" />
        </Button>
      </div>

      <nav className="flex flex-col items-start gap-6 text-lg bg-background">
        <Link
          to="/smart-clipper"
          className="flex items-center gap-2 text-foreground/80 hover:text-foreground transition-colors"
          onClick={onClose}
        >
          <Scissors className="h-4 w-4" />
          <span>SmartClipper</span>
        </Link>
        <Link
          to="/clip-results"
          className="flex items-center gap-2 text-foreground/80 hover:text-foreground transition-colors"
          onClick={onClose}
        >
          <VideoIcon className="h-4 w-4" />
          <span>MyClips</span>
        </Link>
        {/* <Link
          to="/avatar-creator"
          className="flex items-center gap-2 text-foreground/80 hover:text-foreground transition-colors"
          onClick={onClose}
        >
          <Image className="h-4 w-4" />
          <span>Create Avatar</span>
        </Link> */}
        {/* <Link
          to="/script-generator"
          className="flex items-center gap-2 text-foreground/80 hover:text-foreground transition-colors"
          onClick={onClose}
        >
          <BookOpen className="h-4 w-4" />
          <span>Write Scripts</span>
        </Link> */}
        {/* <Link
          to="/video-generator"
          className="flex items-center gap-2 text-foreground/80 hover:text-foreground transition-colors"
          onClick={onClose}
        >
          <Film className="h-4 w-4" />
          <span>Generate Videos</span>
        </Link> */}
      </nav>

      <div className="flex items-center bg-background">
        <ThemeToggle />
      </div>

      {isAuthenticated ? (
        <div className="space-y-3 bg-background">
          <Link to="/profile" onClick={onClose} className="block w-full">
            <Button variant="outline" className="w-full">
              <User className="h-4 w-4 mr-2" />
              Profile
            </Button>
          </Link>
          <Link to="/analytics" onClick={onClose} className="block w-full">
            <Button className="w-full">Analytics</Button>
          </Link>
          <Button
            variant="ghost"
            className="w-full text-muted-foreground"
            onClick={handleLogout}
          >
            Sign out
          </Button>
        </div>
      ) : (
        <div className="space-y-3 bg-background">
          <Link to="/login" onClick={onClose} className="block w-full">
            <Button variant="outline" className="w-full">
              <LogIn className="h-4 w-4 mr-2" />
              Sign in
            </Button>
          </Link>
          <Link
            to="/register"
            onClick={onClose}
            className="block w-full bg-background"
          >
            <Button className="w-full bg-gradient-purple-blue hover:opacity-90">
              <UserPlus className="h-4 w-4 mr-2" />
              Sign up
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
};

export default MobileNav;
