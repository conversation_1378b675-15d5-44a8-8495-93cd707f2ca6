"""
Video Template System Integration

This module integrates with existing SmartClips video processing templates
and provides AI-powered template application based on content analysis.
"""

import os
import logging
from typing import Dict, Any, List, Optional
import json

from advanced_video_processor import AdvancedVideoProcessor
from gpt_editor_service import GPTEditorService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TemplateSystemIntegration:
    """Integrates AI video editor with existing template system"""
    
    def __init__(self, openai_api_key: str = None, temp_dir: str = None):
        self.openai_api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
        self.temp_dir = temp_dir or "temp"
        
        # Initialize existing video processor
        self.video_processor = AdvancedVideoProcessor(
            openai_api_key=self.openai_api_key,
            temp_dir=self.temp_dir
        )
        
        # Initialize GPT editor service
        self.gpt_service = GPTEditorService(openai_api_key=self.openai_api_key)
        
        # Enhanced template configurations that integrate with existing system
        self.enhanced_templates = {
            "podcast": {
                "name": "Professional Podcast",
                "description": "Clean, professional podcast video with speaker focus",
                "ai_analysis_prompt": "Analyze this video for podcast content. Focus on speaker segments, audio quality, and professional presentation needs.",
                "processing_options": {
                    "add_subtitles": True,
                    "add_emojis": False,
                    "subtitle_style": "clean_modern",
                    "create_short_form": True,
                    "platforms": ["youtube", "spotify"],
                    "use_ai_features": True
                },
                "post_processing": [
                    {"action": "color_correction", "parameters": {"brightness": 1.05, "contrast": 1.1}},
                    {"action": "enhance_audio", "parameters": {"normalize": True}},
                    {"action": "add_watermark", "parameters": {"position": "bottom_right", "opacity": 0.6}}
                ],
                "target_aspect_ratios": ["16:9", "1:1"],
                "recommended_duration": {"min": 30, "max": 300}
            },
            
            "gaming": {
                "name": "Gaming Highlights",
                "description": "Dynamic gaming content with vibrant effects and highlights",
                "ai_analysis_prompt": "Analyze this gaming video for exciting moments, action sequences, and highlight-worthy content.",
                "processing_options": {
                    "add_subtitles": True,
                    "add_emojis": True,
                    "subtitle_style": "viral_style",
                    "create_short_form": True,
                    "platforms": ["tiktok", "youtube", "twitch"],
                    "use_ai_features": True,
                    "max_short_clips": 5
                },
                "post_processing": [
                    {"action": "add_effects", "parameters": {"saturation": 1.3, "contrast": 1.2}},
                    {"action": "add_text", "parameters": {"text": "EPIC MOMENT", "position": "top", "style": "gaming"}},
                    {"action": "adjust_speed", "parameters": {"speed": 1.1}}
                ],
                "target_aspect_ratios": ["16:9", "9:16"],
                "recommended_duration": {"min": 15, "max": 60}
            },
            
            "educational": {
                "name": "Educational Content",
                "description": "Clear, informative educational videos with enhanced readability",
                "ai_analysis_prompt": "Analyze this educational content for key concepts, important information, and learning moments.",
                "processing_options": {
                    "add_subtitles": True,
                    "add_emojis": True,
                    "subtitle_style": "clean_modern",
                    "create_short_form": True,
                    "platforms": ["youtube", "instagram"],
                    "use_ai_features": True
                },
                "post_processing": [
                    {"action": "color_correction", "parameters": {"brightness": 1.1, "contrast": 1.05}},
                    {"action": "add_text", "parameters": {"text": "Learn More", "position": "bottom", "style": "educational"}}
                ],
                "target_aspect_ratios": ["16:9", "1:1"],
                "recommended_duration": {"min": 30, "max": 180}
            },
            
            "social_media": {
                "name": "Social Media Optimized",
                "description": "Engaging social media content with viral elements",
                "ai_analysis_prompt": "Analyze this content for viral potential, engaging moments, and social media optimization opportunities.",
                "processing_options": {
                    "add_subtitles": True,
                    "add_emojis": True,
                    "subtitle_style": "viral_style",
                    "create_short_form": True,
                    "platforms": ["tiktok", "instagram", "youtube"],
                    "use_ai_features": True,
                    "max_short_clips": 3
                },
                "post_processing": [
                    {"action": "crop", "parameters": {"aspect_ratio": "9:16"}},
                    {"action": "add_effects", "parameters": {"saturation": 1.2, "brightness": 1.05}},
                    {"action": "add_text", "parameters": {"text": "Swipe for more!", "position": "bottom", "style": "social"}}
                ],
                "target_aspect_ratios": ["9:16", "1:1"],
                "recommended_duration": {"min": 15, "max": 60}
            },
            
            "business": {
                "name": "Business Professional",
                "description": "Professional business content with corporate styling",
                "ai_analysis_prompt": "Analyze this business content for professional presentation, key messages, and corporate communication needs.",
                "processing_options": {
                    "add_subtitles": True,
                    "add_emojis": False,
                    "subtitle_style": "clean_modern",
                    "create_short_form": False,
                    "platforms": ["linkedin", "youtube"],
                    "use_ai_features": True
                },
                "post_processing": [
                    {"action": "color_correction", "parameters": {"contrast": 1.1, "saturation": 0.95}},
                    {"action": "add_watermark", "parameters": {"position": "bottom_right", "opacity": 0.8}}
                ],
                "target_aspect_ratios": ["16:9"],
                "recommended_duration": {"min": 60, "max": 600}
            }
        }

    async def apply_template(self, video_path: str, output_dir: str, 
                           template_name: str, custom_options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Apply a template to a video using the existing processing pipeline
        
        Args:
            video_path: Path to input video
            output_dir: Directory for output files
            template_name: Name of template to apply
            custom_options: Optional custom processing options
            
        Returns:
            Processing results dictionary
        """
        template = self.enhanced_templates.get(template_name.lower())
        if not template:
            raise ValueError(f"Template '{template_name}' not found")
        
        logger.info(f"Applying template '{template_name}' to video: {video_path}")
        
        # Merge template options with custom options
        processing_options = template["processing_options"].copy()
        if custom_options:
            processing_options.update(custom_options)
        
        try:
            # Use existing advanced video processor
            results = self.video_processor.process_video_comprehensive(
                video_path=video_path,
                output_dir=output_dir,
                options=processing_options
            )
            
            # Apply post-processing effects if specified
            if template.get("post_processing") and results.get("status") == "Success":
                results = await self._apply_post_processing_effects(
                    results, template["post_processing"], output_dir
                )
            
            # Add template metadata to results
            results["template_applied"] = template_name
            results["template_description"] = template["description"]
            
            return results
            
        except Exception as e:
            logger.error(f"Error applying template '{template_name}': {e}")
            return {
                "status": "Error",
                "message": f"Template application failed: {str(e)}",
                "template_applied": template_name
            }

    async def analyze_and_recommend_template(self, video_path: str) -> Dict[str, Any]:
        """
        Analyze video content and recommend the best template
        
        Args:
            video_path: Path to video file
            
        Returns:
            Dictionary with recommended template and analysis
        """
        if not self.gpt_service.client:
            # Fallback recommendation based on video properties
            return self._fallback_template_recommendation(video_path)
        
        try:
            # Extract basic video info
            from ffmpeg_command_generator import FFmpegCommandGenerator
            ffmpeg_gen = FFmpegCommandGenerator()
            video_info = ffmpeg_gen.get_video_info(video_path)
            
            # Get transcript for content analysis
            transcript_data = self.video_processor.extract_transcript_with_timing(video_path)
            transcript_text = " ".join([segment.get("text", "") for segment in transcript_data])
            
            # Create analysis prompt
            prompt = f"""Analyze this video content and recommend the best template:

Video Information:
- Duration: {video_info.get('duration', 0)} seconds
- Resolution: {video_info.get('width', 0)}x{video_info.get('height', 0)}
- Has Audio: {video_info.get('has_audio', False)}

Transcript Sample: {transcript_text[:500]}...

Available Templates:
{json.dumps({name: info['description'] for name, info in self.enhanced_templates.items()}, indent=2)}

Provide a JSON response with:
- recommended_template: The best template name
- confidence: Float between 0.0 and 1.0
- reasoning: Why this template was chosen
- alternative_templates: List of other suitable templates
"""

            response = await self.gpt_service.client.chat.completions.create(
                model="gpt-4-turbo-preview",
                messages=[
                    {"role": "system", "content": "You are a video content analysis expert. Respond only with valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.3,
                response_format={"type": "json_object"}
            )
            
            analysis = json.loads(response.choices[0].message.content)
            
            # Validate recommended template exists
            recommended = analysis.get("recommended_template", "").lower()
            if recommended not in self.enhanced_templates:
                recommended = "social_media"  # Default fallback
            
            return {
                "recommended_template": recommended,
                "confidence": analysis.get("confidence", 0.7),
                "reasoning": analysis.get("reasoning", "AI analysis completed"),
                "alternative_templates": analysis.get("alternative_templates", []),
                "video_analysis": {
                    "duration": video_info.get('duration', 0),
                    "aspect_ratio": f"{video_info.get('width', 0)}:{video_info.get('height', 0)}",
                    "has_audio": video_info.get('has_audio', False),
                    "content_type": self._detect_content_type(transcript_text)
                }
            }
            
        except Exception as e:
            logger.error(f"Error in AI template recommendation: {e}")
            return self._fallback_template_recommendation(video_path)

    def _fallback_template_recommendation(self, video_path: str) -> Dict[str, Any]:
        """Fallback template recommendation when AI is not available"""
        try:
            from ffmpeg_command_generator import FFmpegCommandGenerator
            ffmpeg_gen = FFmpegCommandGenerator()
            video_info = ffmpeg_gen.get_video_info(video_path)
            
            duration = video_info.get('duration', 0)
            width = video_info.get('width', 0)
            height = video_info.get('height', 0)
            
            # Simple rule-based recommendation
            if duration > 300:  # Long videos
                recommended = "podcast"
            elif height > width:  # Vertical videos
                recommended = "social_media"
            elif duration < 60:  # Short videos
                recommended = "social_media"
            else:
                recommended = "educational"
            
            return {
                "recommended_template": recommended,
                "confidence": 0.6,
                "reasoning": "Rule-based recommendation (AI not available)",
                "alternative_templates": ["social_media", "educational"],
                "video_analysis": {
                    "duration": duration,
                    "aspect_ratio": f"{width}:{height}",
                    "has_audio": video_info.get('has_audio', False),
                    "content_type": "unknown"
                }
            }
            
        except Exception as e:
            logger.error(f"Error in fallback recommendation: {e}")
            return {
                "recommended_template": "social_media",
                "confidence": 0.3,
                "reasoning": "Default recommendation due to analysis error",
                "alternative_templates": [],
                "video_analysis": {}
            }

    def _detect_content_type(self, transcript: str) -> str:
        """Detect content type from transcript"""
        transcript_lower = transcript.lower()
        
        gaming_keywords = ["game", "play", "level", "score", "win", "lose", "boss", "character"]
        educational_keywords = ["learn", "teach", "explain", "understand", "concept", "theory", "study"]
        business_keywords = ["business", "company", "strategy", "market", "profit", "revenue", "client"]
        podcast_keywords = ["welcome", "today", "discuss", "interview", "guest", "episode"]
        
        keyword_counts = {
            "gaming": sum(1 for word in gaming_keywords if word in transcript_lower),
            "educational": sum(1 for word in educational_keywords if word in transcript_lower),
            "business": sum(1 for word in business_keywords if word in transcript_lower),
            "podcast": sum(1 for word in podcast_keywords if word in transcript_lower)
        }
        
        if max(keyword_counts.values()) > 0:
            return max(keyword_counts, key=keyword_counts.get)
        
        return "general"

    async def _apply_post_processing_effects(self, results: Dict[str, Any], 
                                           post_effects: List[Dict[str, Any]], 
                                           output_dir: str) -> Dict[str, Any]:
        """Apply post-processing effects to the processed video"""
        # This would integrate with the FFmpeg command generator
        # For now, we'll log the effects that would be applied
        logger.info(f"Post-processing effects to apply: {post_effects}")
        
        # In a full implementation, this would:
        # 1. Take the output from the main processing
        # 2. Apply additional FFmpeg commands for post-processing
        # 3. Update the results with the final output
        
        return results

    def get_available_templates(self) -> Dict[str, Dict[str, Any]]:
        """Get all available templates with their configurations"""
        return {
            name: {
                "name": config["name"],
                "description": config["description"],
                "target_platforms": config["processing_options"]["platforms"],
                "recommended_duration": config.get("recommended_duration", {}),
                "target_aspect_ratios": config.get("target_aspect_ratios", [])
            }
            for name, config in self.enhanced_templates.items()
        }

    def validate_template_compatibility(self, video_path: str, template_name: str) -> Dict[str, Any]:
        """Check if a video is compatible with a specific template"""
        template = self.enhanced_templates.get(template_name.lower())
        if not template:
            return {"compatible": False, "reason": "Template not found"}
        
        try:
            from ffmpeg_command_generator import FFmpegCommandGenerator
            ffmpeg_gen = FFmpegCommandGenerator()
            video_info = ffmpeg_gen.get_video_info(video_path)
            
            duration = video_info.get('duration', 0)
            recommended_duration = template.get("recommended_duration", {})
            
            compatibility_issues = []
            
            # Check duration compatibility
            if recommended_duration:
                min_dur = recommended_duration.get("min", 0)
                max_dur = recommended_duration.get("max", float('inf'))
                
                if duration < min_dur:
                    compatibility_issues.append(f"Video too short (minimum {min_dur}s recommended)")
                elif duration > max_dur:
                    compatibility_issues.append(f"Video too long (maximum {max_dur}s recommended)")
            
            # Check audio requirement
            if not video_info.get('has_audio') and template["processing_options"].get("add_subtitles"):
                compatibility_issues.append("Template requires audio for subtitles, but video has no audio")
            
            return {
                "compatible": len(compatibility_issues) == 0,
                "issues": compatibility_issues,
                "recommendations": self._get_compatibility_recommendations(compatibility_issues)
            }
            
        except Exception as e:
            logger.error(f"Error checking template compatibility: {e}")
            return {"compatible": True, "reason": "Unable to verify compatibility"}

    def _get_compatibility_recommendations(self, issues: List[str]) -> List[str]:
        """Get recommendations to fix compatibility issues"""
        recommendations = []
        
        for issue in issues:
            if "too short" in issue:
                recommendations.append("Consider using a different template or adding more content")
            elif "too long" in issue:
                recommendations.append("Consider trimming the video or using a different template")
            elif "no audio" in issue:
                recommendations.append("Add audio track or use a template that doesn't require subtitles")
        
        return recommendations
