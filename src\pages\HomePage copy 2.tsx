import React, { useState, useEffect } from "react";
import { Navigate, Link } from "react-router-dom";
import DashboardLayout from "@/components/Dashboard";
import { useAuth } from "@/context/AuthContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Scissors,
  Play,
  Upload,
  Plus,
  Search,
  CheckCircle,
  Zap,
  Gift,
  Link2,
  Check,
  ArrowUp,
  ArrowDown,
  Video,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  getUserSocialPlatforms,
  type UserClip,
  type UserStats,
  type SocialPlatform,
} from "@/services/userService";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Toggle } from "@/components/ui/toggle";
import { supabase } from "@/lib/supabase";
import { toast } from "@/hooks/use-toast";

const HomePage = () => {
  const { isAuthenticated, user, isLoading, markWelcomeModalAsSeen } =
    useAuth();
  const [showWelcomeModal, setShowWelcomeModal] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");
  const [searchQuery, setSearchQuery] = useState("");
  const [clips, setClips] = useState<UserClip[]>([]);
  const [stats, setStats] = useState<UserStats>({
    totalViews: 0,
    totalVideos: 0,
    totalClips: 0,
    totalSubscribers: 0,
    watchTime: 0,
    credits: 0,
  });
  const [socialPlatforms, setSocialPlatforms] = useState<SocialPlatform[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [addEmojis, setAddEmojis] = useState(false);
  const [videoUrl, setVideoUrl] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeInputTab, setActiveInputTab] = useState("url");
  const [isUploading, setIsUploading] = useState(false);

  // State to hold the result for the new UI tab
  const [processedVideoResult, setProcessedVideoResult] = useState<{
    url: string;
  } | null>(null);

  useEffect(() => {
    if (isAuthenticated && user && !user.seen_welcome) {
      setShowWelcomeModal(true);
      markWelcomeModalAsSeen();
    }
  }, [user, isAuthenticated, markWelcomeModalAsSeen]);

  useEffect(() => {
    if (isAuthenticated && user) {
      fetchAllUserData();
    }
  }, [isAuthenticated, user]);

  // In HomePage.tsx

  const handleUpload = async (file: File) => {
    if (!file) return;
    setIsUploading(true);
    setError(null);

    try {
      const formData = new FormData();
      // The key 'file' must match the FastAPI parameter name
      formData.append("file", file);

      // Send the original file to your new backend endpoint
      // IMPORTANT: Update with your actual API URL if different
      const response = await fetch(
        "http://localhost:8000/compress-and-upload/",
        {
          method: "POST",
          body: formData,
        }
      );

      if (!response.ok) {
        // Get error details from your backend for better feedback
        const errorData = await response.json();
        throw new Error(errorData.detail || "Processing failed on the server.");
      }

      const result = await response.json();
      const compressedUrl = result.url;

      // Set the URL state with the final, compressed video URL from Cloudinary
      setVideoUrl(compressedUrl);

      toast({
        title: "Upload Successful",
        description: "Your video was compressed and uploaded.",
      });
      setActiveInputTab("url");
    } catch (err: any) {
      setError(err.message);
      toast({
        title: "Upload Failed",
        description: err.message,
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) handleUpload(file);
  };

  const handleGetClips = async () => {
    if (!videoUrl) {
      setError("Please provide a video URL or upload a file.");
      return;
    }
    setError(null);
    setIsProcessing(true);
    setProcessedVideoResult(null); // Clear previous result

    try {
      const payload = {
        video_url: videoUrl,
        options: {
          add_subtitles: true,
          add_emojis: addEmojis,
          create_short_form: true,
          platforms: ["tiktok"],
          subtitle_style: "glitch_zoom", //1. viral_style / clean_modern 2. karaoke_pop done 3. popline 4. simple 5. glitch_zoom 6. beasty done
          max_short_clips: 5,
        },
      };
      const formData = new FormData();
      formData.append("request", JSON.stringify(payload));

      const response = await fetch("http://localhost:8000/advanced-process", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Processing failed");
      }

      const data = await response.json();
      if (!data.success || !data.processed_videos?.final_output) {
        throw new Error(
          data.message ||
            "Failed to retrieve processed video from API response."
        );
      }

      // Set state to show the new video player tab and switch to it
      setProcessedVideoResult({ url: data.processed_videos.final_output });
      setActiveTab("result");

      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session?.user?.id)
        throw new Error("User not authenticated for saving data.");

      const transcript = data.metadata?.transcript;
      if (!transcript || transcript.length === 0) {
        throw new Error(
          "Transcript data missing in API response, cannot save clip details."
        );
      }

      // Construct a single record for the database
      const insertData = {
        user_id: session.user.id,
        url: data.processed_videos.final_output,
        text: transcript.map((t: { word: string }) => t.word).join(" "),
        start_time: transcript[0].start,
        end_time: transcript[transcript.length - 1].end,
        score: 0,
        feedback: "None",
        platform: "Cloudinary",
      };

      const { error: insertError } = await supabase
        .from("myclip")
        .insert([insertData]);
      if (insertError) throw insertError;

      toast({
        title: "Success!",
        description: "Video processed and saved. See the result below.",
      });

      // Refresh the library in the background
      fetchAllUserData();
    } catch (err: any) {
      const errorMessage = err.message || "An unknown error occurred.";
      setError(errorMessage);
      toast({
        title: "Processing Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const fetchAllUserData = async () => {
    if (!user) return;
    setIsLoadingData(true);
    try {
      const {
        data: rawClips,
        error: clipsError,
        count,
      } = await supabase
        .from("myclip")
        .select("id, url, created_at, platform", { count: "exact" })
        .eq("user_id", user.id)
        .order("created_at", { ascending: false })
        .limit(20);

      if (clipsError) throw clipsError;

      const formattedClips: UserClip[] = (rawClips || []).map((clip) => ({
        id: clip.id,
        thumbnail: clip.url.trim().replace(/\.mp4$/, ".jpg"),
        title: `Clip from ${new Date(clip.created_at).toLocaleDateString()}`,
        status: "published",
        duration: "0:30",
        platform: clip.platform || "Cloudinary",
        views: 0,
        likes: 0,
        comments: 0,
        createdAt: new Date(clip.created_at).toLocaleDateString(),
      }));

      setClips(formattedClips);
      setStats((prev) => ({
        ...prev,
        totalClips: count || 0,
        credits: user.credits || 0,
      }));

      const platformsData = await getUserSocialPlatforms();
      setSocialPlatforms(platformsData);
    } catch (error) {
      console.error("Error fetching user data:", error);
    } finally {
      setIsLoadingData(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!isAuthenticated) return <Navigate to="/landing" />;

  const performanceStats = [
    {
      title: "Clips Generated",
      value: stats.totalClips.toString(),
      trend: "",
      icon: Scissors,
      color: "text-purple-500",
    },
    {
      title: "Credits Available",
      value: stats.credits.toString(),
      change: "Available",
      trend: "neutral" as const,
      icon: Zap,
      color: "text-yellow-500",
    },
  ];

  return (
    <DashboardLayout>
      <Dialog open={showWelcomeModal} onOpenChange={setShowWelcomeModal}>
        <DialogContent className="sm:max-w-md text-center">
          <DialogHeader>
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100 mb-4">
              <Gift className="h-6 w-6 text-green-600" aria-hidden="true" />
            </div>
            <DialogTitle className="text-2xl font-semibold">
              Welcome to SmartClips!
            </DialogTitle>
            <DialogDescription className="mt-2 text-base text-muted-foreground">
              We're excited to have you on board! To get you started, we've
              added<strong> 10 free credits</strong> to your account. Enjoy
              creating!
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="sm:justify-center">
            <Button type="button" onClick={() => setShowWelcomeModal(false)}>
              Get Started
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="flex flex-col items-center justify-center min-h-screen w-full">
        <div className="sm:max-w-md w-full bg-[#1c1c1c] text-white p-6 sm:p-8 rounded-xl shadow-2xl border border-gray-800">
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 mb-4 rounded-full bg-gradient-to-br from-purple-600 to-blue-500">
              <Zap className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-gray-100">
              Create a New Clip
            </h2>
            <p className="text-gray-400 mt-1">
              Paste a URL or upload a video to get started.
            </p>
          </div>
          <Tabs
            value={activeInputTab}
            onValueChange={setActiveInputTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-2 bg-[#2d2d2d] border-gray-700">
              <TabsTrigger value="url">From URL</TabsTrigger>
              <TabsTrigger value="upload">Upload File</TabsTrigger>
            </TabsList>
            <TabsContent value="url" className="mt-4">
              <div className="relative">
                <Link2 className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-500" />
                <Input
                  placeholder="https://x.com/user/status/12345"
                  className="pl-12 h-14 bg-[#2d2d2d] border-2 border-gray-700 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-base text-white placeholder:text-gray-500 rounded-lg"
                  value={videoUrl}
                  onChange={(e) => setVideoUrl(e.target.value)}
                />
              </div>
            </TabsContent>
            <TabsContent value="upload" className="mt-4">
              <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-600 border-dashed rounded-lg cursor-pointer bg-[#2d2d2d] hover:bg-gray-800">
                <div className="flex flex-col items-center justify-center text-center">
                  {isUploading ? (
                    <>
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mb-2"></div>
                      <p className="text-sm text-gray-400">Uploading...</p>
                    </>
                  ) : (
                    <>
                      <Upload className="w-8 h-8 mb-2 text-gray-500" />
                      <p className="text-sm text-gray-400">
                        <span className="font-semibold text-purple-400">
                          Click to upload
                        </span>{" "}
                        or drag & drop
                      </p>
                      <p className="text-xs text-gray-500">
                        MP4, MOV (MAX. 500MB)
                      </p>
                    </>
                  )}
                </div>
                <input
                  id="file-upload"
                  type="file"
                  className="hidden"
                  onChange={handleFileChange}
                  accept="video/mp4,video/quicktime,video/avi"
                  disabled={isUploading}
                />
              </label>
            </TabsContent>
          </Tabs>
          <div className="space-y-6 mt-6">
            <Toggle
              pressed={addEmojis}
              onPressedChange={setAddEmojis}
              className="group w-full h-14 flex items-center justify-between p-4 rounded-lg bg-[#2d2d2d] border-2 border-transparent data-[state=on]:border-purple-500 data-[state=on]:bg-[#2d2d2d]"
            >
              <div className="flex items-center">
                <span className="text-3xl mr-3">✨</span>
                <div>
                  <span className="font-semibold text-white">
                    AI Emoji Captions
                  </span>
                  <p className="text-xs text-gray-400">
                    Automatically add relevant emojis
                  </p>
                </div>
              </div>
              <div className="w-6 h-6 flex items-center justify-center rounded-full border-2 border-gray-600 data-[state=on]:border-purple-500 data-[state=on]:bg-purple-600">
                <Check className="h-4 w-4 text-white opacity-0 transition-opacity duration-200 group-data-[state=on]:opacity-100" />
              </div>
            </Toggle>
          </div>
          {error && (
            <p className="text-sm text-red-500 text-center mt-4">{error}</p>
          )}
          <DialogFooter className="mt-8">
            <Button
              className="w-full h-14 text-lg font-bold bg-gradient-to-r from-purple-600 to-blue-500 text-white hover:opacity-90 disabled:opacity-70"
              onClick={handleGetClips}
              disabled={isProcessing || isUploading}
            >
              {isProcessing || isUploading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                  {isProcessing ? "Processing..." : "Uploading..."}
                </>
              ) : (
                <>
                  <Scissors className="w-5 h-5 mr-2" />
                  Get Clips
                </>
              )}
            </Button>
          </DialogFooter>
        </div>
      </div>

      <div className="space-y-6 mt-12">
        <div>
          <h1 className="text-xl md:text-2xl font-semibold">
            Channel dashboard
          </h1>
          <p className="text-sm md:text-base text-muted-foreground">
            Welcome back, {user?.username || "Creator"}! Here's your content.
          </p>
        </div>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4">
          {performanceStats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-3 md:p-6">
                <div className="flex items-center justify-between mb-2 md:mb-4">
                  <div
                    className={`p-1.5 md:p-2 rounded-lg ${
                      stat.color
                        .replace("text-", "bg-")
                        .replace("-500", "-50 dark:bg-") + "-900/20"
                    }`}
                  >
                    <stat.icon
                      className={`h-4 w-4 md:h-5 md:w-5 ${stat.color}`}
                    />
                  </div>
                  {stat.trend !== "neutral" && stat.trend !== "" && (
                    <div
                      className={`flex items-center text-xs ${
                        stat.trend === "up" ? "text-green-600" : "text-red-600"
                      }`}
                    >
                      {stat.trend === "up" ? (
                        <ArrowUp className="h-3 w-3 mr-1" />
                      ) : (
                        <ArrowDown className="h-3 w-3 mr-1" />
                      )}
                      <span className="hidden sm:inline">{stat.change}</span>
                    </div>
                  )}
                </div>
                <div>
                  <p className="text-lg md:text-2xl font-bold mb-1">
                    {stat.value}
                  </p>
                  <p className="text-xs md:text-sm text-muted-foreground">
                    {stat.title}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-4 md:space-y-6"
        >
          <TabsList className="grid w-full grid-cols-2 h-auto p-1">
            <TabsTrigger value="overview" className="text-xs sm:text-sm py-2">
              Overview
            </TabsTrigger>
            {processedVideoResult && (
              <TabsTrigger
                value="result"
                className="text-xs sm:text-sm py-2 flex items-center gap-2"
              >
                <Video className="h-4 w-4" />
                Latest Result
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div>
              <div className="flex items-center justify-between mb-3 md:mb-4">
                <h2 className="text-lg md:text-xl font-semibold">
                  Latest uploads
                </h2>
                <Link to="/clip-results">
                  <Button variant="outline" size="sm">
                    View all
                  </Button>
                </Link>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4">
                {clips.length > 0 ? (
                  clips.slice(0, 4).map((clip) => (
                    <Card
                      key={clip.id}
                      className="group hover:shadow-md transition-all"
                    >
                      <div className="relative">
                        <img
                          src={clip.thumbnail}
                          alt={clip.title}
                          className="w-full h-32 object-cover"
                        />
                      </div>
                      <CardContent className="p-3">
                        <h3 className="font-medium text-sm line-clamp-2">
                          {clip.title}
                        </h3>
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  <p className="col-span-4 text-muted-foreground text-center py-4">
                    No clips found. Create one above to get started!
                  </p>
                )}
              </div>
            </div>
          </TabsContent>

          {processedVideoResult && (
            <TabsContent value="result">
              <Card>
                <CardHeader>
                  <CardTitle>Your Processed Video</CardTitle>
                </CardHeader>
                <CardContent>
                  <video
                    src={processedVideoResult.url}
                    controls
                    className="w-full rounded-lg aspect-video bg-black"
                  />
                  <p className="text-sm text-muted-foreground mt-4">
                    This video has been processed and saved. You can find it
                    later in your "Latest uploads" or content library.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
          )}
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default HomePage;
