import { serve } from "https://deno.land/std@0.224.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.43.4";
async function verifyStripeSignature({ bodyBuffer, signatureHeader, secret }) {
  const parts = signatureHeader.split(",").map((part) => part.split("="));
  const timestamp = parts.find((part) => part[0] === "t")?.[1];
  const sig = parts.find((part) => part[0] === "v1")?.[1];
  if (!timestamp || !sig) {
    return {
      valid: false,
      error: "Malformed signature header",
    };
  }
  const encoder = new TextEncoder();
  const key = await crypto.subtle.importKey(
    "raw",
    encoder.encode(secret),
    {
      name: "HMAC",
      hash: "SHA-256",
    },
    false,
    [
      "sign",
    ],
  );
  const timestampBuffer = encoder.encode(`${timestamp}.`);
  const signedPayloadBuffer = new Uint8Array(
    timestampBuffer.length + bodyBuffer.byteLength,
  );
  signedPayloadBuffer.set(new Uint8Array(timestampBuffer), 0);
  signedPayloadBuffer.set(new Uint8Array(bodyBuffer), timestampBuffer.length);
  const signatureBuffer = await crypto.subtle.sign(
    "HMAC",
    key,
    signedPayloadBuffer,
  );
  const signatureHex = Array.from(new Uint8Array(signatureBuffer)).map((b) =>
    b.toString(16).padStart(2, "0")
  ).join("");
  if (signatureHex !== sig) {
    return {
      valid: false,
      error: "Signature does not match",
    };
  }
  return {
    valid: true,
  };
}
serve(async (req) => {
  const signature = req.headers.get("Stripe-Signature");
  const signingSecret = Deno.env.get("STRIPE_WEBHOOK_SIGNING_SECRET");
  if (!signature || !signingSecret) {
    return new Response("Stripe signature or secret is missing.", {
      status: 400,
    });
  }
  const bodyBuffer = await req.arrayBuffer();
  const { valid, error: verificationError } = await verifyStripeSignature({
    bodyBuffer,
    signatureHeader: signature,
    secret: signingSecret,
  });
  if (!valid) {
    console.error(
      `Webhook signature verification failed: ${verificationError}`,
    );
    return new Response(`Signature verification failed: ${verificationError}`, {
      status: 400,
    });
  }
  const bodyText = new TextDecoder().decode(bodyBuffer);
  const event = JSON.parse(bodyText);
  // Handle both possible success events
  if (
    event.type === "invoice.paid" || event.type === "invoice.payment_succeeded"
  ) {
    const invoice = event.data.object;
    // Process if it's a recurring payment OR a manually paid one
    if (invoice.billing_reason !== "subscription_create") {
      const customerId = invoice.customer;
      // --- FIX: Get the Product ID from the correct location ---
      const productStripeId = invoice.lines?.data?.[0]?.pricing?.price_details
        ?.product;
      if (!productStripeId) {
        console.warn("Could not find product ID in invoice. Skipping.");
        return new Response(
          JSON.stringify({
            received: true,
            message: "No product ID found.",
          }),
          {
            status: 200,
          },
        );
      }
      console.log(
        `Processing invoice for customer: ${customerId}, Product: ${productStripeId}`,
      );
      const supabaseAdmin = createClient(
        Deno.env.get("SUPABASE_URL"),
        Deno.env.get("SUPABASE_SERVICE_ROLE_KEY"),
      );
      // Find user in DB
      const { data: user, error: userError } = await supabaseAdmin.from(
        "profiles",
      ).select("id, credits").eq("stripe_customer_id", customerId).single();
      if (userError || !user) {
        console.error(
          "User not found for stripe_customer_id:",
          customerId,
          userError,
        );
        return new Response("User not found", {
          status: 404,
        });
      }
      // --- FIX: Use the Product ID to determine the plan ---
      let creditsToAdd = 0;
      // You will need to get your actual Stripe Product IDs (prod_...)
      switch (productStripeId) {
        case "prod_SeuiHgJ0lsq5bQ":
          creditsToAdd = 200;
          break;
        case "prod_SeujTQCdd3Lh38":
          creditsToAdd = 500;
          break;
        case "prod_Seukdqc6XB94g8":
          creditsToAdd = 999999;
          break;
        default:
          console.warn(`Unknown product ID: ${productStripeId}`);
      }
      if (creditsToAdd > 0) {
        const newTotalCredits = (user.credits || 0) + creditsToAdd;
        const { error: updateError } = await supabaseAdmin.from("profiles")
          .update({
            credits: newTotalCredits,
            second: newTotalCredits * 60,
          }).eq("id", user.id);
        if (updateError) {
          console.error("Database update failed:", updateError.message);
          return new Response("Database update failed", {
            status: 500,
          });
        }
        console.log(
          `SUCCESS: Added ${creditsToAdd} credits to user ${user.id}.`,
        );
      }
    }
  }
  return new Response(
    JSON.stringify({
      received: true,
    }),
    {
      status: 200,
    },
  );
});
