#!/usr/bin/env python3
"""
Comprehensive Authentication System Testing
Tests OAuth flows, JWT tokens, and protected routes
"""

import requests
import json
import time
from typing import Optional

BASE_URL = "http://localhost:8000"

class AuthenticationTester:
    def __init__(self):
        self.session = requests.Session()
        self.auth_token = None
        self.test_user = {
            "username": f"testuser_{int(time.time())}",
            "email": f"test_{int(time.time())}@example.com",
            "password": "testpassword123"
        }
        
    def test_user_registration(self) -> bool:
        """Test user registration endpoint"""
        print("🔐 Testing User Registration...")
        
        try:
            response = self.session.post(
                f"{BASE_URL}/users/",
                json=self.test_user
            )
            
            print(f"  Registration Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"  ✅ User registered successfully: {data.get('username')}")
                print(f"  Email: {data.get('email')}")
                print(f"  Subscription: {data.get('subscription')}")
                return True
            elif response.status_code == 400:
                error_data = response.json()
                if "already registered" in error_data.get("detail", ""):
                    print("  ⚠️  User already exists (expected for repeated tests)")
                    return True
                else:
                    print(f"  ❌ Registration failed: {error_data}")
                    return False
            else:
                print(f"  ❌ Registration failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"  ❌ Registration test failed: {str(e)}")
            return False
    
    def test_user_login(self) -> bool:
        """Test user login and JWT token generation"""
        print("\n🔑 Testing User Login...")
        
        try:
            # Try login with form data (OAuth2PasswordRequestForm format)
            login_data = {
                "username": self.test_user["username"],
                "password": self.test_user["password"]
            }
            
            response = self.session.post(
                f"{BASE_URL}/token",
                data=login_data  # Use data instead of json for form data
            )
            
            print(f"  Login Status: {response.status_code}")
            
            if response.status_code == 200:
                token_data = response.json()
                self.auth_token = token_data.get("access_token")
                
                print(f"  ✅ Login successful")
                print(f"  Token Type: {token_data.get('token_type')}")
                print(f"  User ID: {token_data.get('user_id')}")
                print(f"  Username: {token_data.get('username')}")
                print(f"  Subscription: {token_data.get('subscription')}")
                print(f"  Token (first 20 chars): {self.auth_token[:20]}...")
                
                return True
            else:
                error_data = response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
                print(f"  ❌ Login failed: {response.status_code} - {error_data}")
                return False
                
        except Exception as e:
            print(f"  ❌ Login test failed: {str(e)}")
            return False
    
    def test_protected_route_without_auth(self) -> bool:
        """Test accessing protected route without authentication"""
        print("\n🚫 Testing Protected Route Without Auth...")
        
        try:
            response = self.session.get(f"{BASE_URL}/users/me/")
            
            print(f"  Status: {response.status_code}")
            
            if response.status_code == 401:
                print("  ✅ Protected route correctly rejects unauthenticated requests")
                return True
            else:
                print(f"  ❌ Protected route should return 401, got {response.status_code}")
                return False
                
        except Exception as e:
            print(f"  ❌ Protected route test failed: {str(e)}")
            return False
    
    def test_protected_route_with_auth(self) -> bool:
        """Test accessing protected route with valid authentication"""
        print("\n✅ Testing Protected Route With Auth...")
        
        if not self.auth_token:
            print("  ❌ No auth token available")
            return False
        
        try:
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            response = self.session.get(f"{BASE_URL}/users/me/", headers=headers)
            
            print(f"  Status: {response.status_code}")
            
            if response.status_code == 200:
                user_data = response.json()
                print(f"  ✅ Authenticated access successful")
                print(f"  Username: {user_data.get('username')}")
                print(f"  Email: {user_data.get('email')}")
                print(f"  Subscription: {user_data.get('subscription')}")
                return True
            else:
                print(f"  ❌ Authenticated access failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"  ❌ Authenticated route test failed: {str(e)}")
            return False
    
    def test_jwt_token_validation(self) -> bool:
        """Test JWT token validation with invalid token"""
        print("\n🔍 Testing JWT Token Validation...")
        
        try:
            # Test with invalid token
            invalid_headers = {"Authorization": "Bearer invalid_token_here"}
            response = self.session.get(f"{BASE_URL}/users/me/", headers=invalid_headers)
            
            print(f"  Invalid token status: {response.status_code}")
            
            if response.status_code == 401:
                print("  ✅ Invalid token correctly rejected")
                
                # Test with malformed header
                malformed_headers = {"Authorization": "InvalidFormat token"}
                response2 = self.session.get(f"{BASE_URL}/users/me/", headers=malformed_headers)
                
                print(f"  Malformed header status: {response2.status_code}")
                
                if response2.status_code == 401:
                    print("  ✅ Malformed auth header correctly rejected")
                    return True
                else:
                    print(f"  ❌ Malformed header should return 401, got {response2.status_code}")
                    return False
            else:
                print(f"  ❌ Invalid token should return 401, got {response.status_code}")
                return False
                
        except Exception as e:
            print(f"  ❌ JWT validation test failed: {str(e)}")
            return False
    
    def test_user_data_endpoints(self) -> bool:
        """Test various user data endpoints that require authentication"""
        print("\n👤 Testing User Data Endpoints...")
        
        if not self.auth_token:
            print("  ❌ No auth token available")
            return False
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        endpoints_to_test = [
            "/user/clips",
            "/user/stats", 
            "/user/video-count",
            "/user/social-platforms"
        ]
        
        results = []
        
        for endpoint in endpoints_to_test:
            try:
                response = self.session.get(f"{BASE_URL}{endpoint}", headers=headers)
                success = response.status_code in [200, 500]  # 500 might be expected for some endpoints
                
                if response.status_code == 200:
                    print(f"  ✅ {endpoint}: {response.status_code}")
                elif response.status_code == 500:
                    print(f"  ⚠️  {endpoint}: {response.status_code} (server error, but auth worked)")
                else:
                    print(f"  ❌ {endpoint}: {response.status_code}")
                    success = False
                
                results.append(success)
                
            except Exception as e:
                print(f"  ❌ {endpoint}: Exception - {str(e)}")
                results.append(False)
        
        success_rate = sum(results) / len(results)
        print(f"  User data endpoints success rate: {success_rate*100:.1f}%")
        
        return success_rate >= 0.75  # Allow some endpoints to fail
    
    def run_all_tests(self):
        """Run all authentication tests"""
        print("🚀 Starting Comprehensive Authentication Testing...")
        print("="*60)
        
        tests = [
            ("User Registration", self.test_user_registration),
            ("User Login", self.test_user_login),
            ("Protected Route (No Auth)", self.test_protected_route_without_auth),
            ("Protected Route (With Auth)", self.test_protected_route_with_auth),
            ("JWT Token Validation", self.test_jwt_token_validation),
            ("User Data Endpoints", self.test_user_data_endpoints)
        ]
        
        results = []
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"  ❌ {test_name} failed with exception: {str(e)}")
                results.append((test_name, False))
        
        # Summary
        print("\n" + "="*60)
        print("📊 AUTHENTICATION TEST SUMMARY")
        print("="*60)
        
        passed = sum(1 for _, result in results if result)
        total = len(results)
        
        print(f"Tests Passed: {passed}/{total}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        print("\nDetailed Results:")
        for test_name, result in results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"  {status} {test_name}")
        
        if passed == total:
            print("\n🎉 All authentication tests passed!")
            print("✅ Authentication system is working correctly")
        else:
            print(f"\n⚠️  {total - passed} authentication tests failed")
            print("❌ Please review the authentication configuration")

if __name__ == "__main__":
    tester = AuthenticationTester()
    tester.run_all_tests()
