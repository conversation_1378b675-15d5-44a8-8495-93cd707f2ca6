"""
Comprehensive Pre-Deployment Verification Script for SmartClips
Tests all API endpoints, authentication, integrations, and deployment readiness
"""

import requests
import json
import os
import time
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path
import tempfile
import subprocess

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pre_deployment_verification.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PreDeploymentVerifier:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.auth_token = None
        self.test_user = {
            "username": "test_user_deploy",
            "email": "<EMAIL>",
            "password": "TestPassword123!"
        }
        self.results = {
            "health_checks": {},
            "authentication": {},
            "api_endpoints": {},
            "integrations": {},
            "environment": {},
            "deployment_readiness": {}
        }

    def log_test(self, category: str, test_name: str, success: bool, details: str = ""):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} [{category}] {test_name}: {details}")
        
        if category not in self.results:
            self.results[category] = {}
        self.results[category][test_name] = {
            "success": success,
            "details": details,
            "timestamp": time.time()
        }

    def test_health_endpoints(self):
        """Test basic health check endpoints"""
        logger.info("🔍 Testing Health Check Endpoints")
        
        # Test root endpoint
        try:
            response = self.session.get(f"{self.base_url}/")
            success = response.status_code == 200
            details = f"Status: {response.status_code}, Response: {response.json() if success else response.text}"
            self.log_test("health_checks", "root_endpoint", success, details)
        except Exception as e:
            self.log_test("health_checks", "root_endpoint", False, str(e))

        # Test health endpoint
        try:
            response = self.session.get(f"{self.base_url}/health")
            success = response.status_code == 200
            if success:
                health_data = response.json()
                details = f"FFmpeg: {health_data.get('ffmpeg_available')}, Services: {health_data.get('services', {})}"
            else:
                details = f"Status: {response.status_code}"
            self.log_test("health_checks", "health_endpoint", success, details)
        except Exception as e:
            self.log_test("health_checks", "health_endpoint", False, str(e))

    def test_authentication_system(self):
        """Test authentication and user management"""
        logger.info("🔐 Testing Authentication System")
        
        # Test user creation
        try:
            response = self.session.post(
                f"{self.base_url}/users/",
                json=self.test_user
            )
            success = response.status_code in [200, 201, 400]  # 400 if user already exists
            details = f"Status: {response.status_code}"
            if response.status_code == 400:
                details += " (User already exists - OK)"
            self.log_test("authentication", "user_creation", success, details)
        except Exception as e:
            self.log_test("authentication", "user_creation", False, str(e))

        # Test token generation
        try:
            response = self.session.post(
                f"{self.base_url}/token",
                data={
                    "username": self.test_user["username"],
                    "password": self.test_user["password"]
                }
            )
            success = response.status_code == 200
            if success:
                token_data = response.json()
                self.auth_token = token_data.get("access_token")
                self.session.headers.update({"Authorization": f"Bearer {self.auth_token}"})
                details = f"Token obtained, Type: {token_data.get('token_type')}"
            else:
                details = f"Status: {response.status_code}, Response: {response.text}"
            self.log_test("authentication", "token_generation", success, details)
        except Exception as e:
            self.log_test("authentication", "token_generation", False, str(e))

        # Test protected endpoint access
        if self.auth_token:
            try:
                response = self.session.get(f"{self.base_url}/users/me/")
                success = response.status_code == 200
                details = f"Status: {response.status_code}"
                if success:
                    user_data = response.json()
                    details += f", User: {user_data.get('username')}"
                self.log_test("authentication", "protected_endpoint_access", success, details)
            except Exception as e:
                self.log_test("authentication", "protected_endpoint_access", False, str(e))

    def test_video_processing_endpoints(self):
        """Test video processing endpoints"""
        logger.info("🎬 Testing Video Processing Endpoints")
        
        # Test URL validation
        try:
            response = self.session.post(
                f"{self.base_url}/validate-url",
                json={"url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ"}
            )
            success = response.status_code == 200
            details = f"Status: {response.status_code}"
            if success:
                validation_data = response.json()
                details += f", Valid: {validation_data.get('is_valid')}, Platform: {validation_data.get('platform')}"
            self.log_test("api_endpoints", "url_validation", success, details)
        except Exception as e:
            self.log_test("api_endpoints", "url_validation", False, str(e))

        # Test URL metadata
        try:
            response = self.session.post(
                f"{self.base_url}/url-metadata",
                json={"url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ"}
            )
            success = response.status_code == 200
            details = f"Status: {response.status_code}"
            if success:
                metadata = response.json()
                details += f", Title: {metadata.get('title', 'N/A')[:50]}..."
            self.log_test("api_endpoints", "url_metadata", success, details)
        except Exception as e:
            self.log_test("api_endpoints", "url_metadata", False, str(e))

    def test_user_data_endpoints(self):
        """Test user data endpoints"""
        logger.info("👤 Testing User Data Endpoints")
        
        if not self.auth_token:
            self.log_test("api_endpoints", "user_data_endpoints", False, "No auth token available")
            return

        endpoints_to_test = [
            ("/user/clips", "user_clips"),
            ("/user/stats", "user_stats"),
            ("/user/video-count", "user_video_count"),
            ("/user/social-platforms", "user_social_platforms")
        ]

        for endpoint, test_name in endpoints_to_test:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}")
                success = response.status_code == 200
                details = f"Status: {response.status_code}"
                if success:
                    data = response.json()
                    if isinstance(data, dict):
                        details += f", Keys: {list(data.keys())}"
                    elif isinstance(data, list):
                        details += f", Count: {len(data)}"
                self.log_test("api_endpoints", test_name, success, details)
            except Exception as e:
                self.log_test("api_endpoints", test_name, False, str(e))

    def test_editor_endpoints(self):
        """Test GPT editor endpoints"""
        logger.info("✏️ Testing Editor Endpoints")
        
        if not self.auth_token:
            self.log_test("api_endpoints", "editor_endpoints", False, "No auth token available")
            return

        # Test get templates
        try:
            response = self.session.get(f"{self.base_url}/api/gpt-editor/templates")
            success = response.status_code == 200
            details = f"Status: {response.status_code}"
            if success:
                templates = response.json().get("templates", [])
                details += f", Templates count: {len(templates)}"
            self.log_test("api_endpoints", "gpt_editor_templates", success, details)
        except Exception as e:
            self.log_test("api_endpoints", "gpt_editor_templates", False, str(e))

        # Test get presets
        try:
            response = self.session.get(f"{self.base_url}/api/gpt-editor/presets")
            success = response.status_code == 200
            details = f"Status: {response.status_code}"
            if success:
                presets = response.json().get("presets", [])
                details += f", Presets count: {len(presets)}"
            self.log_test("api_endpoints", "gpt_editor_presets", success, details)
        except Exception as e:
            self.log_test("api_endpoints", "gpt_editor_presets", False, str(e))

    def test_environment_variables(self):
        """Test environment variable configuration"""
        logger.info("🌍 Testing Environment Configuration")
        
        # Test through health endpoint
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                health_data = response.json()
                services = health_data.get("services", {})
                
                for service, configured in services.items():
                    self.log_test("environment", f"{service}_configured", configured, 
                                f"{service} API key {'configured' if configured else 'missing'}")
            else:
                self.log_test("environment", "env_check_via_health", False, 
                            f"Health endpoint failed: {response.status_code}")
        except Exception as e:
            self.log_test("environment", "env_check_via_health", False, str(e))

    def test_cors_configuration(self):
        """Test CORS configuration"""
        logger.info("🌐 Testing CORS Configuration")
        
        try:
            # Test preflight request
            response = self.session.options(
                f"{self.base_url}/health",
                headers={
                    "Origin": "http://localhost:3000",
                    "Access-Control-Request-Method": "GET",
                    "Access-Control-Request-Headers": "Content-Type"
                }
            )
            success = response.status_code in [200, 204]
            cors_headers = {
                "Access-Control-Allow-Origin": response.headers.get("Access-Control-Allow-Origin"),
                "Access-Control-Allow-Methods": response.headers.get("Access-Control-Allow-Methods"),
                "Access-Control-Allow-Headers": response.headers.get("Access-Control-Allow-Headers")
            }
            details = f"Status: {response.status_code}, CORS headers: {cors_headers}"
            self.log_test("deployment_readiness", "cors_configuration", success, details)
        except Exception as e:
            self.log_test("deployment_readiness", "cors_configuration", False, str(e))

    def test_static_file_serving(self):
        """Test static file serving"""
        logger.info("📁 Testing Static File Serving")
        
        try:
            response = self.session.get(f"{self.base_url}/static/")
            # Static directory listing might return 404 or 403, which is OK
            success = response.status_code in [200, 403, 404]
            details = f"Status: {response.status_code}"
            self.log_test("deployment_readiness", "static_file_serving", success, details)
        except Exception as e:
            self.log_test("deployment_readiness", "static_file_serving", False, str(e))

    def generate_report(self):
        """Generate comprehensive test report"""
        logger.info("📊 Generating Test Report")
        
        total_tests = 0
        passed_tests = 0
        
        report = ["=" * 80, "SMARTCLIPS PRE-DEPLOYMENT VERIFICATION REPORT", "=" * 80, ""]
        
        for category, tests in self.results.items():
            report.append(f"📋 {category.upper().replace('_', ' ')}")
            report.append("-" * 40)
            
            category_passed = 0
            category_total = len(tests)
            
            for test_name, result in tests.items():
                status = "✅ PASS" if result["success"] else "❌ FAIL"
                report.append(f"  {status} {test_name}: {result['details']}")
                
                if result["success"]:
                    category_passed += 1
                    passed_tests += 1
                total_tests += 1
            
            report.append(f"  Category Score: {category_passed}/{category_total}")
            report.append("")
        
        report.extend([
            "=" * 80,
            f"OVERALL SCORE: {passed_tests}/{total_tests} ({(passed_tests/total_tests*100):.1f}%)",
            "=" * 80
        ])
        
        # Deployment readiness assessment
        if passed_tests / total_tests >= 0.8:
            report.append("🎉 DEPLOYMENT READY: System passes 80%+ of tests")
        elif passed_tests / total_tests >= 0.6:
            report.append("⚠️  DEPLOYMENT CAUTION: System passes 60-80% of tests - review failures")
        else:
            report.append("🚫 DEPLOYMENT NOT READY: System passes <60% of tests - fix critical issues")
        
        report_text = "\n".join(report)
        
        # Save report to file
        with open("pre_deployment_report.txt", "w") as f:
            f.write(report_text)
        
        print(report_text)
        return passed_tests / total_tests

    def run_all_tests(self):
        """Run all verification tests"""
        logger.info("🚀 Starting Pre-Deployment Verification")
        
        try:
            self.test_health_endpoints()
            self.test_authentication_system()
            self.test_video_processing_endpoints()
            self.test_user_data_endpoints()
            self.test_editor_endpoints()
            self.test_environment_variables()
            self.test_cors_configuration()
            self.test_static_file_serving()
            
            success_rate = self.generate_report()
            return success_rate >= 0.8
            
        except Exception as e:
            logger.error(f"Verification failed with error: {e}")
            return False

def main():
    """Main function to run verification"""
    import argparse
    
    parser = argparse.ArgumentParser(description="SmartClips Pre-Deployment Verification")
    parser.add_argument("--url", default="http://localhost:8000", 
                       help="Backend URL to test (default: http://localhost:8000)")
    args = parser.parse_args()
    
    verifier = PreDeploymentVerifier(args.url)
    success = verifier.run_all_tests()
    
    exit(0 if success else 1)

if __name__ == "__main__":
    main()
