import { ContactForm } from "../components/ContactForm";
import { useEffect } from "react";

export default function Contact() {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <>
      <style>{`
        body {
          background-color: #000;
        }
        .contact-wrapper {
          min-height: 100vh;
          background-color: #000;
          color: white;
          padding: 3rem 1rem;
          font-family: "Segoe UI", sans-serif;
        }
        .contact-title {
          text-align: center;
          font-size: 2.5rem;
          font-weight: bold;
          margin-bottom: 3rem;
        }
        .contact-content {
          display: flex;
          flex-direction: column;
          gap: 2rem;
          max-width: 1100px;
          margin: 0 auto 2rem auto;
        }
        @media (min-width: 768px) {
          .contact-content {
            flex-direction: row;
          }
        }
        .contact-box {
          background-color: #1a1a1a;
          padding: 2rem;
          border-radius: 12px;
          flex: 1;
        }
        .manual-fields {
          background-color: #1a1a1a;
          padding: 2rem;
          border-radius: 12px;
          max-width: 800px;
          margin: 0 auto;
        }
        input:focus,
        textarea:focus,
        button:focus {
          outline: none;
          box-shadow: none;
          border-color: purple;
        }
        .manual-fields h2 {
          margin-bottom: 1rem;
          font-size: 1.25rem;
        }
        .manual-fields input {
          width: 100%;
          padding: 0.75rem;
          margin-bottom: 1rem;
          border: 1px solid #555;
          border-radius: 6px;
          background-color: #333;
          color: white;
        }
        .contact-form input,
        .contact-form textarea {
          width: 95%;
          padding: 0.75rem;
          margin-bottom: 1rem;
          border-radius: 6px;
          background-color: #333;
          border: 1px solid #555;
          color: white;
        }
        .contact-form button {
          width: 100%;
          padding: 0.75rem;
          background-color: purple;
          color: white;
          border: none;
          border-radius: 6px;
          font-weight: bold;
          cursor: pointer;
        }
        .contact-form button:hover {
          background-color: darkviolet;
        }
        .info-block {
          background-color: #2a2a2a;
          padding: 1rem;
          border-radius: 6px;
          margin-bottom: 1rem;
          border-left: 4px solid purple;
        }
        .info-block strong {
          display: block;
          margin-bottom: 0.25rem;
          color: #ccc;
        }
        .info-block p {
          margin: 0;
          font-size: 1rem;
          color: white;
        }
      `}</style>

      <div className="contact-wrapper">
        <h1 className="contact-title">Contact Us</h1>

        <div className="contact-content">
          <div className="contact-box">
            <h2>Let’s Get in Touch</h2>
            <p>
              We’re always happy to connect! Whether you have a question,
              comment, or just want to say hello, we’d love to hear from you.
              Use the form below to send us a message, or reach out directly
              using the contact details provided. Our team typically replies
              within 24 hours, and we’re here to help with whatever you need.
            </p>
          </div>

          <div className="contact-box">
            <ContactForm />
          </div>
        </div>

        <div className="manual-fields">
          <h2>Contact Information</h2>

          <div className="info-block">
            <strong>Full Name:</strong>
            <p>Smart clipper</p>
          </div>

          <div className="info-block">
            <strong>Email:</strong>
            <p><EMAIL></p>
          </div>

          <div className="info-block">
            <strong>Phone:</strong>
            <p>+1-3xx-xxxxxxx</p>
          </div>

          <div className="info-block">
            <strong>Address:</strong>
            <p>United State of America</p>
          </div>
        </div>
      </div>
    </>
  );
}
