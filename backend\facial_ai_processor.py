"""
Facial AI Processor for Podcast Video Enhancement
Implements real-time face detection, voice activity detection, and dynamic zoom functionality
"""

import cv2
import numpy as np
import mediapipe as mp
import librosa
import soundfile as sf
from pydub import AudioSegment
from pydub.silence import detect_nonsilent
import ffmpeg
import os
import json
import logging
import time
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime
import asyncio
import tempfile
import requests
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class FaceDetection:
    """Data class for face detection results"""
    timestamp: float
    bbox: Tuple[int, int, int, int]  # x, y, width, height
    confidence: float
    landmarks: Optional[List[Tuple[int, int]]] = None
    speaker_id: Optional[int] = None

@dataclass
class VoiceActivity:
    """Data class for voice activity detection results"""
    start_time: float
    end_time: float
    confidence: float
    speaker_id: Optional[int] = None

@dataclass
class SpeakerInfo:
    """Enhanced speaker information for dynamic switching"""
    speaker_id: int
    position: Tuple[float, float]  # (x, y) center position
    bbox: Tuple[int, int, int, int]  # Current bounding box
    confidence: float
    last_active_time: float
    total_speaking_time: float
    face_detections: List[FaceDetection]

@dataclass
class TransitionInfo:
    """Information for smooth transitions between speakers"""
    from_speaker: Optional[int]
    to_speaker: int
    start_time: float
    duration: float
    from_position: Optional[Tuple[float, float]]
    to_position: Tuple[float, float]

@dataclass
class ZoomConfig:
    """Configuration for zoom effects"""
    zoom_level: float = 1.5  # 1.0 = no zoom, 2.0 = 2x zoom
    transition_speed: float = 0.5  # seconds for zoom transition
    detection_sensitivity: float = 0.7  # confidence threshold for face detection
    voice_threshold: float = 0.3  # threshold for voice activity detection
    padding: int = 50  # padding around face in pixels
    # Enhanced framing options
    include_torso: bool = True  # Include torso in framing (chest-up shot)
    headroom_ratio: float = 0.15  # Percentage of frame height for headroom
    torso_ratio: float = 0.6  # How much torso to include (0.6 = chest-up)
    # Dynamic switching options
    speaker_switch_threshold: float = 0.5  # Minimum duration to switch speakers (seconds)
    transition_smoothness: float = 0.3  # Smoothness of transitions (0-1)
    speaker_memory_duration: float = 2.0  # How long to remember speaker positions

class FacialAIProcessor:
    """Main class for facial AI processing of podcast videos"""
    
    def __init__(self, faces_api_key: str = None):
        """Initialize the facial AI processor"""
        self.faces_api_key = faces_api_key or "eyJraWQiOm51bGwsImFsZyI6IlJTMjU2In0"
        self.faces_db_url = "https://faces.mpdl.mpg.de/imeji/user?email=ivaturi.anish%40gmail.com"
        
        # Initialize MediaPipe Face Detection
        self.mp_face_detection = mp.solutions.face_detection
        self.mp_drawing = mp.solutions.drawing_utils
        self.face_detection = self.mp_face_detection.FaceDetection(
            model_selection=1,  # 0 for short-range, 1 for full-range
            min_detection_confidence=0.5
        )
        
        # Initialize MediaPipe Face Mesh for landmarks
        self.mp_face_mesh = mp.solutions.face_mesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=False,
            max_num_faces=5,
            refine_landmarks=True,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )

        # Enhanced speaker tracking
        self.speakers: Dict[int, SpeakerInfo] = {}
        self.current_speaker: Optional[int] = None
        self.last_speaker_switch: float = 0.0
        self.active_transitions: List[TransitionInfo] = []

        logger.info("Facial AI Processor initialized successfully")

    def analyze_video(self, video_path: str, config: ZoomConfig = None) -> Dict[str, Any]:
        """
        Analyze video for faces and speaking segments
        
        Args:
            video_path: Path to the input video file
            config: Zoom configuration parameters
            
        Returns:
            Dictionary containing analysis results
        """
        if config is None:
            config = ZoomConfig()
            
        logger.info(f"Starting video analysis for: {video_path}")
        
        try:
            # Extract audio for voice activity detection
            audio_path = self._extract_audio(video_path)

            # Detect voice activity
            voice_activities = self._detect_voice_activity(audio_path, config)

            # Detect faces in video
            face_detections = self._detect_faces_in_video(video_path, config)
            
            # Correlate voice activity with face detections
            speaker_segments = self._correlate_voice_and_faces(voice_activities, face_detections)
            
            # Generate zoom timeline
            zoom_timeline = self._generate_zoom_timeline(speaker_segments, config)
            
            # Clean up temporary audio file
            if os.path.exists(audio_path):
                os.remove(audio_path)
            
            analysis_result = {
                "video_path": video_path,
                "duration": self._get_video_duration(video_path),
                "face_detections": len(face_detections),
                "voice_segments": len(voice_activities),
                "speaker_segments": speaker_segments,
                "zoom_timeline": zoom_timeline,
                "config": config.__dict__,
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"Video analysis completed. Found {len(face_detections)} face detections and {len(voice_activities)} voice segments")
            return analysis_result
            
        except Exception as e:
            logger.error(f"Error during video analysis: {str(e)}")
            raise

    def process_video_with_face_zoom(self, video_path: str, output_path: str, config: ZoomConfig = None) -> str:
        """
        Process video with dynamic face zoom effects
        
        Args:
            video_path: Path to input video
            output_path: Path for output video
            config: Zoom configuration
            
        Returns:
            Path to processed video
        """
        if config is None:
            config = ZoomConfig()
            
        logger.info(f"Processing video with face zoom: {video_path}")
        
        try:
            # First analyze the video
            analysis = self.analyze_video(video_path, config)

            # Apply zoom effects based on analysis
            processed_path = self._apply_zoom_effects(
                video_path, 
                output_path, 
                analysis["zoom_timeline"], 
                config
            )
            
            logger.info(f"Video processing completed: {processed_path}")
            return processed_path
            
        except Exception as e:
            logger.error(f"Error during video processing: {str(e)}")
            raise

    def generate_preview(self, video_path: str, config: ZoomConfig = None) -> Dict[str, Any]:
        """
        Generate preview with face tracking overlay
        
        Args:
            video_path: Path to input video
            config: Zoom configuration
            
        Returns:
            Preview data with face tracking information
        """
        if config is None:
            config = ZoomConfig()
            
        logger.info(f"Generating preview for: {video_path}")
        
        try:
            # Analyze video
            analysis = self.analyze_video(video_path, config)

            # Generate preview frames with face overlays
            preview_frames = self._generate_preview_frames(video_path, analysis, config)
            
            preview_data = {
                "video_path": video_path,
                "analysis": analysis,
                "preview_frames": preview_frames,
                "total_frames": len(preview_frames),
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"Preview generated with {len(preview_frames)} frames")
            return preview_data
            
        except Exception as e:
            logger.error(f"Error generating preview: {str(e)}")
            raise

    def _extract_audio(self, video_path: str) -> str:
        """Extract audio from video for voice activity detection"""
        temp_audio = tempfile.mktemp(suffix=".wav")

        try:
            # Check if video has audio streams
            probe = ffmpeg.probe(video_path)
            audio_streams = [stream for stream in probe['streams'] if stream['codec_type'] == 'audio']

            if not audio_streams:
                logger.warning(f"No audio streams found in {video_path}, creating silent audio")
                # Create a silent audio file for videos without audio
                duration = float(probe['format']['duration'])
                (
                    ffmpeg
                    .input('anullsrc=channel_layout=mono:sample_rate=16000', f='lavfi', t=duration)
                    .output(temp_audio, acodec='pcm_s16le')
                    .overwrite_output()
                    .run(capture_stdout=True, capture_stderr=True)
                )
            else:
                # Extract audio normally
                (
                    ffmpeg
                    .input(video_path)
                    .output(temp_audio, acodec='pcm_s16le', ac=1, ar='16000')
                    .overwrite_output()
                    .run(capture_stdout=True, capture_stderr=True)
                )

            return temp_audio
        except ffmpeg.Error as e:
            logger.error(f"Error extracting audio: {e.stderr.decode()}")
            raise
        except Exception as e:
            logger.error(f"Error during audio extraction: {str(e)}")
            raise

    def _detect_voice_activity(self, audio_path: str, config: ZoomConfig) -> List[VoiceActivity]:
        """Detect voice activity in audio using multiple methods"""
        voice_activities = []
        
        try:
            # Load audio
            audio = AudioSegment.from_wav(audio_path)
            
            # Method 1: Silence detection using pydub
            nonsilent_ranges = detect_nonsilent(
                audio,
                min_silence_len=500,  # 500ms minimum silence
                silence_thresh=audio.dBFS - 16  # 16dB below average
            )
            
            for start_ms, end_ms in nonsilent_ranges:
                voice_activities.append(VoiceActivity(
                    start_time=start_ms / 1000.0,
                    end_time=end_ms / 1000.0,
                    confidence=0.8  # Default confidence for silence detection
                ))
            
            # Method 2: Energy-based VAD using librosa
            y, sr = librosa.load(audio_path, sr=16000)
            
            # Compute short-time energy
            frame_length = int(0.025 * sr)  # 25ms frames
            hop_length = int(0.010 * sr)   # 10ms hop
            
            energy = librosa.feature.rms(
                y=y, 
                frame_length=frame_length, 
                hop_length=hop_length
            )[0]
            
            # Threshold-based VAD
            energy_threshold = np.mean(energy) * config.voice_threshold
            voice_frames = energy > energy_threshold
            
            # Convert frame-based detection to time segments
            times = librosa.frames_to_time(
                np.arange(len(voice_frames)), 
                sr=sr, 
                hop_length=hop_length
            )
            
            # Group consecutive voice frames
            in_speech = False
            start_time = 0
            
            for i, (time, is_voice) in enumerate(zip(times, voice_frames)):
                if is_voice and not in_speech:
                    start_time = time
                    in_speech = True
                elif not is_voice and in_speech:
                    voice_activities.append(VoiceActivity(
                        start_time=start_time,
                        end_time=time,
                        confidence=0.9
                    ))
                    in_speech = False
            
            # Handle case where speech continues to end
            if in_speech:
                voice_activities.append(VoiceActivity(
                    start_time=start_time,
                    end_time=times[-1],
                    confidence=0.9
                ))
            
            # Merge overlapping segments and sort
            voice_activities = self._merge_voice_segments(voice_activities)
            
            logger.info(f"Detected {len(voice_activities)} voice activity segments")
            return voice_activities
            
        except Exception as e:
            logger.error(f"Error in voice activity detection: {str(e)}")
            return []

    def _detect_faces_in_video(self, video_path: str, config: ZoomConfig) -> List[FaceDetection]:
        """Detect faces in video frames"""
        face_detections = []
        
        try:
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = 0
            
            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break
                
                timestamp = frame_count / fps
                
                # Convert BGR to RGB for MediaPipe
                rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # Detect faces
                results = self.face_detection.process(rgb_frame)
                
                if results.detections:
                    for detection in results.detections:
                        # Extract bounding box
                        bbox = detection.location_data.relative_bounding_box
                        h, w, _ = frame.shape
                        
                        x = int(bbox.xmin * w)
                        y = int(bbox.ymin * h)
                        width = int(bbox.width * w)
                        height = int(bbox.height * h)
                        
                        confidence = detection.score[0]
                        
                        if confidence >= config.detection_sensitivity:
                            face_detections.append(FaceDetection(
                                timestamp=timestamp,
                                bbox=(x, y, width, height),
                                confidence=confidence
                            ))
                
                frame_count += 1
                
                # Process every 5th frame for performance
                for _ in range(4):
                    ret, _ = cap.read()
                    if not ret:
                        break
                    frame_count += 1
            
            cap.release()
            
            logger.info(f"Detected {len(face_detections)} faces in video")
            return face_detections
            
        except Exception as e:
            logger.error(f"Error in face detection: {str(e)}")
            return []

    def _correlate_voice_and_faces(self, voice_activities: List[VoiceActivity], face_detections: List[FaceDetection]) -> List[Dict[str, Any]]:
        """Enhanced correlation with dynamic speaker switching"""
        speaker_segments = []

        # Reset speaker tracking for new analysis
        self.speakers.clear()
        self.current_speaker = None
        self.last_speaker_switch = 0.0

        for voice_segment in voice_activities:
            # Find faces that appear during this voice segment
            concurrent_faces = []

            for face in face_detections:
                if voice_segment.start_time <= face.timestamp <= voice_segment.end_time:
                    concurrent_faces.append(face)

            if concurrent_faces:
                # Enhanced speaker identification with memory
                active_speaker_id = self._identify_active_speaker(
                    concurrent_faces,
                    voice_segment.start_time,
                    voice_segment.end_time
                )

                if active_speaker_id is not None:
                    speaker_info = self.speakers[active_speaker_id]

                    speaker_segments.append({
                        "start_time": voice_segment.start_time,
                        "end_time": voice_segment.end_time,
                        "speaker_id": active_speaker_id,
                        "face_bbox": speaker_info.bbox,
                        "confidence": speaker_info.confidence,
                        "face_count": len(speaker_info.face_detections),
                        "is_speaker_switch": active_speaker_id != self.current_speaker,
                        "previous_speaker": self.current_speaker
                    })

                    # Update current speaker
                    self.current_speaker = active_speaker_id
                    self.last_speaker_switch = voice_segment.start_time

        return speaker_segments

    def _identify_active_speaker(self, faces: List[FaceDetection], start_time: float, end_time: float) -> Optional[int]:
        """Identify the active speaker with enhanced logic"""
        if not faces:
            return None

        # Group faces by proximity
        face_groups = self._group_faces_by_speaker(faces)

        # Update or create speaker info for each group
        for group_id, group_faces in face_groups.items():
            avg_bbox = self._calculate_average_bbox(group_faces)
            avg_confidence = np.mean([f.confidence for f in group_faces])
            center_x = avg_bbox[0] + avg_bbox[2] // 2
            center_y = avg_bbox[1] + avg_bbox[3] // 2

            # Find existing speaker or create new one
            speaker_id = self._find_or_create_speaker(
                (center_x, center_y),
                avg_bbox,
                avg_confidence,
                group_faces
            )

            # Update speaker activity
            if speaker_id in self.speakers:
                speaker = self.speakers[speaker_id]
                speaker.last_active_time = end_time
                speaker.total_speaking_time += (end_time - start_time)
                speaker.face_detections.extend(group_faces)

        # Determine most likely active speaker
        return self._select_most_likely_speaker(start_time)

    def _find_or_create_speaker(self, position: Tuple[float, float], bbox: Tuple[int, int, int, int],
                               confidence: float, faces: List[FaceDetection]) -> int:
        """Find existing speaker or create new one based on position"""
        # Check if position matches existing speaker
        for speaker_id, speaker in self.speakers.items():
            distance = np.sqrt((position[0] - speaker.position[0])**2 +
                             (position[1] - speaker.position[1])**2)
            if distance < 150:  # Threshold for same speaker
                # Update speaker info
                speaker.position = position
                speaker.bbox = bbox
                speaker.confidence = max(speaker.confidence, confidence)
                return speaker_id

        # Create new speaker
        new_speaker_id = len(self.speakers)
        self.speakers[new_speaker_id] = SpeakerInfo(
            speaker_id=new_speaker_id,
            position=position,
            bbox=bbox,
            confidence=confidence,
            last_active_time=0.0,
            total_speaking_time=0.0,
            face_detections=faces.copy()
        )
        return new_speaker_id

    def _select_most_likely_speaker(self, current_time: float) -> Optional[int]:
        """Select the most likely active speaker based on various factors"""
        if not self.speakers:
            return None

        # Score speakers based on multiple factors
        speaker_scores = {}

        for speaker_id, speaker in self.speakers.items():
            score = 0.0

            # Factor 1: Confidence score
            score += speaker.confidence * 0.3

            # Factor 2: Recent activity (higher score for recent speakers)
            time_since_active = current_time - speaker.last_active_time
            if time_since_active < 2.0:  # Recent activity bonus
                score += (2.0 - time_since_active) * 0.4

            # Factor 3: Total speaking time (consistent speakers get bonus)
            score += min(speaker.total_speaking_time / 10.0, 1.0) * 0.2

            # Factor 4: Continuity bonus (prefer current speaker)
            if speaker_id == self.current_speaker:
                score += 0.1

            speaker_scores[speaker_id] = score

        # Return speaker with highest score
        return max(speaker_scores.items(), key=lambda x: x[1])[0]

    def _generate_zoom_timeline(self, speaker_segments: List[Dict[str, Any]], config: ZoomConfig) -> List[Dict[str, Any]]:
        """Generate enhanced timeline for zoom effects with smooth transitions"""
        zoom_timeline = []
        self.active_transitions.clear()

        for i, segment in enumerate(speaker_segments):
            bbox = segment["face_bbox"]

            # Calculate enhanced framing for torso + head
            if config.include_torso:
                enhanced_bbox = self._calculate_enhanced_framing(bbox, config)
            else:
                enhanced_bbox = bbox

            # Calculate center from enhanced bbox
            face_center_x = enhanced_bbox[0] + enhanced_bbox[2] // 2
            face_center_y = enhanced_bbox[1] + enhanced_bbox[3] // 2

            # Check for speaker switch
            is_speaker_switch = segment.get("is_speaker_switch", False)
            previous_speaker = segment.get("previous_speaker")

            timeline_entry = {
                "start_time": segment["start_time"],
                "end_time": segment["end_time"],
                "zoom_level": config.zoom_level,
                "target_bbox": enhanced_bbox,
                "face_center": (face_center_x, face_center_y),
                "transition_speed": config.transition_speed,
                "speaker_id": segment["speaker_id"],
                "is_speaker_switch": is_speaker_switch,
                "previous_speaker": previous_speaker
            }

            # Add transition info for speaker switches
            if is_speaker_switch and previous_speaker is not None:
                # Get previous speaker position
                prev_position = None
                if previous_speaker in self.speakers:
                    prev_bbox = self.speakers[previous_speaker].bbox
                    prev_position = (
                        prev_bbox[0] + prev_bbox[2] // 2,
                        prev_bbox[1] + prev_bbox[3] // 2
                    )

                transition = TransitionInfo(
                    from_speaker=previous_speaker,
                    to_speaker=segment["speaker_id"],
                    start_time=segment["start_time"],
                    duration=config.transition_speed,
                    from_position=prev_position,
                    to_position=(face_center_x, face_center_y)
                )

                self.active_transitions.append(transition)
                timeline_entry["transition"] = transition

            zoom_timeline.append(timeline_entry)

        return zoom_timeline

    def _calculate_enhanced_framing(self, face_bbox: Tuple[int, int, int, int], config: ZoomConfig) -> Tuple[int, int, int, int]:
        """Calculate professional framing that includes torso and proper headroom following video production standards"""
        x, y, w, h = face_bbox

        # Professional video framing guidelines
        # - Headroom: 10-20% of frame height above the head
        # - Torso inclusion: Show from chest up (approximately 60% more height below face)
        # - Aspect ratio: Maintain 16:9 or 4:3 for professional look
        # - Rule of thirds: Position eyes in upper third of frame

        # Calculate face center and eye level (approximately 1/3 from top of face)
        face_center_x = x + w // 2
        face_center_y = y + h // 2
        eye_level_y = y + int(h * 0.3)  # Eyes are typically in upper third of face

        # Calculate professional headroom (15% of desired frame height)
        headroom_ratio = config.headroom_ratio

        # Calculate torso extension (chest-up shot)
        torso_extension = int(h * config.torso_ratio * 2)  # More generous torso inclusion

        # Determine frame dimensions based on professional standards
        # For talking head shots, use 4:3 aspect ratio for better composition
        desired_aspect_ratio = 4.0 / 3.0

        # Calculate frame height to include headroom + face + torso
        total_content_height = int(h * headroom_ratio) + h + torso_extension
        frame_height = int(total_content_height / (1 - headroom_ratio))  # Account for headroom percentage

        # Calculate frame width based on aspect ratio
        frame_width = int(frame_height * desired_aspect_ratio)

        # Position the frame with proper composition
        # Eyes should be in upper third of frame (rule of thirds)
        eye_position_ratio = 0.33  # Eyes at 1/3 from top
        frame_top = eye_level_y - int(frame_height * eye_position_ratio)

        # Center horizontally on face
        frame_left = face_center_x - frame_width // 2

        # Ensure frame stays within video bounds (will be handled in zoom application)
        frame_left = max(0, frame_left)
        frame_top = max(0, frame_top)

        # Apply safety margins to prevent cutting off important content
        safety_margin = int(min(w, h) * 0.1)  # 10% safety margin
        frame_left = max(safety_margin, frame_left)
        frame_top = max(safety_margin, frame_top)

        return (frame_left, frame_top, frame_width, frame_height)

    def _apply_professional_composition_rules(self, bbox: Tuple[int, int, int, int],
                                            frame_width: int, frame_height: int) -> Tuple[int, int, int, int]:
        """Apply professional video composition rules for better framing"""
        x, y, w, h = bbox

        # Rule of thirds: divide frame into 9 equal parts
        third_width = frame_width // 3
        third_height = frame_height // 3

        # Golden ratio points for more pleasing composition
        golden_ratio = 1.618
        golden_x = int(frame_width / golden_ratio)
        golden_y = int(frame_height / golden_ratio)

        # Adjust positioning based on composition rules
        # For single subject, center is often acceptable, but slight offset can be more dynamic
        center_x = x + w // 2
        center_y = y + h // 2

        # Apply subtle offset for more dynamic composition
        offset_x = int(third_width * 0.1)  # Slight offset from center
        offset_y = int(third_height * 0.05)  # Minimal vertical offset

        adjusted_x = max(0, min(frame_width - w, center_x - w // 2 + offset_x))
        adjusted_y = max(0, min(frame_height - h, center_y - h // 2 - offset_y))

        return (adjusted_x, adjusted_y, w, h)

    def _merge_voice_segments(self, segments: List[VoiceActivity]) -> List[VoiceActivity]:
        """Merge overlapping voice activity segments"""
        if not segments:
            return []
        
        # Sort by start time
        segments.sort(key=lambda x: x.start_time)
        
        merged = [segments[0]]
        
        for current in segments[1:]:
            last = merged[-1]
            
            # If segments overlap or are very close (within 0.5 seconds)
            if current.start_time <= last.end_time + 0.5:
                # Merge segments
                merged[-1] = VoiceActivity(
                    start_time=last.start_time,
                    end_time=max(last.end_time, current.end_time),
                    confidence=max(last.confidence, current.confidence)
                )
            else:
                merged.append(current)
        
        return merged

    def _group_faces_by_speaker(self, faces: List[FaceDetection]) -> Dict[int, List[FaceDetection]]:
        """Group faces by speaker using spatial clustering"""
        if not faces:
            return {}
        
        # Simple clustering based on face position
        speakers = {}
        speaker_id = 0
        
        for face in faces:
            assigned = False
            
            # Check if face belongs to existing speaker
            for sid, speaker_faces in speakers.items():
                if self._faces_belong_to_same_speaker(face, speaker_faces):
                    speakers[sid].append(face)
                    assigned = True
                    break
            
            # Create new speaker if not assigned
            if not assigned:
                speakers[speaker_id] = [face]
                speaker_id += 1
        
        return speakers

    def _faces_belong_to_same_speaker(self, face: FaceDetection, speaker_faces: List[FaceDetection]) -> bool:
        """Check if a face belongs to the same speaker based on position similarity"""
        if not speaker_faces:
            return False
        
        # Calculate average position of existing speaker faces
        avg_x = np.mean([f.bbox[0] + f.bbox[2]/2 for f in speaker_faces])
        avg_y = np.mean([f.bbox[1] + f.bbox[3]/2 for f in speaker_faces])
        
        # Calculate face center
        face_x = face.bbox[0] + face.bbox[2]/2
        face_y = face.bbox[1] + face.bbox[3]/2
        
        # Check if within reasonable distance (adjust threshold as needed)
        distance = np.sqrt((face_x - avg_x)**2 + (face_y - avg_y)**2)
        threshold = 100  # pixels
        
        return distance < threshold

    def _calculate_average_bbox(self, faces: List[FaceDetection]) -> Tuple[int, int, int, int]:
        """Calculate average bounding box from multiple face detections"""
        if not faces:
            return (0, 0, 0, 0)
        
        avg_x = int(np.mean([f.bbox[0] for f in faces]))
        avg_y = int(np.mean([f.bbox[1] for f in faces]))
        avg_w = int(np.mean([f.bbox[2] for f in faces]))
        avg_h = int(np.mean([f.bbox[3] for f in faces]))
        
        return (avg_x, avg_y, avg_w, avg_h)

    def _get_video_duration(self, video_path: str) -> float:
        """Get video duration in seconds"""
        try:
            probe = ffmpeg.probe(video_path)
            duration = float(probe['streams'][0]['duration'])
            return duration
        except:
            return 0.0

    def _apply_zoom_effects(self, input_path: str, output_path: str, zoom_timeline: List[Dict[str, Any]], config: ZoomConfig) -> str:
        """Apply zoom effects to video based on timeline"""
        logger.info(f"Applying zoom effects with {len(zoom_timeline)} segments")

        if not zoom_timeline:
            logger.info("No zoom segments to apply, copying original video")
            import shutil
            shutil.copy2(input_path, output_path)
            return output_path

        try:
            # Open input video
            cap = cv2.VideoCapture(input_path)
            if not cap.isOpened():
                raise ValueError(f"Could not open video: {input_path}")

            # Get video properties
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            logger.info(f"Processing video: {width}x{height} @ {fps}fps, {total_frames} frames")

            # Setup high-quality video writer for professional output
            # Use H.264 codec for better compression and compatibility
            fourcc = cv2.VideoWriter_fourcc(*'H264')

            # Try H.264 first, fallback to mp4v if not available
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

            if not out.isOpened():
                logger.warning("H.264 codec not available, falling back to mp4v")
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

                if not out.isOpened():
                    raise ValueError(f"Could not create output video with any codec: {output_path}")

            logger.info(f"Video writer initialized with codec: {fourcc}")

            # Create enhanced zoom timeline lookup for efficient processing
            zoom_lookup = {}
            for segment in zoom_timeline:
                start_frame = int(segment['start_time'] * fps)
                end_frame = int(segment['end_time'] * fps)
                zoom_level = segment['zoom_level']
                face_center = segment.get('face_center', (width // 2, height // 2))
                transition = segment.get('transition')

                for frame_idx in range(start_frame, min(end_frame + 1, total_frames)):
                    current_time = frame_idx / fps
                    zoom_lookup[frame_idx] = {
                        'zoom_level': zoom_level,
                        'face_center': face_center,
                        'transition': transition,
                        'current_time': current_time,
                        'speaker_id': segment.get('speaker_id'),
                        'is_speaker_switch': segment.get('is_speaker_switch', False)
                    }

            logger.info(f"Created enhanced zoom lookup for {len(zoom_lookup)} frames")

            # Process each frame
            frame_count = 0
            processed_frames = 0

            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break

                # Check if this frame needs zoom effect
                if frame_count in zoom_lookup:
                    zoom_info = zoom_lookup[frame_count]
                    frame = self._apply_zoom_to_frame(
                        frame,
                        zoom_info['zoom_level'],
                        zoom_info['face_center'],
                        config,
                        zoom_info.get('transition'),
                        zoom_info.get('current_time', 0.0)
                    )
                    processed_frames += 1

                # Write frame to output
                out.write(frame)
                frame_count += 1

                # Progress logging
                if frame_count % 1000 == 0:
                    progress = (frame_count / total_frames) * 100
                    logger.info(f"Processing progress: {progress:.1f}% ({frame_count}/{total_frames})")

            # Cleanup
            cap.release()
            out.release()

            logger.info(f"Zoom effects applied successfully!")
            logger.info(f"  - Total frames processed: {frame_count}")
            logger.info(f"  - Frames with zoom effects: {processed_frames}")
            logger.info(f"  - Output video: {output_path}")

            # Verify output video quality and completeness
            verification_result = self._verify_output_video(output_path, input_path)
            if verification_result["success"]:
                logger.info("✅ Output video verification successful")
                logger.info(f"  - Duration match: {verification_result['duration_match']}")
                logger.info(f"  - Quality preserved: {verification_result['quality_preserved']}")
            else:
                logger.warning("⚠️ Output video verification failed")

            return output_path

        except Exception as e:
            logger.error(f"Error applying zoom effects: {e}")
            # Fallback: copy original video
            import shutil
            shutil.copy2(input_path, output_path)
            return output_path

    def _verify_output_video(self, output_path: str, input_path: str) -> Dict[str, Any]:
        """Verify the quality and completeness of the output video"""
        try:
            # Check if output file exists and has reasonable size
            if not os.path.exists(output_path):
                return {"success": False, "error": "Output file does not exist"}

            output_size = os.path.getsize(output_path)
            input_size = os.path.getsize(input_path)

            # Output should be at least 50% of input size (accounting for compression)
            if output_size < input_size * 0.5:
                return {"success": False, "error": "Output file too small"}

            # Verify video properties using OpenCV
            cap_input = cv2.VideoCapture(input_path)
            cap_output = cv2.VideoCapture(output_path)

            if not cap_input.isOpened() or not cap_output.isOpened():
                return {"success": False, "error": "Cannot open video files for verification"}

            # Get video properties
            input_fps = cap_input.get(cv2.CAP_PROP_FPS)
            output_fps = cap_output.get(cv2.CAP_PROP_FPS)
            input_frames = int(cap_input.get(cv2.CAP_PROP_FRAME_COUNT))
            output_frames = int(cap_output.get(cv2.CAP_PROP_FRAME_COUNT))

            cap_input.release()
            cap_output.release()

            # Check frame count and FPS consistency
            duration_match = abs(input_frames - output_frames) <= 5  # Allow 5 frame difference
            fps_match = abs(input_fps - output_fps) < 0.1
            quality_preserved = duration_match and fps_match

            return {
                "success": True,
                "duration_match": duration_match,
                "fps_match": fps_match,
                "quality_preserved": quality_preserved,
                "input_frames": input_frames,
                "output_frames": output_frames,
                "input_fps": input_fps,
                "output_fps": output_fps,
                "size_ratio": output_size / input_size
            }

        except Exception as e:
            return {"success": False, "error": f"Verification failed: {str(e)}"}

    def generate_complete_processed_video(self, video_path: str, output_path: str, config: ZoomConfig = None) -> Dict[str, Any]:
        """Generate a complete processed video with enhanced facial AI zoom and professional framing"""
        if config is None:
            config = ZoomConfig()

        logger.info("🎬 Starting complete video processing with enhanced facial AI")
        logger.info(f"📁 Input: {video_path}")
        logger.info(f"📁 Output: {output_path}")
        logger.info(f"⚙️ Config: zoom={config.zoom_level}x, torso={config.include_torso}, transitions={config.transition_smoothness}")

        start_time = time.time()

        try:
            # Step 1: Analyze video for faces and voice activity
            logger.info("🔍 Step 1: Analyzing video...")
            analysis = self.analyze_video(video_path, config)

            # Step 2: Process video with enhanced zoom effects
            logger.info("🎯 Step 2: Applying enhanced zoom effects...")
            processed_path = self._apply_zoom_effects(video_path, output_path, analysis["zoom_timeline"], config)

            # Step 3: Generate processing report
            processing_time = time.time() - start_time

            report = {
                "success": True,
                "input_video": video_path,
                "output_video": processed_path,
                "processing_time": processing_time,
                "analysis": {
                    "face_detections": analysis.get("face_detections", 0),
                    "voice_segments": analysis.get("voice_segments", 0),
                    "speaker_segments": len(analysis.get("speaker_segments", [])),
                    "zoom_segments": len(analysis.get("zoom_timeline", []))
                },
                "enhancements": {
                    "dynamic_speaker_switching": True,
                    "professional_framing": config.include_torso,
                    "smooth_transitions": True,
                    "torso_head_composition": config.include_torso
                },
                "config": config.__dict__
            }

            logger.info("✅ Complete video processing finished successfully!")
            logger.info(f"⏱️ Total processing time: {processing_time:.2f} seconds")
            logger.info(f"🎭 Detected {report['analysis']['speaker_segments']} speaker segments")
            logger.info(f"🎯 Applied {report['analysis']['zoom_segments']} zoom effects")

            return report

        except Exception as e:
            logger.error(f"❌ Error during complete video processing: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "input_video": video_path,
                "processing_time": time.time() - start_time
            }

    def _apply_zoom_to_frame(self, frame: np.ndarray, zoom_level: float, face_center: tuple, config: ZoomConfig,
                           transition_info: Optional[TransitionInfo] = None, current_time: float = 0.0) -> np.ndarray:
        """Apply enhanced zoom effect with smooth transitions and improved framing"""
        try:
            if zoom_level <= 1.0:
                return frame  # No zoom needed

            height, width = frame.shape[:2]

            # Handle smooth transitions between speakers
            if transition_info and self._is_in_transition(transition_info, current_time):
                face_center = self._interpolate_transition(transition_info, current_time)

            face_x, face_y = face_center

            # Ensure face center is within frame bounds
            face_x = max(0, min(width - 1, int(face_x)))
            face_y = max(0, min(height - 1, int(face_y)))

            # Calculate crop dimensions for professional framing
            if config.include_torso:
                # Use professional framing calculations
                base_crop_width = int(width / zoom_level)
                base_crop_height = int(height / zoom_level)

                # Apply professional aspect ratio (4:3 for talking heads)
                professional_aspect = 4.0 / 3.0

                # Adjust dimensions to maintain professional composition
                if base_crop_width / base_crop_height > professional_aspect:
                    # Too wide, adjust width
                    crop_width = int(base_crop_height * professional_aspect)
                    crop_height = base_crop_height
                else:
                    # Too tall, adjust height
                    crop_width = base_crop_width
                    crop_height = int(base_crop_width / professional_aspect)

                # Ensure minimum size for quality
                min_crop_size = min(width, height) // 3
                crop_width = max(crop_width, min_crop_size)
                crop_height = max(crop_height, min_crop_size)
            else:
                # Standard zoom calculation
                crop_width = int(width / zoom_level)
                crop_height = int(height / zoom_level)

            # Calculate crop region centered on face
            crop_x1 = max(0, face_x - crop_width // 2)
            crop_y1 = max(0, face_y - crop_height // 2)
            crop_x2 = min(width, crop_x1 + crop_width)
            crop_y2 = min(height, crop_y1 + crop_height)

            # Adjust crop region if it goes out of bounds
            if crop_x2 - crop_x1 < crop_width:
                crop_x1 = max(0, crop_x2 - crop_width)
            if crop_y2 - crop_y1 < crop_height:
                crop_y1 = max(0, crop_y2 - crop_height)

            # Crop the frame
            cropped_frame = frame[crop_y1:crop_y2, crop_x1:crop_x2]

            # Resize cropped frame back to original dimensions (this creates the zoom effect)
            zoomed_frame = cv2.resize(cropped_frame, (width, height), interpolation=cv2.INTER_LANCZOS4)

            return zoomed_frame

        except Exception as e:
            logger.warning(f"Error applying zoom to frame: {e}")
            return frame  # Return original frame on error

    def _is_in_transition(self, transition: TransitionInfo, current_time: float) -> bool:
        """Check if current time is within transition period"""
        return transition.start_time <= current_time <= (transition.start_time + transition.duration)

    def _interpolate_transition(self, transition: TransitionInfo, current_time: float) -> Tuple[float, float]:
        """Interpolate position during transition for smooth movement"""
        if not transition.from_position:
            return transition.to_position

        # Calculate transition progress (0.0 to 1.0)
        progress = (current_time - transition.start_time) / transition.duration
        progress = max(0.0, min(1.0, progress))

        # Apply easing function for smoother transitions
        eased_progress = self._ease_in_out_cubic(progress)

        # Interpolate between positions
        from_x, from_y = transition.from_position
        to_x, to_y = transition.to_position

        interpolated_x = from_x + (to_x - from_x) * eased_progress
        interpolated_y = from_y + (to_y - from_y) * eased_progress

        return (interpolated_x, interpolated_y)

    def _ease_in_out_cubic(self, t: float) -> float:
        """Cubic easing function for smooth transitions"""
        if t < 0.5:
            return 4 * t * t * t
        else:
            return 1 - pow(-2 * t + 2, 3) / 2

    def _generate_preview_frames(self, video_path: str, analysis: Dict[str, Any], config: ZoomConfig) -> List[Dict[str, Any]]:
        """Generate preview frames with face tracking overlays"""
        preview_frames = []
        
        try:
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = 0
            
            # Generate preview for first 30 seconds or 10 frames, whichever is less
            max_frames = min(int(30 * fps), 300)
            frame_interval = max(1, max_frames // 10)  # Sample 10 frames max
            
            while cap.isOpened() and frame_count < max_frames:
                ret, frame = cap.read()
                if not ret:
                    break
                
                if frame_count % frame_interval == 0:
                    timestamp = frame_count / fps
                    
                    # Find faces at this timestamp
                    current_faces = []
                    for segment in analysis.get("speaker_segments", []):
                        if segment["start_time"] <= timestamp <= segment["end_time"]:
                            current_faces.append(segment)
                    
                    preview_frames.append({
                        "timestamp": timestamp,
                        "frame_number": frame_count,
                        "faces": current_faces,
                        "has_speech": len(current_faces) > 0
                    })
                
                frame_count += 1
            
            cap.release()
            
        except Exception as e:
            logger.error(f"Error generating preview frames: {str(e)}")
        
        return preview_frames
