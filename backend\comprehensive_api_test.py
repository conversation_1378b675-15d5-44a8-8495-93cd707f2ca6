#!/usr/bin/env python3
"""
Comprehensive API Endpoint Testing Script for SmartClips
Tests all major API endpoints for proper responses and functionality
"""

import requests
import json
import time
import os
from typing import Dict, Any, Optional

# Configuration
BASE_URL = "http://localhost:8000"
TEST_USER = {
    "username": "testuser",
    "email": "<EMAIL>", 
    "password": "testpassword123"
}

class APITester:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = requests.Session()
        self.auth_token = None
        self.test_results = []
        
    def log_test(self, endpoint: str, method: str, status_code: int, success: bool, details: str = ""):
        """Log test results"""
        result = {
            "endpoint": endpoint,
            "method": method,
            "status_code": status_code,
            "success": success,
            "details": details,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.test_results.append(result)
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {method} {endpoint} - {status_code} - {details}")
        
    def test_endpoint(self, endpoint: str, method: str = "GET", data: Optional[Dict] = None, 
                     files: Optional[Dict] = None, expected_status: int = 200, 
                     auth_required: bool = False) -> bool:
        """Test a single endpoint"""
        url = f"{self.base_url}{endpoint}"
        headers = {}
        
        if auth_required and self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
            
        try:
            if method == "GET":
                response = self.session.get(url, headers=headers)
            elif method == "POST":
                if files:
                    response = self.session.post(url, data=data, files=files, headers=headers)
                else:
                    headers["Content-Type"] = "application/json"
                    response = self.session.post(url, json=data, headers=headers)
            elif method == "PUT":
                headers["Content-Type"] = "application/json"
                response = self.session.put(url, json=data, headers=headers)
            elif method == "DELETE":
                response = self.session.delete(url, headers=headers)
            else:
                self.log_test(endpoint, method, 0, False, f"Unsupported method: {method}")
                return False
                
            success = response.status_code == expected_status
            details = f"Response: {response.text[:100]}..." if len(response.text) > 100 else response.text
            
            self.log_test(endpoint, method, response.status_code, success, details)
            return success
            
        except Exception as e:
            self.log_test(endpoint, method, 0, False, f"Exception: {str(e)}")
            return False
    
    def test_health_endpoints(self):
        """Test health and basic endpoints"""
        print("\n🏥 Testing Health & Basic Endpoints...")
        self.test_endpoint("/", "GET", expected_status=200)
        self.test_endpoint("/health", "GET", expected_status=200)
        
    def test_authentication_endpoints(self):
        """Test authentication endpoints"""
        print("\n🔐 Testing Authentication Endpoints...")
        
        # Test user creation (might fail if user exists)
        user_data = TEST_USER.copy()
        self.test_endpoint("/users/", "POST", data=user_data, expected_status=200)
        
        # Test login
        login_data = {
            "username": TEST_USER["username"],
            "password": TEST_USER["password"]
        }
        
        try:
            response = self.session.post(f"{self.base_url}/token", data=login_data)
            if response.status_code == 200:
                token_data = response.json()
                self.auth_token = token_data.get("access_token")
                self.log_test("/token", "POST", 200, True, "Authentication successful")
            else:
                self.log_test("/token", "POST", response.status_code, False, response.text)
        except Exception as e:
            self.log_test("/token", "POST", 0, False, f"Exception: {str(e)}")
            
        # Test user profile (requires auth)
        self.test_endpoint("/users/me/", "GET", auth_required=True, expected_status=200)
        
    def test_video_processing_endpoints(self):
        """Test video processing endpoints"""
        print("\n🎬 Testing Video Processing Endpoints...")
        
        # Test URL validation (public endpoint)
        url_data = {"url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ"}
        self.test_endpoint("/validate-url", "POST", data=url_data, expected_status=200)
        
        # Test URL metadata (public endpoint)
        self.test_endpoint("/url-metadata", "POST", data=url_data, expected_status=200)
        
        # Test process-instant (requires auth)
        instant_data = {
            "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            "max_duration": 30,
            "add_subtitles": True,
            "add_emojis": False
        }
        self.test_endpoint("/process-instant", "POST", data=instant_data, 
                          auth_required=True, expected_status=200)
        
    def test_user_data_endpoints(self):
        """Test user data endpoints"""
        print("\n👤 Testing User Data Endpoints...")
        
        # Test user clips
        self.test_endpoint("/user/clips", "GET", auth_required=True, expected_status=200)
        
        # Test user stats
        self.test_endpoint("/user/stats", "GET", auth_required=True, expected_status=200)
        
        # Test user video count
        self.test_endpoint("/user/video-count", "GET", auth_required=True, expected_status=200)
        
        # Test social platforms
        self.test_endpoint("/user/social-platforms", "GET", auth_required=True, expected_status=200)
        
    def test_editor_endpoints(self):
        """Test manual editor endpoints"""
        print("\n✂️ Testing Editor Endpoints...")
        
        # Test get editor projects
        self.test_endpoint("/editor/projects", "GET", auth_required=True, expected_status=200)
        
        # Test effect templates
        self.test_endpoint("/editor/effects/templates", "GET", auth_required=True, expected_status=200)
        
    def test_gpt_editor_endpoints(self):
        """Test GPT editor endpoints"""
        print("\n🤖 Testing GPT Editor Endpoints...")
        
        # Test get templates
        self.test_endpoint("/api/gpt-editor/templates", "GET", auth_required=True, expected_status=200)
        
        # Test get stats
        self.test_endpoint("/api/gpt-editor/stats", "GET", auth_required=True, expected_status=200)
        
        # Test get presets
        self.test_endpoint("/api/gpt-editor/presets", "GET", auth_required=True, expected_status=200)
        
        # Test chat history
        self.test_endpoint("/api/gpt-editor/chat-history", "GET", auth_required=True, expected_status=200)
        
    def test_face_detection_endpoints(self):
        """Test face detection endpoints"""
        print("\n👤 Testing Face Detection Endpoints...")
        
        # These endpoints require file uploads or specific data, so we'll test basic access
        # Test analyze endpoint (will fail without proper data but should return 422, not 404)
        self.test_endpoint("/api/face-detection/analyze", "POST", auth_required=True, expected_status=422)
        
    def test_sharing_endpoints(self):
        """Test social media sharing endpoints"""
        print("\n📱 Testing Sharing Endpoints...")
        
        share_data = {
            "video_url": "https://example.com/video.mp4",
            "title": "Test Video",
            "description": "Test Description"
        }
        
        # These will likely fail due to missing OAuth tokens, but should return proper error codes
        self.test_endpoint("/share/youtube", "POST", data=share_data, auth_required=True, expected_status=400)
        self.test_endpoint("/share/instagram", "POST", data=share_data, auth_required=True, expected_status=400)
        self.test_endpoint("/share/tiktok", "POST", data=share_data, auth_required=True, expected_status=400)
        self.test_endpoint("/share/twitter", "POST", data=share_data, auth_required=True, expected_status=400)
        
    def run_all_tests(self):
        """Run all API tests"""
        print("🚀 Starting Comprehensive API Testing...")
        print(f"Testing against: {self.base_url}")
        
        # Run test suites
        self.test_health_endpoints()
        self.test_authentication_endpoints()
        self.test_video_processing_endpoints()
        self.test_user_data_endpoints()
        self.test_editor_endpoints()
        self.test_gpt_editor_endpoints()
        self.test_face_detection_endpoints()
        self.test_sharing_endpoints()
        
        # Generate summary
        self.generate_summary()
        
    def generate_summary(self):
        """Generate test summary"""
        print("\n" + "="*60)
        print("📊 TEST SUMMARY")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  • {result['method']} {result['endpoint']} - {result['status_code']} - {result['details'][:50]}...")
        
        # Save results to file
        with open("api_test_results.json", "w") as f:
            json.dump(self.test_results, f, indent=2)
        print(f"\n📄 Detailed results saved to: api_test_results.json")

if __name__ == "__main__":
    tester = APITester(BASE_URL)
    tester.run_all_tests()
