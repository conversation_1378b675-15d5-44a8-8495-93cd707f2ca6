# SmartClips GPT Editor - AI-Powered Video Editor

## Overview

The SmartClips GPT Editor is an AI-powered video editing system that processes natural language commands and transforms them into professional video editing operations. Built on top of the existing SmartClips infrastructure, it leverages OpenAI's GPT-4 for command interpretation and FFmpeg for video processing.

## 🎯 Key Features

### Natural Language Processing
- **AI Command Interpretation**: Uses OpenAI GPT-4 to parse natural language editing requests
- **Fallback Parsing**: Works without OpenAI API using rule-based parsing
- **Confidence Scoring**: Each parsed command includes confidence levels
- **Context Awareness**: Considers video metadata for better command interpretation

### Video Processing Capabilities
- **Trimming & Cutting**: Precise video trimming with time-based controls
- **Aspect Ratio Conversion**: Convert between formats (16:9, 9:16, 1:1)
- **Text Overlays**: Add custom text with positioning and styling
- **Audio Enhancement**: Normalize and improve audio quality
- **Color Correction**: Adjust brightness, contrast, saturation
- **Speed Adjustment**: Change playback speed with audio sync
- **Watermarking**: Add branded watermarks and logos

### Template System
- **5 Pre-built Templates**: Podcast, Gaming, TikTok, Instagram, YouTube
- **AI Template Recommendation**: Automatic template suggestion based on content
- **Template Customization**: Modify templates with custom parameters
- **Platform Optimization**: Templates optimized for specific social platforms

### Job Processing
- **Background Processing**: Asynchronous video processing with job tracking
- **Real-time Progress**: Live progress updates and status monitoring
- **Error Handling**: Comprehensive error reporting and recovery
- **Job Management**: Create, monitor, and cancel processing jobs

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- FFmpeg installed and accessible
- OpenAI API key (optional, for enhanced AI features)
- Cloudinary account (for video storage)

### Installation
```bash
# Clone the repository
git clone https://github.com/Anish-I/smartclips.git
cd smartclips

# Switch to GPT Editor branch
git checkout gpt-editor

# Install dependencies
pip install -r requirements.txt

# Set environment variables
export OPENAI_API_KEY="your-openai-api-key"
export CLOUDINARY_CLOUD_NAME="your-cloud-name"
export CLOUDINARY_API_KEY="your-api-key"
export CLOUDINARY_API_SECRET="your-api-secret"
```

### Running the Server
```bash
cd backend
python main.py
```

The server will start on `http://localhost:8000` with API documentation at `http://localhost:8000/docs`.

## 📡 API Endpoints

### Process Natural Language Request
```http
POST /api/gpt-editor/process
Content-Type: application/json

{
  "command": "Make this video TikTok ready with subtitles and emojis",
  "video_url": "https://example.com/video.mp4",
  "template": "tiktok",
  "options": {
    "max_duration": 60,
    "add_watermark": true
  }
}
```

**Response:**
```json
{
  "job_id": "gpt-editor-abc123",
  "status": "processing",
  "message": "Video editing job started successfully",
  "parsed_commands": [
    {
      "action": "crop",
      "parameters": {"aspect_ratio": "9:16"},
      "confidence": 0.95,
      "description": "Crop video to vertical format"
    }
  ],
  "estimated_time": 45
}
```

### Upload and Process Video
```http
POST /api/gpt-editor/upload-and-process
Content-Type: multipart/form-data

file: [video file]
command: "Add subtitles and make it professional"
template: "podcast"
```

### Get Job Status
```http
GET /api/gpt-editor/job/{job_id}
```

**Response:**
```json
{
  "job_id": "gpt-editor-abc123",
  "status": "completed",
  "progress": 1.0,
  "result_url": "https://res.cloudinary.com/demo/video/upload/processed.mp4",
  "processing_log": [
    "Job created and queued",
    "AI command parsing completed",
    "Video processing started",
    "Job completed successfully"
  ]
}
```

### Other Endpoints
- `DELETE /api/gpt-editor/job/{job_id}` - Cancel job
- `GET /api/gpt-editor/templates` - List available templates
- `POST /api/gpt-editor/analyze-video` - Analyze video and recommend template
- `GET /api/gpt-editor/stats` - Get processing statistics

## 🎨 Available Templates

### 1. Podcast Template
- **Description**: Professional podcast video with speaker focus
- **Features**: Clean subtitles, audio enhancement, speaker detection
- **Platforms**: YouTube, Spotify
- **Best For**: Interview content, educational talks

### 2. Gaming Template
- **Description**: Dynamic gaming content with vibrant effects
- **Features**: Vibrant colors, action detection, highlight extraction
- **Platforms**: TikTok, YouTube, Twitch
- **Best For**: Gaming highlights, reaction videos

### 3. TikTok Template
- **Description**: Vertical format optimized for TikTok
- **Features**: Viral-style subtitles, emoji overlays, vertical format
- **Platforms**: TikTok, Instagram Reels
- **Best For**: Short-form viral content

### 4. Instagram Template
- **Description**: Square or vertical format for Instagram
- **Features**: Trendy effects, Instagram-optimized colors
- **Platforms**: Instagram, Instagram Reels
- **Best For**: Social media content, lifestyle videos

### 5. YouTube Template
- **Description**: Vertical format for YouTube Shorts
- **Features**: YouTube-style subtitles, watermarks
- **Platforms**: YouTube Shorts
- **Best For**: Educational shorts, quick tutorials

## 💬 Natural Language Commands

### Supported Command Types

#### Video Trimming
- "Trim this video to 30 seconds"
- "Cut the video from 10 seconds to 60 seconds"
- "Make it 45 seconds long"

#### Format Conversion
- "Make this TikTok ready"
- "Convert to vertical format"
- "Make it square for Instagram"
- "Apply 16:9 aspect ratio"

#### Text and Subtitles
- "Add subtitles to this video"
- "Add text saying 'Hello World' at the top"
- "Put a title in the center"

#### Template Application
- "Apply podcast template"
- "Make it look professional for business"
- "Use gaming style effects"
- "Optimize for social media"

#### Audio Enhancement
- "Enhance the audio quality"
- "Normalize the audio"
- "Make the audio clearer"

#### Visual Effects
- "Make the colors more vibrant"
- "Brighten the video"
- "Add a watermark"
- "Increase the contrast"

## 🏗️ Architecture

### Core Components

1. **GPT Editor Service** (`gpt_editor_service.py`)
   - Natural language processing
   - Command parsing and validation
   - Template management
   - Processing time estimation

2. **FFmpeg Command Generator** (`ffmpeg_command_generator.py`)
   - FFmpeg command generation
   - Video processing execution
   - Progress tracking
   - Error handling

3. **Template System** (`template_system.py`)
   - Template configuration management
   - AI-powered template recommendation
   - Template compatibility checking
   - Integration with existing video processor

4. **Job Manager** (`gpt_editor_jobs.py`)
   - Background job processing
   - Status tracking and updates
   - Job lifecycle management
   - Progress monitoring

### Processing Pipeline

1. **Command Reception**: API receives natural language command
2. **AI Parsing**: GPT-4 interprets command into structured actions
3. **Template Analysis**: System recommends optimal template if needed
4. **FFmpeg Generation**: Structured commands converted to FFmpeg operations
5. **Background Processing**: Video processing executed asynchronously
6. **Progress Tracking**: Real-time status updates provided
7. **Result Storage**: Processed video uploaded to Cloudinary
8. **Completion Notification**: Final result URL returned to client

## 🧪 Testing

### Running Tests
```bash
# Run core functionality tests
python test_core_functionality.py

# Run working demo
python working_demo.py

# Run comprehensive demo
python demo_gpt_editor.py
```

### Test Coverage
- ✅ Natural language command parsing
- ✅ FFmpeg command generation
- ✅ Template system functionality
- ✅ Job management operations
- ✅ Async processing capabilities
- ✅ Error handling and recovery
- ✅ Integration with existing systems

## 🔧 Configuration

### Environment Variables
```bash
# Required
OPENAI_API_KEY=your-openai-api-key
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Optional
FFMPEG_BINARY=ffmpeg  # Path to FFmpeg binary
SECRET_KEY=your-secret-key  # JWT secret
```

### Template Customization
Templates can be customized by modifying the `video_templates` dictionary in `gpt_editor_service.py`:

```python
"custom_template": {
    "name": "Custom Template",
    "description": "Your custom template description",
    "effects": [
        {"action": "crop", "parameters": {"aspect_ratio": "16:9"}},
        {"action": "add_text", "parameters": {"text": "Custom", "position": "top"}}
    ],
    "style_config": {
        "subtitle_style": "modern",
        "color_scheme": "custom"
    },
    "target_platforms": ["youtube", "instagram"]
}
```

## 🚀 Production Deployment

### Performance Considerations
- Use Redis for job storage in production
- Implement video processing queues for scalability
- Configure CDN for faster video delivery
- Set up monitoring and logging

### Security
- Validate all input parameters
- Implement rate limiting
- Use secure file upload handling
- Sanitize FFmpeg commands

### Monitoring
- Track job processing times
- Monitor FFmpeg execution success rates
- Log API usage and errors
- Set up alerts for system health

## 🤝 Integration with SmartClips

The GPT Editor seamlessly integrates with existing SmartClips features:
- **Authentication**: Uses existing Google OAuth system
- **Storage**: Leverages Cloudinary integration
- **Video Processing**: Built on existing FFmpeg infrastructure
- **Database**: Extends current video and user models
- **API**: Follows established API patterns

## 📈 Future Enhancements

- **Advanced AI Features**: Scene detection, object recognition
- **Real-time Processing**: Live video editing capabilities
- **Collaborative Editing**: Multi-user editing sessions
- **Custom Effects**: User-defined video effects
- **Batch Processing**: Process multiple videos simultaneously
- **Mobile SDK**: Native mobile app integration

## 🆘 Troubleshooting

### Common Issues

**FFmpeg Not Found**
```bash
# Install FFmpeg
# Windows: Download from https://ffmpeg.org/
# macOS: brew install ffmpeg
# Linux: sudo apt-get install ffmpeg
```

**OpenAI API Errors**
- Verify API key is correct
- Check API usage limits
- System falls back to rule-based parsing

**Video Processing Failures**
- Check video format compatibility
- Verify sufficient disk space
- Review FFmpeg error logs

**Job Status Not Updating**
- Check background task execution
- Verify job manager initialization
- Review async processing logs

## 📞 Support

For technical support and questions:
- Create an issue on GitHub
- Check the API documentation at `/docs`
- Review the test files for usage examples
- Run the demo scripts for functionality verification

---

**Built with ❤️ for SmartClips - Making video editing intelligent and accessible.**
