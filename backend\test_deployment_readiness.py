#!/usr/bin/env python3
"""
Deployment Readiness Assessment
Verify dependencies, database setup, and production configurations
"""

import os
import requests
import json
import subprocess
import sqlite3
from pathlib import Path
from typing import Dict, Any, List

BASE_URL = "http://localhost:8000"

class DeploymentReadinessTester:
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.successes = []
        
    def log_issue(self, category: str, message: str, severity: str = "error"):
        """Log deployment issues"""
        if severity == "error":
            self.issues.append(f"[{category}] {message}")
            print(f"    ❌ {message}")
        elif severity == "warning":
            self.warnings.append(f"[{category}] {message}")
            print(f"    ⚠️  {message}")
        else:
            self.successes.append(f"[{category}] {message}")
            print(f"    ✅ {message}")
    
    def test_dependencies(self) -> bool:
        """Test all required dependencies"""
        print("📦 Testing Dependencies...")
        
        # Check requirements.txt exists
        if not os.path.exists("requirements.txt"):
            self.log_issue("Dependencies", "requirements.txt not found", "error")
            return False
        else:
            self.log_issue("Dependencies", "requirements.txt found", "success")
        
        # Check critical Python packages
        critical_packages = [
            "fastapi", "uvicorn", "sqlalchemy", "cloudinary", 
            "openai", "moviepy", "requests", "python-dotenv",
            "passlib", "python-jose", "python-multipart"
        ]
        
        missing_packages = []
        for package in critical_packages:
            try:
                # Handle special package name mappings
                import_name = package.replace("-", "_")
                if package == "python-dotenv":
                    import_name = "dotenv"
                elif package == "python-jose":
                    import_name = "jose"
                elif package == "python-multipart":
                    import_name = "multipart"

                __import__(import_name)
                self.log_issue("Dependencies", f"{package} installed", "success")
            except ImportError:
                missing_packages.append(package)
                self.log_issue("Dependencies", f"{package} not installed", "error")
        
        # Check FFmpeg
        try:
            result = subprocess.run(["ffmpeg", "-version"], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                self.log_issue("Dependencies", "FFmpeg available", "success")
            else:
                self.log_issue("Dependencies", "FFmpeg not working", "error")
        except (FileNotFoundError, subprocess.TimeoutExpired):
            self.log_issue("Dependencies", "FFmpeg not found", "error")
        
        return len(missing_packages) == 0
    
    def test_database_setup(self) -> bool:
        """Test database configuration and connectivity"""
        print("\n🗄️  Testing Database Setup...")
        
        # Check if database file exists (SQLite)
        db_files = ["quikclips.db", "smartclips.db", "smartclips_chat.db"]
        db_found = False
        
        for db_file in db_files:
            if os.path.exists(db_file):
                self.log_issue("Database", f"Database file {db_file} found", "success")
                db_found = True
                
                # Test database connectivity
                try:
                    conn = sqlite3.connect(db_file)
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                    tables = cursor.fetchall()
                    conn.close()
                    
                    if tables:
                        self.log_issue("Database", f"Database {db_file} has {len(tables)} tables", "success")
                    else:
                        self.log_issue("Database", f"Database {db_file} is empty", "warning")
                        
                except Exception as e:
                    self.log_issue("Database", f"Database {db_file} connection failed: {str(e)}", "error")
        
        if not db_found:
            self.log_issue("Database", "No database files found", "warning")
        
        # Test database through API
        try:
            response = requests.get(f"{BASE_URL}/health", timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                if health_data.get("database") == "connected":
                    self.log_issue("Database", "API reports database connected", "success")
                else:
                    self.log_issue("Database", "API reports database issues", "error")
            else:
                self.log_issue("Database", "Cannot check database through API", "error")
        except Exception as e:
            self.log_issue("Database", f"API health check failed: {str(e)}", "error")
        
        return db_found
    
    def test_configuration_files(self) -> bool:
        """Test configuration files"""
        print("\n⚙️  Testing Configuration Files...")
        
        # Check .env file
        if os.path.exists(".env"):
            self.log_issue("Config", ".env file found", "success")
            
            # Check .env content
            try:
                with open(".env", "r") as f:
                    env_content = f.read()
                    
                required_vars = [
                    "CLOUDINARY_CLOUD_NAME", "CLOUDINARY_API_KEY", "CLOUDINARY_API_SECRET",
                    "OPENAI_API_KEY", "ELEVENLABS_API_KEY", "SECRET_KEY"
                ]
                
                for var in required_vars:
                    if var in env_content and not env_content.count(f"{var}=your_") > 0:
                        self.log_issue("Config", f"{var} configured in .env", "success")
                    else:
                        self.log_issue("Config", f"{var} not properly configured", "warning")
                        
            except Exception as e:
                self.log_issue("Config", f"Error reading .env: {str(e)}", "error")
        else:
            self.log_issue("Config", ".env file not found", "error")
        
        # Check Google credentials file
        if os.path.exists("angular-argon-452914-f1-17bffe088833.json"):
            self.log_issue("Config", "Google credentials file found", "success")
        else:
            self.log_issue("Config", "Google credentials file not found", "warning")
        
        return os.path.exists(".env")
    
    def test_api_functionality(self) -> bool:
        """Test core API functionality"""
        print("\n🌐 Testing API Functionality...")
        
        # Test basic endpoints
        endpoints_to_test = [
            ("/", "GET", "Root endpoint"),
            ("/health", "GET", "Health check"),
            ("/validate-url", "POST", "URL validation")
        ]
        
        api_working = True
        
        for endpoint, method, description in endpoints_to_test:
            try:
                if method == "GET":
                    response = requests.get(f"{BASE_URL}{endpoint}", timeout=10)
                elif method == "POST":
                    # Test with sample data
                    test_data = {"url": "https://www.youtube.com/watch?v=test"}
                    response = requests.post(f"{BASE_URL}{endpoint}", json=test_data, timeout=10)
                
                if response.status_code in [200, 422]:  # 422 is acceptable for validation endpoints
                    self.log_issue("API", f"{description} working", "success")
                else:
                    self.log_issue("API", f"{description} failed: {response.status_code}", "error")
                    api_working = False
                    
            except Exception as e:
                self.log_issue("API", f"{description} error: {str(e)}", "error")
                api_working = False
        
        return api_working
    
    def test_security_configuration(self) -> bool:
        """Test security configurations"""
        print("\n🔒 Testing Security Configuration...")
        
        security_ok = True
        
        # Check CORS configuration
        try:
            response = requests.options(f"{BASE_URL}/", 
                                      headers={"Origin": "https://malicious-site.com"})
            cors_origin = response.headers.get("Access-Control-Allow-Origin")
            
            if cors_origin == "*":
                self.log_issue("Security", "CORS allows all origins (development only)", "warning")
            elif cors_origin:
                self.log_issue("Security", f"CORS configured: {cors_origin}", "success")
            else:
                self.log_issue("Security", "CORS not configured", "error")
                security_ok = False
                
        except Exception as e:
            self.log_issue("Security", f"CORS test failed: {str(e)}", "error")
        
        # Check if sensitive files are exposed
        sensitive_files = [".env", "angular-argon-452914-f1-17bffe088833.json"]
        for file in sensitive_files:
            try:
                response = requests.get(f"{BASE_URL}/static/{file}", timeout=5)
                if response.status_code == 404:
                    self.log_issue("Security", f"{file} not exposed via web", "success")
                else:
                    self.log_issue("Security", f"{file} may be exposed via web", "error")
                    security_ok = False
            except:
                self.log_issue("Security", f"{file} not exposed via web", "success")
        
        return security_ok
    
    def test_production_readiness(self) -> bool:
        """Test production-specific configurations"""
        print("\n🚀 Testing Production Readiness...")
        
        production_ready = True
        
        # Check for development-only settings
        try:
            with open("main.py", "r") as f:
                main_content = f.read()
                
                if 'allow_origins=["*"]' in main_content:
                    self.log_issue("Production", "CORS allows all origins (should be restricted)", "warning")
                
                if "development_secret_key" in main_content:
                    self.log_issue("Production", "Using development secret key", "error")
                    production_ready = False
                else:
                    self.log_issue("Production", "Secret key appears to be configured", "success")
                    
        except Exception as e:
            self.log_issue("Production", f"Cannot check main.py: {str(e)}", "warning")
        
        # Check static file serving
        static_dir = Path("static")
        if static_dir.exists():
            self.log_issue("Production", "Static directory exists", "success")
        else:
            self.log_issue("Production", "Static directory not found", "warning")
        
        # Check temp directory
        temp_dir = Path("temp")
        if temp_dir.exists():
            self.log_issue("Production", "Temp directory exists", "success")
        else:
            self.log_issue("Production", "Temp directory not found", "warning")
        
        return production_ready
    
    def run_all_tests(self):
        """Run all deployment readiness tests"""
        print("🚀 Starting Deployment Readiness Assessment...")
        print("="*70)
        
        # Run all test suites
        deps_ok = self.test_dependencies()
        db_ok = self.test_database_setup()
        config_ok = self.test_configuration_files()
        api_ok = self.test_api_functionality()
        security_ok = self.test_security_configuration()
        prod_ok = self.test_production_readiness()
        
        # Generate final assessment
        print("\n" + "="*70)
        print("📊 DEPLOYMENT READINESS SUMMARY")
        print("="*70)
        
        print(f"\n✅ Successes: {len(self.successes)}")
        print(f"⚠️  Warnings: {len(self.warnings)}")
        print(f"❌ Critical Issues: {len(self.issues)}")
        
        if self.issues:
            print(f"\n❌ Critical Issues to Fix:")
            for issue in self.issues:
                print(f"  • {issue}")
        
        if self.warnings:
            print(f"\n⚠️  Warnings to Consider:")
            for warning in self.warnings:
                print(f"  • {warning}")
        
        # Overall assessment
        print(f"\n🎯 Overall Deployment Readiness:")
        
        if len(self.issues) == 0:
            if len(self.warnings) <= 3:
                print("  ✅ READY FOR DEPLOYMENT")
                print("  🚀 All critical systems are working correctly")
            else:
                print("  ⚠️  MOSTLY READY - Address warnings for optimal deployment")
        else:
            print("  ❌ NOT READY FOR DEPLOYMENT")
            print("  🔧 Please fix critical issues before deploying")
        
        # Save detailed report
        report = {
            "timestamp": __import__("datetime").datetime.now().isoformat(),
            "successes": self.successes,
            "warnings": self.warnings,
            "issues": self.issues,
            "ready_for_deployment": len(self.issues) == 0
        }
        
        with open("deployment_readiness_report.json", "w") as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: deployment_readiness_report.json")

if __name__ == "__main__":
    tester = DeploymentReadinessTester()
    tester.run_all_tests()
