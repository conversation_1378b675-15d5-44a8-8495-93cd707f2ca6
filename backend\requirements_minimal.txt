# SmartClips Minimal Production Requirements
# Use this file for deployment-ready version with main_minimal.py

# Core FastAPI and web framework dependencies
fastapi==0.104.1
uvicorn==0.24.0
python-multipart==0.0.6

# Database and ORM
sqlalchemy==2.0.23
pydantic==2.5.0

# Authentication and security
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
bcrypt==4.1.2

# HTTP requests and API integrations
requests==2.31.0
openai==1.3.7
cloudinary==1.36.0

# Video processing (basic)
moviepy==1.0.3
yt-dlp==2023.11.16
ffmpeg-python==0.2.0

# Image processing and utilities
Pillow==10.1.0
numpy==2.2.6
opencv-contrib-python==*********

# Environment and configuration
python-dotenv==1.0.0

# Compatible MediaPipe version (for facial AI features)
mediapipe==0.10.9
protobuf==3.20.3

# Additional utilities for image processing
scikit-image==0.25.2
tifffile==2025.8.28
