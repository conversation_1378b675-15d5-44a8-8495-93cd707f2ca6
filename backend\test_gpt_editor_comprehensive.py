"""
Comprehensive Test Suite for GPT Editor

This module provides unit tests, integration tests, and end-to-end tests
for the AI-powered video editor system.
"""

import unittest
import asyncio
import os
import tempfile
import json
from unittest.mock import Mock, patch, AsyncMock

# Import modules to test
from gpt_editor_service import GPTEditorService
from ffmpeg_command_generator import <PERSON><PERSON><PERSON><PERSON><PERSON>mandGenerator
from gpt_editor_jobs import GPTEditorJobManager

class TestGPTEditorService(unittest.TestCase):
    """Unit tests for GPT Editor Service"""
    
    def setUp(self):
        self.service = GPTEditorService()
    
    def test_initialization(self):
        """Test service initialization"""
        self.assertIsNotNone(self.service)
        self.assertIsInstance(self.service.available_actions, dict)
        self.assertIsInstance(self.service.video_templates, dict)
        self.assertGreater(len(self.service.available_actions), 0)
        self.assertGreater(len(self.service.video_templates), 0)
    
    def test_fallback_parsing_trim(self):
        """Test fallback command parsing for trim commands"""
        commands = self.service._fallback_parse_command("trim video to 30 seconds")
        self.assertGreater(len(commands), 0)
        
        # Should detect trim action
        trim_commands = [cmd for cmd in commands if cmd['action'] == 'trim']
        self.assertGreater(len(trim_commands), 0)
    
    def test_fallback_parsing_subtitles(self):
        """Test fallback command parsing for subtitle commands"""
        commands = self.service._fallback_parse_command("add subtitles to this video")
        self.assertGreater(len(commands), 0)
        
        # Should detect subtitle action
        subtitle_commands = [cmd for cmd in commands if cmd['action'] == 'add_subtitles']
        self.assertGreater(len(subtitle_commands), 0)
    
    def test_fallback_parsing_tiktok(self):
        """Test fallback command parsing for TikTok commands"""
        commands = self.service._fallback_parse_command("make this TikTok ready")
        self.assertGreater(len(commands), 0)
        
        # Should detect template application
        template_commands = [cmd for cmd in commands if cmd['action'] == 'apply_template']
        self.assertGreater(len(template_commands), 0)
        
        if template_commands:
            self.assertEqual(template_commands[0]['parameters']['template'], 'tiktok')
    
    def test_template_retrieval(self):
        """Test template retrieval functionality"""
        template = self.service.get_template_by_name('tiktok')
        self.assertIsNotNone(template)
        self.assertIn('name', template)
        self.assertIn('description', template)
        
        # Test non-existent template
        template = self.service.get_template_by_name('nonexistent')
        self.assertIsNone(template)
    
    def test_processing_time_estimation(self):
        """Test processing time estimation"""
        commands = [
            {"action": "trim", "parameters": {}, "confidence": 0.9, "description": "test"},
            {"action": "add_subtitles", "parameters": {}, "confidence": 0.8, "description": "test"}
        ]
        
        estimated_time = self.service.estimate_processing_time(commands, 60)
        self.assertIsInstance(estimated_time, int)
        self.assertGreater(estimated_time, 0)

class TestFFmpegCommandGenerator(unittest.TestCase):
    """Unit tests for FFmpeg Command Generator"""
    
    def setUp(self):
        self.generator = FFmpegCommandGenerator()
    
    def test_initialization(self):
        """Test generator initialization"""
        self.assertIsNotNone(self.generator)
        self.assertIsNotNone(self.generator.ffmpeg_binary)
    
    def test_trim_command_generation(self):
        """Test trim command generation"""
        cmd = self.generator._generate_single_command(
            "input.mp4", "output.mp4", "trim", 
            {"start_time": 10, "end_time": 60}
        )
        
        self.assertIsNotNone(cmd)
        self.assertIn("ffmpeg", cmd[0])
        self.assertIn("-ss", cmd)
        self.assertIn("-to", cmd)
        self.assertIn("10", cmd)
        self.assertIn("60", cmd)
    
    def test_crop_command_generation(self):
        """Test crop command generation"""
        cmd = self.generator._generate_single_command(
            "input.mp4", "output.mp4", "crop",
            {"aspect_ratio": "16:9"}
        )
        
        self.assertIsNotNone(cmd)
        self.assertIn("ffmpeg", cmd[0])
        self.assertIn("-vf", cmd)
    
    def test_text_overlay_command_generation(self):
        """Test text overlay command generation"""
        cmd = self.generator._generate_single_command(
            "input.mp4", "output.mp4", "add_text",
            {"text": "Test Text", "position": "center"}
        )
        
        self.assertIsNotNone(cmd)
        self.assertIn("ffmpeg", cmd[0])
        self.assertIn("-vf", cmd)
        # Should contain drawtext filter
        vf_index = cmd.index("-vf")
        self.assertIn("drawtext", cmd[vf_index + 1])
    
    def test_unknown_action(self):
        """Test handling of unknown actions"""
        cmd = self.generator._generate_single_command(
            "input.mp4", "output.mp4", "unknown_action", {}
        )
        
        self.assertIsNone(cmd)
    
    def test_command_sequence_generation(self):
        """Test command sequence generation"""
        commands = [
            {"action": "trim", "parameters": {"start_time": 0, "end_time": 30}, "confidence": 0.9, "description": "test"},
            {"action": "crop", "parameters": {"aspect_ratio": "16:9"}, "confidence": 0.8, "description": "test"}
        ]
        
        sequence = self.generator.generate_command_sequence("input.mp4", "output.mp4", commands)
        
        self.assertEqual(len(sequence), 2)
        self.assertIsInstance(sequence, list)
        for cmd in sequence:
            self.assertIsInstance(cmd, list)
            self.assertIn("ffmpeg", cmd[0])

class TestGPTEditorJobManager(unittest.TestCase):
    """Unit tests for Job Manager"""
    
    def setUp(self):
        self.job_manager = GPTEditorJobManager()
    
    def test_job_creation(self):
        """Test job creation"""
        job_id = self.job_manager.create_job(
            command="test command",
            video_url="http://example.com/video.mp4"
        )
        
        self.assertIsNotNone(job_id)
        self.assertIn(job_id, self.job_manager.jobs)
        
        job_data = self.job_manager.jobs[job_id]
        self.assertEqual(job_data["command"], "test command")
        self.assertEqual(job_data["status"], "pending")
        self.assertEqual(job_data["progress"], 0.0)
    
    def test_job_status_retrieval(self):
        """Test job status retrieval"""
        job_id = self.job_manager.create_job(command="test")
        
        status = self.job_manager.get_job_status(job_id)
        self.assertIsNotNone(status)
        self.assertEqual(status["job_id"], job_id)
        self.assertEqual(status["status"], "pending")
        
        # Test non-existent job
        status = self.job_manager.get_job_status("nonexistent")
        self.assertIsNone(status)
    
    def test_job_cancellation(self):
        """Test job cancellation"""
        job_id = self.job_manager.create_job(command="test")
        
        success = self.job_manager.cancel_job(job_id)
        self.assertTrue(success)
        
        # Test cancelling non-existent job
        success = self.job_manager.cancel_job("nonexistent")
        self.assertFalse(success)
    
    def test_job_statistics(self):
        """Test job statistics"""
        # Create some test jobs
        job1 = self.job_manager.create_job(command="test1")
        job2 = self.job_manager.create_job(command="test2")
        
        stats = self.job_manager.get_job_statistics()
        
        self.assertIsInstance(stats, dict)
        self.assertIn("total_jobs", stats)
        self.assertIn("status_breakdown", stats)
        self.assertGreaterEqual(stats["total_jobs"], 2)

class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system"""
    
    def setUp(self):
        self.service = GPTEditorService()
        self.generator = FFmpegCommandGenerator()
        self.job_manager = GPTEditorJobManager()
    
    async def test_end_to_end_parsing_and_generation(self):
        """Test end-to-end command parsing and FFmpeg generation"""
        # Parse a command
        commands = await self.service.parse_editing_command("trim video to 30 seconds")
        self.assertGreater(len(commands), 0)
        
        # Generate FFmpeg commands
        if commands:
            sequence = self.generator.generate_command_sequence(
                "input.mp4", "output.mp4", commands
            )
            self.assertIsInstance(sequence, list)
    
    def test_job_workflow(self):
        """Test complete job workflow"""
        # Create job
        job_id = self.job_manager.create_job(
            command="make this TikTok ready",
            video_url="http://example.com/test.mp4"
        )
        
        # Check initial status
        status = self.job_manager.get_job_status(job_id)
        self.assertEqual(status["status"], "pending")
        
        # Cancel job
        success = self.job_manager.cancel_job(job_id)
        self.assertTrue(success)

class TestAsyncFunctionality(unittest.IsolatedAsyncioTestCase):
    """Test async functionality"""
    
    async def test_async_command_parsing(self):
        """Test async command parsing"""
        service = GPTEditorService()
        
        commands = await service.parse_editing_command("add subtitles")
        self.assertIsInstance(commands, list)
        self.assertGreater(len(commands), 0)
    
    async def test_async_ffmpeg_execution(self):
        """Test async FFmpeg execution (mocked)"""
        generator = FFmpegCommandGenerator()
        
        # Mock the subprocess execution
        with patch('subprocess.run') as mock_run:
            mock_run.return_value.returncode = 0
            
            commands = [["ffmpeg", "-version"]]
            success = await generator.execute_command_sequence(commands)
            self.assertTrue(success)

def run_tests():
    """Run all tests"""
    print("🧪 Running GPT Editor Test Suite")
    print("=" * 50)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test classes
    suite.addTests(loader.loadTestsFromTestCase(TestGPTEditorService))
    suite.addTests(loader.loadTestsFromTestCase(TestFFmpegCommandGenerator))
    suite.addTests(loader.loadTestsFromTestCase(TestGPTEditorJobManager))
    suite.addTests(loader.loadTestsFromTestCase(TestIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Run async tests separately
    print("\n🔄 Running Async Tests...")
    async_suite = loader.loadTestsFromTestCase(TestAsyncFunctionality)
    async_result = runner.run(async_suite)
    
    # Summary
    total_tests = result.testsRun + async_result.testsRun
    total_failures = len(result.failures) + len(async_result.failures)
    total_errors = len(result.errors) + len(async_result.errors)
    
    print(f"\n📊 Test Summary:")
    print(f"   Total Tests: {total_tests}")
    print(f"   Passed: {total_tests - total_failures - total_errors}")
    print(f"   Failed: {total_failures}")
    print(f"   Errors: {total_errors}")
    
    if total_failures == 0 and total_errors == 0:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
    
    return total_failures == 0 and total_errors == 0

if __name__ == "__main__":
    success = run_tests()
