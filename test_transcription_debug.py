#!/usr/bin/env python3
"""
Debug the transcription process to find why it's failing
"""

import sys
import os
import tempfile
import logging
from pathlib import Path

# Add backend to path
sys.path.append('backend')

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    import speech_recognition as sr
    from pydub import AudioSegment
    from moviepy.editor import VideoFileClip
    print("✅ All required imports successful")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def test_speech_recognition():
    """Test if speech recognition is working"""
    print("\n🔍 Testing Speech Recognition...")
    
    try:
        r = sr.Recognizer()
        print(f"✅ Speech recognizer created: {type(r)}")
        
        # Test microphone (if available)
        try:
            mic_list = sr.Microphone.list_microphone_names()
            print(f"✅ Available microphones: {len(mic_list)}")
        except Exception as e:
            print(f"⚠️  Microphone test failed: {e}")
        
        # Test Google Speech Recognition
        try:
            # Create a simple test audio (silence)
            test_audio = AudioSegment.silent(duration=1000)  # 1 second of silence
            
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                test_audio.export(temp_file.name, format="wav")
                
                with sr.AudioFile(temp_file.name) as source:
                    audio_data = r.record(source)
                    
                # Try to recognize (should fail gracefully for silence)
                try:
                    result = r.recognize_google(audio_data)
                    print(f"✅ Google Speech Recognition working: '{result}'")
                except sr.UnknownValueError:
                    print("✅ Google Speech Recognition working (no speech detected in silence)")
                except sr.RequestError as e:
                    print(f"❌ Google Speech Recognition API error: {e}")
                    return False
                
                os.unlink(temp_file.name)
                
        except Exception as e:
            print(f"❌ Speech recognition test failed: {e}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Speech recognition setup failed: {e}")
        return False

def test_audio_processing():
    """Test audio processing capabilities"""
    print("\n🔍 Testing Audio Processing...")
    
    try:
        # Test pydub
        test_audio = AudioSegment.silent(duration=5000)  # 5 seconds
        print(f"✅ Created test audio: {len(test_audio)}ms")
        
        # Test chunking
        chunk_length_ms = 2000  # 2 seconds
        chunks = [test_audio[i:i + chunk_length_ms]
                  for i in range(0, len(test_audio), chunk_length_ms)]
        print(f"✅ Audio chunking works: {len(chunks)} chunks")
        
        # Test export
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            test_audio.export(temp_file.name, format="wav")
            
            if os.path.exists(temp_file.name) and os.path.getsize(temp_file.name) > 0:
                print(f"✅ Audio export works: {os.path.getsize(temp_file.name)} bytes")
                os.unlink(temp_file.name)
                return True
            else:
                print("❌ Audio export failed")
                return False
                
    except Exception as e:
        print(f"❌ Audio processing test failed: {e}")
        return False

def test_video_audio_extraction():
    """Test video audio extraction"""
    print("\n🔍 Testing Video Audio Extraction...")
    
    # We can't test with a real video file, but we can test the process
    try:
        # Test if moviepy can create a test video
        from moviepy.editor import ColorClip, AudioFileClip
        
        # Create a simple test video with audio
        test_clip = ColorClip(size=(640, 480), color=(255, 0, 0), duration=2)
        print("✅ Test video clip created")
        
        # Test audio extraction process (without actual file)
        print("✅ Video processing imports working")
        
        return True
        
    except Exception as e:
        print(f"❌ Video audio extraction test failed: {e}")
        return False

def debug_transcription_function():
    """Debug the actual transcription function"""
    print("\n🔍 Debugging Transcription Function...")
    
    try:
        from backend.video_processing import transcribe_video
        print("✅ Transcription function imported")
        
        # The function expects a video file, so we can't test it directly
        # But we can check if it's properly defined
        import inspect
        sig = inspect.signature(transcribe_video)
        print(f"✅ Function signature: {sig}")
        
        return True
        
    except Exception as e:
        print(f"❌ Transcription function debug failed: {e}")
        return False

def check_environment():
    """Check environment and dependencies"""
    print("\n🔍 Checking Environment...")
    
    # Check Python version
    print(f"Python version: {sys.version}")
    
    # Check required packages
    packages = [
        'speech_recognition',
        'pydub', 
        'moviepy',
        'openai'
    ]
    
    for package in packages:
        try:
            __import__(package)
            print(f"✅ {package}: Available")
        except ImportError:
            print(f"❌ {package}: Missing")
    
    # Check for system dependencies
    try:
        import subprocess
        
        # Check for ffmpeg
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ FFmpeg: Available")
        else:
            print("❌ FFmpeg: Not working")
    except Exception as e:
        print(f"⚠️  FFmpeg check failed: {e}")
    
    return True

def main():
    """Run all transcription debug tests"""
    print("🚀 Debugging SmartClips Transcription Issues\n")
    
    tests = [
        ("Environment Check", check_environment),
        ("Speech Recognition", test_speech_recognition),
        ("Audio Processing", test_audio_processing),
        ("Video Audio Extraction", test_video_audio_extraction),
        ("Transcription Function", debug_transcription_function),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print(f"\n{'='*60}")
    print("📊 TRANSCRIPTION DEBUG RESULTS")
    print('='*60)
    print(f"Total Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n✅ All transcription components working!")
        print("🔍 The issue may be in the specific transcription logic or API calls.")
    else:
        print(f"\n⚠️  {total - passed} transcription components have issues.")
        print("🛠️  These need to be fixed for transcription to work.")
    
    # Specific recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    if passed < total:
        print("1. Install missing dependencies")
        print("2. Check internet connection for Google Speech API")
        print("3. Verify FFmpeg installation")
    else:
        print("1. Check Google Speech Recognition API limits")
        print("2. Verify audio quality and format")
        print("3. Add more detailed logging to transcription function")
    
    return passed == total

if __name__ == "__main__":
    main()
