"""
Demo: Chat History and Presets API Integration

This script demonstrates the complete chat history and preset functionality
through the API endpoints, showing real-world usage scenarios.
"""

import asyncio
import logging
import json
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def demo_complete_workflow():
    """Demonstrate the complete chat history and preset workflow"""
    
    logger.info("🎬 SmartClips GPT Editor - Chat History & Presets Demo")
    logger.info("=" * 70)
    
    # Import services for direct demonstration
    from chat_history_service import chat_service
    from gpt_editor_service import GPTEditorService
    from gpt_editor_jobs import job_manager
    
    gpt_service = GPTEditorService()
    
    # Demo user
    demo_user_id = "demo_user_2025"
    demo_session_id = "session_" + str(int(datetime.now().timestamp()))
    
    logger.info(f"👤 Demo User: {demo_user_id}")
    logger.info(f"🔗 Session: {demo_session_id}")
    
    # Scenario 1: Complex TikTok editing request
    logger.info(f"\n🎯 SCENARIO 1: Complex TikTok Video Creation")
    logger.info("=" * 50)
    
    complex_prompt = "Take this 3-minute interview video and create a viral TikTok: trim to the most engaging 45 seconds, add trendy subtitles with emojis, crop to vertical format, boost colors by 20%, and add our brand watermark"
    
    logger.info(f"📝 User Prompt: '{complex_prompt}'")
    
    # Step 1: Create job (this saves user message automatically)
    job_id_1 = job_manager.create_job(
        command=complex_prompt,
        video_url="https://example.com/interview.mp4",
        template="tiktok",
        options={"max_duration": 45, "brand_watermark": True},
        user_id=demo_user_id,
        session_id=demo_session_id
    )
    
    logger.info(f"✅ Job Created: {job_id_1}")
    
    # Step 2: Parse and process (simulated)
    parsed_commands = await gpt_service.parse_editing_command(complex_prompt)
    avg_confidence = sum(cmd['confidence'] for cmd in parsed_commands) / len(parsed_commands)
    
    logger.info(f"🤖 AI Parsed {len(parsed_commands)} commands (confidence: {avg_confidence:.2f}):")
    for i, cmd in enumerate(parsed_commands, 1):
        logger.info(f"   {i}. {cmd['action'].upper()}: {cmd['description']}")
    
    # Step 3: Simulate successful completion
    chat_service.update_processing_status(
        job_id=job_id_1,
        status="completed",
        result_url="https://res.cloudinary.com/demo/video/upload/viral_tiktok_result.mp4",
        processing_time=28.5
    )
    
    logger.info(f"✅ Job Completed Successfully!")
    logger.info(f"📹 Result: https://res.cloudinary.com/demo/video/upload/viral_tiktok_result.mp4")
    
    # Scenario 2: Podcast editing request
    logger.info(f"\n🎯 SCENARIO 2: Professional Podcast Enhancement")
    logger.info("=" * 50)
    
    podcast_prompt = "Transform this raw podcast recording: enhance audio quality, add professional subtitles, apply podcast template, and ensure it's under 10 minutes"
    
    logger.info(f"📝 User Prompt: '{podcast_prompt}'")
    
    job_id_2 = job_manager.create_job(
        command=podcast_prompt,
        video_url="https://example.com/podcast_raw.mp4",
        template="podcast",
        options={"max_duration": 600, "professional_style": True},
        user_id=demo_user_id,
        session_id=demo_session_id
    )
    
    logger.info(f"✅ Job Created: {job_id_2}")
    
    # Parse and complete
    parsed_commands_2 = await gpt_service.parse_editing_command(podcast_prompt)
    
    chat_service.update_processing_status(
        job_id=job_id_2,
        status="completed",
        result_url="https://res.cloudinary.com/demo/video/upload/professional_podcast.mp4",
        processing_time=45.2
    )
    
    logger.info(f"✅ Podcast Enhancement Completed!")
    
    # Step 4: Check chat history
    logger.info(f"\n📜 CHAT HISTORY RETRIEVAL")
    logger.info("-" * 30)
    
    chat_history = chat_service.get_chat_history(
        user_id=demo_user_id,
        session_id=demo_session_id,
        limit=10
    )
    
    logger.info(f"📊 Retrieved {len(chat_history)} messages from this session:")
    
    for msg in chat_history:
        msg_type = msg['message_type']
        timestamp = msg['timestamp'][:19]  # Remove microseconds
        content_preview = msg['content'][:60] + "..." if len(msg['content']) > 60 else msg['content']
        
        type_emoji = "👤" if msg_type == "user" else "🤖"
        logger.info(f"   {type_emoji} [{timestamp}] {content_preview}")
        
        if msg.get('job_id') and msg.get('processing_status'):
            status_emoji = "✅" if msg['processing_status'] == "completed" else "⏳"
            logger.info(f"      {status_emoji} Job: {msg['job_id']} ({msg['processing_status']})")
    
    # Step 5: Check auto-created presets
    logger.info(f"\n🎨 AUTO-CREATED PRESETS")
    logger.info("-" * 25)
    
    user_presets = chat_service.get_presets(user_id=demo_user_id, limit=10)
    
    logger.info(f"📊 Found {len(user_presets)} presets for user:")
    
    for preset in user_presets:
        logger.info(f"   🎨 '{preset['name']}'")
        logger.info(f"      Category: {preset['category']}")
        logger.info(f"      Commands: {len(preset['commands'])}")
        logger.info(f"      Confidence: {preset['avg_confidence']:.2f}")
        logger.info(f"      Used: {preset['usage_count']} times")
        logger.info(f"      Original: {preset['original_prompt'][:50]}...")
    
    # Step 6: Create manual preset
    logger.info(f"\n🛠️  MANUAL PRESET CREATION")
    logger.info("-" * 30)
    
    if user_presets:
        # Create a manual preset from the first job
        manual_preset_id = chat_service.create_preset_from_successful_job(
            job_id=job_id_1,
            preset_name="Viral TikTok Creator",
            description="Complete workflow for creating viral TikTok content from long-form videos",
            is_public=True
        )
        
        if manual_preset_id:
            logger.info(f"✅ Manual preset created: {manual_preset_id}")
            logger.info(f"   Name: 'Viral TikTok Creator'")
            logger.info(f"   Public: Yes")
            logger.info(f"   Based on job: {job_id_1}")
        else:
            logger.info(f"⚠️  Manual preset creation failed (might already exist)")
    
    # Step 7: Use preset for new job
    logger.info(f"\n🔄 PRESET USAGE DEMONSTRATION")
    logger.info("-" * 35)
    
    if user_presets:
        test_preset = user_presets[0]
        logger.info(f"🎨 Using preset: '{test_preset['name']}'")
        
        # Use the preset
        preset_data = chat_service.use_preset(test_preset['id'], demo_user_id)
        
        if preset_data:
            logger.info(f"✅ Preset applied successfully!")
            logger.info(f"   Original prompt: {preset_data['original_prompt'][:50]}...")
            logger.info(f"   Commands to execute: {len(preset_data['commands'])}")
            
            # Create new job from preset
            preset_job_id = job_manager.create_job(
                command=preset_data['original_prompt'],
                video_url="https://example.com/new_video.mp4",
                template=preset_data.get('template_config', {}).get('template'),
                options={"preset_used": test_preset['name']},
                user_id=demo_user_id,
                session_id=demo_session_id
            )
            
            logger.info(f"✅ New job created from preset: {preset_job_id}")
    
    # Step 8: Get public presets
    logger.info(f"\n🌐 PUBLIC PRESETS SHOWCASE")
    logger.info("-" * 30)
    
    public_presets = chat_service.get_presets(is_public=True, limit=5)
    
    logger.info(f"📊 Available public presets: {len(public_presets)}")
    
    for preset in public_presets:
        logger.info(f"   🌟 '{preset['name']}'")
        logger.info(f"      Category: {preset['category']}")
        logger.info(f"      Success Rate: {preset['success_rate']:.1%}")
        logger.info(f"      Used by: {preset['usage_count']} users")
    
    # Step 9: Category-based preset search
    logger.info(f"\n🏷️  CATEGORY-BASED PRESET SEARCH")
    logger.info("-" * 35)
    
    categories = ["tiktok", "podcast", "gaming", "educational", "business"]
    
    for category in categories:
        category_presets = chat_service.get_presets(category=category, limit=3)
        logger.info(f"   📂 {category.title()}: {len(category_presets)} presets")
        
        for preset in category_presets:
            logger.info(f"      • {preset['name']} (confidence: {preset['avg_confidence']:.2f})")
    
    # Final Statistics
    logger.info(f"\n📊 SESSION STATISTICS")
    logger.info("=" * 25)
    
    # Get all user data
    all_chat = chat_service.get_chat_history(user_id=demo_user_id, limit=100)
    all_presets = chat_service.get_presets(user_id=demo_user_id, limit=50)
    
    user_messages = [msg for msg in all_chat if msg['message_type'] == 'user']
    assistant_messages = [msg for msg in all_chat if msg['message_type'] == 'assistant']
    successful_jobs = [msg for msg in assistant_messages if msg.get('success')]
    
    logger.info(f"👤 User Messages: {len(user_messages)}")
    logger.info(f"🤖 Assistant Responses: {len(assistant_messages)}")
    logger.info(f"✅ Successful Jobs: {len(successful_jobs)}")
    logger.info(f"🎨 Created Presets: {len(all_presets)}")
    
    if successful_jobs:
        avg_processing_time = sum(msg.get('processing_time', 0) for msg in successful_jobs) / len(successful_jobs)
        logger.info(f"⚡ Avg Processing Time: {avg_processing_time:.1f}s")
    
    if all_presets:
        total_preset_usage = sum(p['usage_count'] for p in all_presets)
        avg_preset_confidence = sum(p['avg_confidence'] for p in all_presets) / len(all_presets)
        logger.info(f"🔄 Total Preset Usage: {total_preset_usage}")
        logger.info(f"🎯 Avg Preset Confidence: {avg_preset_confidence:.2f}")
    
    success_rate = len(successful_jobs) / len(assistant_messages) * 100 if assistant_messages else 0
    logger.info(f"📈 Success Rate: {success_rate:.1f}%")
    
    # API Endpoints Summary
    logger.info(f"\n🌐 API ENDPOINTS AVAILABLE")
    logger.info("=" * 30)
    
    endpoints = [
        ("GET", "/api/gpt-editor/chat-history", "Retrieve chat history"),
        ("POST", "/api/gpt-editor/create-preset", "Create preset from job"),
        ("GET", "/api/gpt-editor/presets", "Get available presets"),
        ("POST", "/api/gpt-editor/use-preset/{id}", "Use preset for new job"),
        ("POST", "/api/gpt-editor/process", "Process with chat history"),
        ("GET", "/api/gpt-editor/job/{id}", "Get job status with chat context")
    ]
    
    for method, endpoint, description in endpoints:
        logger.info(f"   {method:4s} {endpoint:35s} - {description}")
    
    # Sample API Usage
    logger.info(f"\n📡 SAMPLE API USAGE")
    logger.info("-" * 20)
    
    sample_requests = {
        "Get Chat History": {
            "method": "GET",
            "url": "/api/gpt-editor/chat-history?session_id=session_123&limit=20",
            "response": {
                "chat_history": [
                    {
                        "id": "msg_123",
                        "message_type": "user",
                        "content": "Make this TikTok ready",
                        "timestamp": "2025-08-24T23:51:23",
                        "job_id": "job_456"
                    }
                ]
            }
        },
        "Create Preset": {
            "method": "POST",
            "url": "/api/gpt-editor/create-preset",
            "body": {
                "job_id": "job_456",
                "name": "TikTok Viral Template",
                "description": "Complete TikTok optimization workflow",
                "is_public": True
            },
            "response": {
                "preset_id": "preset_789",
                "message": "Preset created successfully"
            }
        },
        "Use Preset": {
            "method": "POST",
            "url": "/api/gpt-editor/use-preset/preset_789",
            "response": {
                "job_id": "job_101112",
                "preset_name": "TikTok Viral Template",
                "message": "Job created from preset successfully"
            }
        }
    }
    
    for name, example in sample_requests.items():
        logger.info(f"\n   📋 {name}:")
        logger.info(f"      {example['method']} {example['url']}")
        if 'body' in example:
            logger.info(f"      Body: {json.dumps(example['body'], indent=8)}")
        logger.info(f"      Response: {json.dumps(example['response'], indent=8)}")
    
    logger.info(f"\n" + "=" * 70)
    logger.info("🎉 CHAT HISTORY & PRESETS DEMO COMPLETE!")
    logger.info("=" * 70)
    
    logger.info(f"✅ Successfully demonstrated:")
    logger.info(f"   • Automatic chat history saving")
    logger.info(f"   • AI-powered preset creation")
    logger.info(f"   • Manual preset management")
    logger.info(f"   • Preset reuse and sharing")
    logger.info(f"   • Category-based organization")
    logger.info(f"   • Usage analytics and tracking")
    logger.info(f"   • Complete API integration")
    
    logger.info(f"\n🚀 The SmartClips GPT Editor now includes:")
    logger.info(f"   📝 Complete chat history for all editing sessions")
    logger.info(f"   🎨 Automatic preset creation from successful workflows")
    logger.info(f"   🔄 One-click preset reuse for common editing tasks")
    logger.info(f"   📊 Analytics and usage tracking")
    logger.info(f"   🌐 Public preset sharing and discovery")
    logger.info(f"   🏷️  Category-based preset organization")

async def main():
    """Main demo function"""
    await demo_complete_workflow()

if __name__ == "__main__":
    asyncio.run(main())
