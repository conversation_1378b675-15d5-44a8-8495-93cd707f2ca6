import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { Loader2 } from "lucide-react";

const AuthCallback: React.FC = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    const handleRedirect = () => {
      if (isLoading) return;

      if (!isAuthenticated) {
        // User not authenticated, redirect to login
        navigate("/login");
        return;
      }

      if (user) {
        console.log("AuthCallback - User data:", {
          subscription: user.subscription,
          credits: user.credits,
          id: user.id,
        });

        // Check if user is a returning user with any paid subscription
        const hasActivePlan =
          user.subscription &&
          user.subscription !== "free" &&
          user.subscription !== null;

        // Also check if user has credits (indicates they've paid before)
        const hasCredits = user.credits && user.credits > 0;

        if (hasActivePlan || hasCredits) {
          // Returning user with subscription or credits - go to dashboard
          console.log("Redirecting returning user to dashboard");
          navigate("/dashboard");
        } else {
          // First-time user or user without subscription - go to payment
          console.log("Redirecting first-time user to payment");
          navigate("/payment");
        }
      }
    };

    // Add a small delay to ensure auth state is fully loaded
    const timer = setTimeout(handleRedirect, 1000);

    return () => clearTimeout(timer);
  }, [user, isAuthenticated, isLoading, navigate]);

  return (
    <div className="min-h-screen bg-black flex items-center justify-center">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin text-white mx-auto mb-4" />
        <p className="text-white text-lg">Completing sign in...</p>
        <p className="text-gray-400 text-sm mt-2">
          Please wait while we redirect you
        </p>
      </div>
    </div>
  );
};

export default AuthCallback;
