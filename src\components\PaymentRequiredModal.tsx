// src/components/PaymentRequiredModal.tsx
import * as React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

type Props = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title?: string;
  description?: string;
  /** Error ya server message jo content mein dikhega */
  errorMessage?: string;
  /** Footer button ka label */
  primaryButtonText?: string;
  onAction: () => void; // e.g. navigate('/vip-member')
  showCancel?: boolean;
};

const PaymentRequiredModal: React.FC<Props> = ({
  open,
  onOpenChange,
  title = 'Upgrade Required',
  description = 'This action requires a VIP plan.',
  errorMessage,
  primaryButtonText = 'Go to VIP',
  onAction,
  showCancel = true,
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {/* width + wrapping + scroll safety */}
      <DialogContent className="sm:max-w-md w-[92vw]">
        <DialogHeader>
          <DialogTitle className="break-words">{title}</DialogTitle>
          {description ? (
            <DialogDescription className="text-sm text-muted-foreground whitespace-pre-wrap break-words">
              {description}
            </DialogDescription>
          ) : null}
        </DialogHeader>

        {/* error/message box (inside modal content) */}
        {errorMessage ? (
          <div className="mt-2 rounded-md border border-red-500/30 bg-red-500/10 text-red-600 p-3 text-sm whitespace-pre-wrap break-words">
            {errorMessage}
          </div>
        ) : null}

        <DialogFooter className="sm:justify-center gap-2">
          {showCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="w-full sm:w-auto"
            >
              Close
            </Button>
          )}
          <Button
            type="button"
            onClick={onAction}
            className="w-full sm:w-auto text-base"
          >
            {primaryButtonText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentRequiredModal;
