import React, { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Slider } from "@/components/ui/slider";
import {
  Play,
  Pause,
  Skip<PERSON>ack,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Copy,
  Trash2,
  Plus,
  ZoomIn,
  ZoomOut,
  Palette,
} from "lucide-react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface TimelineClip {
  id: string;
  start: number;
  end: number;
  duration: number;
  url: string;
  title: string;
  track: number;
}
interface TimelineSegment {
  id: string;
  sourceStart: number;
  sourceEnd: number;
  duration: number;
  virtualStart: number;
  virtualEnd: number;
  type: "video" | "gap";
}
interface TimelineProps {
  clips: TimelineClip[];
  segments: TimelineSegment[];
  duration: number;
  currentTime: number;
  textOverlays: TextOverlay[];
  onTextOverlayDelete: (overlayId: string) => void;
  onFilterDelete: (clipId: string) => void;
  onClipMove: (draggedClipId: string, targetClipId: string) => void;
  onTextOverlayUpdate: (
    overlayId: string,
    updates: Partial<TextOverlay>
  ) => void;

  isPlaying: boolean;
  onTimeChange: (time: number) => void;
  onPlay: () => void;
  onPause: () => void;
  onClipUpdate: (clipId: string, updates: Partial<TimelineClip>) => void;
  onClipDelete: (clipId: string) => void;
  onClipAdd: (clip: Omit<TimelineClip, "id">) => void;
  onClipDuplicate: (clipId: string) => void;
  onClipSplit: (clipId: string, time: number) => void;
  onSpeedChange: (rate: number) => void;
}

const Timeline: React.FC<TimelineProps> = ({
  clips,
  segments,
  duration,
  currentTime,
  isPlaying,
  textOverlays,
  onTimeChange,
  onPlay,
  onPause,
  onClipUpdate,
  onClipDelete,
  onClipAdd,
  onClipDuplicate,
  onClipSplit,
  onSpeedChange,
  onTextOverlayUpdate,
  onClipMove,
  onTextOverlayDelete,
  onFilterDelete,
  // videoRef,
}) => {
  const [zoom, setZoom] = useState(1);
  const [selectedClip, setSelectedClip] = useState<string | null>(null);
  const [dragState, setDragState] = useState<{
    id: string;
    type: "move" | "resize-start" | "resize-end";
    elementType: "clip" | "text-overlay";
    startX: number;
    originalStart: number;
    originalEnd: number;
  } | null>(null);

  const [playbackRate, setPlaybackRate] = useState(1);

  useEffect(() => {
    onSpeedChange(playbackRate);
  }, [playbackRate, onSpeedChange]);

  const timelineRef = useRef<HTMLDivElement>(null);
  const TRACK_HEIGHT = 60;
  const TIMELINE_HEIGHT = 250;
  const PIXELS_PER_SECOND = 50 * zoom;

  // Convert time to pixel position
  const timeToPixels = (time: number) => time * PIXELS_PER_SECOND;

  // Convert pixel position to time
  const pixelsToTime = (pixels: number) => pixels / PIXELS_PER_SECOND;

  // Format time for display

  const formatTime = (seconds: number) => {
    const safeSeconds = Math.max(0, seconds);

    const mins = Math.floor(safeSeconds / 60);
    const secs = Math.floor(safeSeconds % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const totalPlayableDuration = duration;

  // Handle timeline click
  const handleTimelineClick = (e: React.MouseEvent) => {
    if (!timelineRef.current || dragState) return;
    const timeline = timelineRef.current;

    const rect = timelineRef.current.getBoundingClientRect();
    // Calculate the click position relative to the timeline
    const x = e.clientX - rect.left + timeline.scrollLeft;
    const time = pixelsToTime(x);
    onTimeChange(Math.max(0, Math.min(duration, time)));
  };

  // Handle clip mouse down
  const handleClipMouseDown = (
    e: React.MouseEvent,
    clipId: string,
    type: "move" | "resize-start" | "resize-end"
  ) => {
    e.stopPropagation();
    const clip = clips.find((c) => c.id === clipId);
    if (!clip) return;

    setDragState({
      clipId,
      type,
      startX: e.clientX,
      startTime: type === "resize-end" ? clip.end : clip.start,
    });
    setSelectedClip(clipId);
  };

  const handleElementMouseDown = (
    e: React.MouseEvent,
    id: string,
    elementType: "clip" | "text-overlay",
    action: "move" | "resize-start" | "resize-end"
  ) => {
    e.stopPropagation();

    const element = (elementType === "clip" ? clips : textOverlays).find(
      (item) => item.id === id
    );

    // Find the segment for the clip if it's a video clip
    const segment = segments.find((s) => s.id === id);

    const originalStart =
      elementType === "clip"
        ? segment?.virtualStart
        : (element as TextOverlay)?.startTime;
    const originalEnd =
      elementType === "clip"
        ? segment?.virtualEnd
        : (element as TextOverlay)?.endTime;

    if (
      element === undefined ||
      originalStart === undefined ||
      originalEnd === undefined
    )
      return;

    setDragState({
      id,
      type: action,
      elementType,
      startX: e.clientX,
      originalStart,
      originalEnd,
    });
    setSelectedClip(elementType === "clip" ? id : null);
  };

  useEffect(() => {
    if (!timelineRef.current || !isPlaying) return;

    const timeline = timelineRef.current;
    const pixelsPerSecond = 50 * zoom;
    const playheadPosition = currentTime * pixelsPerSecond;

    // Get the visible width of the timeline
    const visibleWidth = timeline.clientWidth;
    // Get the current scroll position
    const scrollLeft = timeline.scrollLeft;

    // Define a "safe zone" in the middle of the timeline (e.g., 40% to 60%)
    const safeZoneStart = scrollLeft + visibleWidth * 0.4;
    const safeZoneEnd = scrollLeft + visibleWidth * 0.6;

    // If the playhead moves outside the safe zone, adjust the scroll
    if (playheadPosition < safeZoneStart || playheadPosition > safeZoneEnd) {
      // Center the playhead in the view
      const targetScrollLeft = playheadPosition - visibleWidth / 2;

      timeline.scrollTo({
        left: targetScrollLeft,
        behavior: "smooth",
      });
    }
  }, [currentTime, isPlaying, zoom]);

  // Unified Drag Handler for all timeline elements
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!dragState) return;

      const deltaX = e.clientX - dragState.startX;
      const deltaTime = pixelsToTime(deltaX);

      // --- Logic for Text Overlays ---
      if (dragState.elementType === "text-overlay") {
        let updates: Partial<TextOverlay> = {};
        const currentDuration = dragState.originalEnd - dragState.originalStart;

        switch (dragState.type) {
          case "move":
            const newStart = Math.max(0, dragState.originalStart + deltaTime);
            updates = {
              startTime: newStart,
              endTime: newStart + currentDuration,
            };
            break;
          case "resize-start":
            const newResizeStart = Math.max(
              0,
              Math.min(
                dragState.originalEnd - 0.1,
                dragState.originalStart + deltaTime
              )
            );
            updates = { startTime: newResizeStart };
            break;
          case "resize-end":
            const newResizeEnd = Math.max(
              dragState.originalStart + 0.1,
              dragState.originalEnd + deltaTime
            );
            updates = { endTime: newResizeEnd };
            break;
        }

        if (Object.keys(updates).length > 0) {
          onTextOverlayUpdate(dragState.id, updates);
        }
      }

      // --- Logic for Video Clips ---
      if (dragState.elementType === "clip") {
        // Find the specific clip being dragged
        const clip = clips.find((c) => c.id === dragState.id);
        if (!clip) return;

        let updates: Partial<TimelineClip> = {};
        const currentDuration = clip.end - clip.start;

        switch (dragState.type) {
          case "move":
            console.warn("Moving video clips is not implemented yet.");
            break;
          case "resize-start":
            const newStartTime = Math.max(
              0,
              Math.min(clip.end - 0.1, dragState.originalStart + deltaTime)
            );
            updates = {
              start: newStartTime,
              duration: clip.end - newStartTime,
            };
            break;
          case "resize-end":
            const newEndTime = Math.max(
              clip.start + 0.1,
              dragState.originalEnd + deltaTime
            );
            updates = {
              end: newEndTime,
              duration: newEndTime - clip.start,
            };
            break;
        }

        if (Object.keys(updates).length > 0) {
          onClipUpdate(dragState.id, updates);
        }
      }
    };

    const handleMouseUp = () => {
      setDragState(null);
    };

    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, [dragState, clips, onClipUpdate, onTextOverlayUpdate, pixelsToTime]);

  // Render time markers
  const renderTimeMarkers = () => {
    const markers = [];
    const interval = zoom < 0.5 ? 10 : zoom < 1 ? 5 : 1;

    for (let time = 0; time <= duration; time += interval) {
      const x = timeToPixels(time);
      markers.push(
        <div
          key={time}
          className="absolute top-0 bottom-0 border-l border-gray-300"
          style={{ left: x }}
        >
          <span className="absolute -top-6 -left-4 text-xs text-gray-500">
            {formatTime(time)}
          </span>
        </div>
      );
    }

    return markers;
  };

  // Render text overlays
  const renderTextOverlays = () => {
    return textOverlays.map((overlay) => {
      const left = timeToPixels(overlay.startTime);
      const width = timeToPixels(overlay.endTime - overlay.startTime);

      return (
        <div
          key={overlay.id}
          className="absolute bg-purple-500 rounded border-2 border-purple-600 select-none cursor-move"
          style={{
            left,
            width,
            top: TRACK_HEIGHT * 2,
            height: TRACK_HEIGHT / 2 - 5,
            zIndex: 5,
          }}
          onMouseDown={(e) =>
            handleElementMouseDown(e, overlay.id, "text-overlay", "move")
          }
        >
          {/* Delete Button */}
          <button
            className="absolute -top-2 -right-2 z-10 w-4 h-4 bg-red-500 text-white rounded-full flex items-center justify-center text-xs opacity-0 group-hover:opacity-100"
            onClick={(e) => {
              e.stopPropagation();
              onTextOverlayDelete(overlay.id);
            }}
          >
            ×
          </button>
          {/* Resize Handles */}
          <div
            className="absolute left-0 top-0 bottom-0 w-2 bg-purple-300 cursor-ew-resize opacity-0 hover:opacity-100"
            onMouseDown={(e) =>
              handleElementMouseDown(
                e,
                overlay.id,
                "text-overlay",
                "resize-start"
              )
            }
          />
          <div
            className="absolute right-0 top-0 bottom-0 w-2 bg-purple-300 cursor-ew-resize opacity-0 hover:opacity-100"
            onMouseDown={(e) =>
              handleElementMouseDown(
                e,
                overlay.id,
                "text-overlay",
                "resize-end"
              )
            }
          />
          <div className="p-1 text-white text-xs truncate">{overlay.text}</div>
        </div>
      );
    });
  };

  // Render filters
  const renderFilters = () => {
    return segments.map((segment) => {
      const originalClip = clips.find((c) => c.id === segment.id);

      if (
        !originalClip ||
        originalClip.type === "gap" ||
        !originalClip.filter ||
        originalClip.filter === "none"
      ) {
        return null;
      }

      const left = timeToPixels(segment.virtualStart);
      const width = timeToPixels(segment.duration);

      return (
        <div
          key={`filter-${segment.id}`}
          className="absolute group bg-green-500 rounded border-2 border-green-600 select-none"
          style={{
            left,
            width,
            top: TRACK_HEIGHT + 25,
            height: TRACK_HEIGHT / 3,
            zIndex: 4,
          }}
        >
          {/* Delete Button */}
          <button
            className="absolute -top-2 -right-2 z-10 w-4 h-4 bg-red-500 text-white rounded-full flex items-center justify-center text-xs opacity-0 group-hover:opacity-100"
            onClick={(e) => {
              e.stopPropagation();
              onFilterDelete(originalClip.id);
            }}
          >
            ×
          </button>
          <div className="p-1 text-white text-xs truncate capitalize">
            {originalClip.filter}
          </div>
        </div>
      );
    });
  };

  const renderClips = () => {
    return segments.map((segment) => {
      const originalClip = clips.find((c) => c.id === segment.id);
      if (!originalClip) return null;

      const left = timeToPixels(segment.virtualStart);
      const width = Math.max(1, timeToPixels(segment.duration));
      const top = originalClip.track * TRACK_HEIGHT + 30;
      const isSelected = selectedClip === segment.id;

      // Shared drag-and-drop handlers
      const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
      };

      const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        const draggedClipId = e.dataTransfer.getData("application/json");
        onClipMove(draggedClipId, segment.id);
      };

      const handleDragStart = (e: React.DragEvent) => {
        // We can only drag video clips, not gaps
        if (originalClip.type === "video") {
          e.dataTransfer.setData("application/json", originalClip.id);
          e.dataTransfer.effectAllowed = "move";
        } else {
          e.preventDefault();
        }
      };

      if (originalClip.type === "gap") {
        return (
          <div
            key={segment.id}
            className="absolute bg-gray-200 rounded border border-dashed border-gray-400"
            style={{ left, width, top, height: TRACK_HEIGHT - 10 }}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          />
        );
      }

      return (
        <div
          key={segment.id}
          className={`absolute bg-blue-500 rounded border-2 cursor-grab select-none ${
            isSelected ? "border-blue-300 shadow-lg" : "border-blue-600"
          }`}
          style={{ left, width, top, height: TRACK_HEIGHT - 10 }}
          onClick={() => setSelectedClip(originalClip.id)}
          draggable // Make the element draggable
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          {/* Resizing is still handled by onMouseDown, which is fine */}
          <div
            className="absolute left-0 top-0 bottom-0 w-2 bg-blue-300 cursor-ew-resize"
            onMouseDown={(e) =>
              handleClipMouseDown(e, segment.id, "resize-start")
            }
          />
          <div
            className="absolute right-0 top-0 bottom-0 w-2 bg-blue-300 cursor-ew-resize"
            onMouseDown={(e) =>
              handleClipMouseDown(e, segment.id, "resize-end")
            }
          />
          <div className="p-2 text-white text-xs truncate pointer-events-none">
            <div className="font-medium">{originalClip.title}</div>
            <div className="opacity-75">{formatTime(segment.duration)}</div>
          </div>
        </div>
      );
    });
  };

  // Render playhead
  const renderPlayhead = () => {
    const x = timeToPixels(currentTime);
    return (
      <div
        className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-10 pointer-events-none"
        style={{
          left: x,
        }}
      >
        <div className="absolute -top-2 -left-2 w-4 h-4 bg-red-500 rounded-full" />
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {/* Transport Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => onTimeChange(Math.max(0, currentTime - 10))}
              >
                <SkipBack className="h-4 w-4" />
              </Button>

              <Button
                variant="outline"
                size="icon"
                onClick={isPlaying ? onPause : onPlay}
              >
                {isPlaying ? (
                  <Pause className="h-4 w-4" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
              </Button>

              <Button
                variant="outline"
                size="icon"
                onClick={() =>
                  onTimeChange(Math.min(duration, currentTime + 10))
                }
              >
                <SkipForward className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm font-mono">
                {formatTime(currentTime)} / {formatTime(totalPlayableDuration)}
              </span>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => setZoom(Math.max(0.1, zoom - 0.2))}
              >
                <ZoomOut className="h-4 w-4" />
              </Button>

              <span className="text-sm w-12 text-center">
                {Math.round(zoom * 100)}%
              </span>

              <Button
                variant="outline"
                size="icon"
                onClick={() => setZoom(Math.min(3, zoom + 0.2))}
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Timeline */}
      <Card>
        <CardContent className="p-0">
          <div
            ref={timelineRef}
            className="relative bg-gray-50 overflow-x-auto cursor-crosshair"
            style={{
              height: TIMELINE_HEIGHT,
            }}
            onClick={handleTimelineClick}
          >
            {/* This inner div has a calculated width, forcing the outer div to scroll */}
            <div
              className="relative h-full"
              style={{
                width: timeToPixels(duration),
                position: "relative",
              }}
            >
              {renderTimeMarkers()}
              {renderClips()}
              {renderTextOverlays()}
              {renderFilters()}
              {renderPlayhead()}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Clip Controls */}
      {selectedClip && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between flex-wrap gap-4">
              {/* Left Side: Selected Clip Title */}
              <div className="text-sm font-medium">
                Selected: {clips.find((c) => c.id === selectedClip)?.title}
              </div>

              {/* Right Side: Button Groups */}
              <div className="flex items-center gap-4">
                {/* Group 1: Editing Tools */}
                <div className="flex items-center gap-2">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Play className="h-4 w-4 mr-1" />
                        Speed ({playbackRate}x)
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {[0.5, 0.75, 1, 1.25, 1.5, 2].map((rate) => (
                        <DropdownMenuItem
                          key={rate}
                          onClick={() => setPlaybackRate(rate)}
                        >
                          {rate}x {rate === 1 && "(Normal)"}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Palette className="h-4 w-4 mr-1" />
                        Filters (
                        {clips.find((c) => c.id === selectedClip)?.filter ||
                          "none"}
                        )
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {["none", "grayscale", "sepia", "invert", "blur"].map(
                        (filter) => (
                          <DropdownMenuItem
                            key={filter}
                            onClick={() =>
                              onClipUpdate(selectedClip, { filter: filter })
                            }
                          >
                            {filter.charAt(0).toUpperCase() + filter.slice(1)}
                          </DropdownMenuItem>
                        )
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                {/* Group 2: Core Actions (Icon Buttons) */}
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => onClipDuplicate(selectedClip)}
                    title="Duplicate"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => onClipSplit(selectedClip, currentTime)}
                    title="Split at Playhead"
                  >
                    <Scissors className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="destructive" // Use a destructive variant for delete
                    size="icon"
                    onClick={() => {
                      onClipDelete(selectedClip);
                      setSelectedClip(null);
                    }}
                    title="Delete"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default Timeline;
