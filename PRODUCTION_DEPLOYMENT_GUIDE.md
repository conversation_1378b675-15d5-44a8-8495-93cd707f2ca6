# SmartClips Production Deployment Guide

## 🚀 Quick Start Deployment

### Option 1: Minimal Production Version (Recommended)

```bash
# 1. Navigate to backend directory
cd backend

# 2. Install minimal dependencies
pip install -r requirements_minimal.txt

# 3. Start production server
uvicorn main_minimal:app --host 0.0.0.0 --port 8000 --workers 4
```

### Option 2: Full Feature Version (Advanced)

```bash
# 1. Install all dependencies (may have conflicts)
pip install -r requirements.txt

# 2. Resolve any dependency conflicts
pip install mediapipe==0.10.9 protobuf==3.20.3

# 3. Start with full features
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

## 🔧 Environment Configuration

### Required Environment Variables

Create a `.env` file in the backend directory:

```env
# Database Configuration
DATABASE_URL=postgresql://user:password@host:port/database
# For development: DATABASE_URL=sqlite:///./smartclips.db

# Authentication
SECRET_KEY=your-super-secret-jwt-key-here

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key

# ElevenLabs Configuration
ELEVENLABS_API_KEY=your-elevenlabs-api-key

# Google Cloud (Optional)
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json
```

## 🐳 Docker Deployment

### Dockerfile

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements_minimal.txt .
RUN pip install --no-cache-dir -r requirements_minimal.txt

# Copy application code
COPY . .

# Expose port
EXPOSE 8000

# Start application
CMD ["uvicorn", "main_minimal:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Docker Compose

```yaml
version: '3.8'

services:
  backend:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/smartclips
      - SECRET_KEY=${SECRET_KEY}
      - CLOUDINARY_CLOUD_NAME=${CLOUDINARY_CLOUD_NAME}
      - CLOUDINARY_API_KEY=${CLOUDINARY_API_KEY}
      - CLOUDINARY_API_SECRET=${CLOUDINARY_API_SECRET}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
    depends_on:
      - db

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=smartclips
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
```

## ☁️ Cloud Deployment Options

### 1. Vercel (Recommended for Frontend)

```bash
# Deploy frontend
cd frontend
vercel --prod
```

### 2. Railway (Recommended for Backend)

```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway init
railway up
```

### 3. Heroku

```bash
# Create Heroku app
heroku create smartclips-backend

# Set environment variables
heroku config:set SECRET_KEY=your-secret-key
heroku config:set CLOUDINARY_CLOUD_NAME=your-cloud-name
# ... set all other env vars

# Deploy
git push heroku main
```

### 4. AWS EC2

```bash
# SSH into EC2 instance
ssh -i your-key.pem ubuntu@your-ec2-ip

# Install dependencies
sudo apt update
sudo apt install python3-pip nginx

# Clone repository
git clone https://github.com/your-username/smartclips.git
cd smartclips/backend

# Install Python dependencies
pip3 install -r requirements_minimal.txt

# Start with systemd service
sudo systemctl start smartclips
sudo systemctl enable smartclips
```

## 🔒 Security Considerations

### 1. Environment Variables
- Never commit `.env` files to version control
- Use strong, unique SECRET_KEY for JWT signing
- Rotate API keys regularly

### 2. CORS Configuration
Update CORS origins for production:

```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "https://your-frontend-domain.com",
        "https://smartclips.vercel.app"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### 3. Database Security
- Use PostgreSQL for production
- Enable SSL connections
- Use connection pooling
- Regular backups

## 📊 Monitoring and Logging

### 1. Application Monitoring

```python
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)
```

### 2. Health Checks

The `/health` endpoint provides comprehensive health information:

```json
{
  "status": "healthy",
  "timestamp": "2025-08-30T22:15:50.652712",
  "services": {
    "openai": true,
    "cloudinary": true,
    "elevenlabs": true,
    "ffmpeg": true
  },
  "ffmpeg_available": true
}
```

## 🧪 Testing in Production

### 1. API Endpoint Tests

```bash
# Test health endpoint
curl https://your-backend-domain.com/health

# Test authentication
curl -X POST https://your-backend-domain.com/users/ \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"testpass"}'
```

### 2. Load Testing

```bash
# Install Apache Bench
sudo apt install apache2-utils

# Test with 100 concurrent requests
ab -n 1000 -c 100 https://your-backend-domain.com/health
```

## 🔄 CI/CD Pipeline

### GitHub Actions Example

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.11
    
    - name: Install dependencies
      run: |
        cd backend
        pip install -r requirements_minimal.txt
    
    - name: Run tests
      run: |
        cd backend
        python -m pytest tests/
    
    - name: Deploy to production
      run: |
        # Your deployment commands here
        echo "Deploying to production..."
```

## 📈 Scaling Considerations

### 1. Horizontal Scaling
- Use multiple worker processes: `--workers 4`
- Load balancer (Nginx, AWS ALB)
- Database connection pooling

### 2. Caching
- Redis for session storage
- CDN for static assets
- Database query caching

### 3. Background Tasks
- Celery for video processing
- Redis as message broker
- Separate worker processes

## 🚨 Troubleshooting

### Common Issues

1. **Import Errors**
   - Use `main_minimal.py` for deployment
   - Check dependency versions

2. **Database Connection**
   - Verify DATABASE_URL format
   - Check network connectivity
   - Ensure database exists

3. **FFmpeg Not Found**
   - Install FFmpeg: `sudo apt install ffmpeg`
   - Verify PATH configuration

4. **CORS Errors**
   - Update allowed origins
   - Check frontend URL configuration

### Debug Mode

```bash
# Run in debug mode
uvicorn main_minimal:app --reload --log-level debug
```

## ✅ Production Checklist

- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] CORS origins updated for production
- [ ] SSL certificates installed
- [ ] Health checks passing
- [ ] Monitoring configured
- [ ] Backups scheduled
- [ ] Load testing completed
- [ ] Security review completed
- [ ] Documentation updated

## 📞 Support

For deployment issues:
1. Check the health endpoint: `/health`
2. Review application logs
3. Verify environment variables
4. Test individual API endpoints
5. Check database connectivity

---

**Deployment Guide Version**: 1.0  
**Last Updated**: August 30, 2025  
**Status**: Production Ready ✅
