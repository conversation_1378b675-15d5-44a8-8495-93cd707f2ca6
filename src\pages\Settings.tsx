
import React from "react";
import { Link, Navigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import DashboardLayout from "@/components/Dashboard";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { 
  Bell, 
  Mail, 
  Shield, 
  Globe, 
  Moon, 
  UserX,
  LogOut,
  User,
  CreditCard 
} from "lucide-react";

const Settings = () => {
  const { isAuthenticated, logout } = useAuth();
  const { toast } = useToast();

  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  const handleLogout = async () => {
    try {
      await logout();
      toast({
        title: "Logged out",
        description: "You have been successfully logged out",
      });
    } catch (error) {
      toast({
        title: "Logout failed",
        description: "There was an error logging you out",
        variant: "destructive",
      });
    }
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto py-8">
        <h1 className="text-3xl font-bold mb-2">Settings</h1>
        <p className="text-muted-foreground mb-8">
          Manage your account settings and preferences
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Bell className="mr-2 h-5 w-5" />
                  Notifications
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className=" flex flex-col items-start">
                    <p className="font-medium">Email Notifications</p>
                    <p className="text-sm text-muted-foreground">Receive email updates about your account</p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className=" flex flex-col items-start">
                    <p className="font-medium">Video Processing Notifications</p>
                    <p className="text-sm text-muted-foreground">Get notified when your videos finish processing</p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className=" flex flex-col items-start">
                    <p className="font-medium">Marketing Emails</p>
                    <p className="text-sm text-muted-foreground">Receive emails about new features and offers</p>
                  </div>
                  <Switch />
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="md:col-span-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="mr-2 h-5 w-5" />
                  Privacy & Security
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className=" flex flex-col items-start">
                    <p className="font-medium">Two-Factor Authentication</p>
                    <p className="text-sm text-muted-foreground">Add an extra layer of security to your account</p>
                  </div>
                  <Switch />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className=" flex flex-col items-start">
                    <p className="font-medium">Data Collection</p>
                    <p className="text-sm text-muted-foreground">Allow us to collect usage data to improve our service</p>
                  </div>
                  <Switch defaultChecked />
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="md:col-span-3">
            <Card className="border-destructive/20">
              <CardHeader>
                <CardTitle className="flex items-center text-destructive">
                  <UserX className="mr-2 h-5 w-5" />
                  Account Actions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className=" flex flex-col items-start">
                    <p className="font-medium">Log out from all devices</p>
                    <p className="text-sm text-muted-foreground">This will log you out from all your active sessions</p>
                  </div>
                  <Button variant="outline" size="sm">Log Out All</Button>
                </div>
                
                <div className="flex justify-between items-center">
                  <div className=" flex flex-col items-start">
                    <p className="font-medium">Delete Account</p>
                    <p className="text-sm text-muted-foreground">Permanently delete your account and all data</p>
                  </div>
                  <Button variant="destructive" size="sm">Delete Account</Button>
                </div>
                
                <div className="flex justify-between items-center">
                  <div className=" flex flex-col items-start">
                    <p className="font-medium">Log out</p>
                    <p className="text-sm text-muted-foreground">Log out from this current session</p>
                  </div>
                  <Button variant="outline" size="sm" onClick={handleLogout}>
                    <LogOut className="mr-2 h-4 w-4" />
                    Log Out
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

           <div className="md:col-span-3">
            <Card className="border-destructive/20">
              <CardHeader>
                <CardTitle className="flex items-center ">
                  <CreditCard className="mr-2 h-6 w-6" />
                  Billing
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
               
                
                
                
                <div className="flex justify-between items-center">
  <div>
    <p className="text-sm text-muted-foreground">check your billing information</p>
  </div>

  <Button asChild variant="outline" size="sm" aria-label="View credits">
    <Link to="/credits">
      <CreditCard className="mr-1 h-4 w-4" />
      Credits
    </Link>
  </Button>
</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Settings;
