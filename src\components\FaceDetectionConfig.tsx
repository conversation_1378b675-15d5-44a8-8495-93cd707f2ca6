import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { AlertCircle, Eye, Zap, Volume2, Settings } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

export interface FaceDetectionConfig {
  zoom_level: number;
  transition_speed: number;
  detection_sensitivity: number;
  voice_threshold: number;
  padding: number;
}

interface FaceDetectionConfigProps {
  config: FaceDetectionConfig;
  onConfigChange: (config: FaceDetectionConfig) => void;
  onAnalyze: () => void;
  onProcess: () => void;
  onPreview: () => void;
  isProcessing: boolean;
  analysisResult?: any;
  previewData?: any;
  processingStatus?: {
    status: string;
    progress: number;
    message?: string;
  };
}

const FaceDetectionConfigComponent: React.FC<FaceDetectionConfigProps> = ({
  config,
  onConfigChange,
  onAnalyze,
  onProcess,
  onPreview,
  isProcessing,
  analysisResult,
  previewData,
  processingStatus
}) => {
  const [activeTab, setActiveTab] = useState('config');

  const updateConfig = (key: keyof FaceDetectionConfig, value: number) => {
    onConfigChange({
      ...config,
      [key]: value
    });
  };

  const getZoomLevelDescription = (level: number) => {
    if (level <= 1.2) return 'Subtle zoom';
    if (level <= 1.5) return 'Moderate zoom';
    if (level <= 2.0) return 'Strong zoom';
    return 'Extreme zoom';
  };

  const getTransitionSpeedDescription = (speed: number) => {
    if (speed <= 0.3) return 'Very fast';
    if (speed <= 0.5) return 'Fast';
    if (speed <= 0.8) return 'Moderate';
    return 'Slow';
  };

  const getSensitivityDescription = (sensitivity: number) => {
    if (sensitivity <= 0.5) return 'Low sensitivity';
    if (sensitivity <= 0.7) return 'Medium sensitivity';
    if (sensitivity <= 0.8) return 'High sensitivity';
    return 'Very high sensitivity';
  };

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Facial AI Enhancement for Podcasts
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Automatically detect faces and apply dynamic zoom effects when speakers talk
          </p>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
           <div className="-mx-4 px-4 overflow-x-auto">
            <TabsList className="inline-flex gap-2 min-w-max">
               <TabsTrigger value="config"   className="text-sm whitespace-nowrap">Configuration</TabsTrigger>
               <TabsTrigger value="analysis" className="text-sm whitespace-nowrap">Analysis</TabsTrigger>
               <TabsTrigger value="preview"  className="text-sm whitespace-nowrap">Preview</TabsTrigger>
               <TabsTrigger value="process"  className="text-sm whitespace-nowrap">Process</TabsTrigger>
           </TabsList>
         </div>


            <TabsContent value="config" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Zoom Level */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="flex items-center gap-2">
                      <Zap className="h-4 w-4" />
                      Zoom Level
                    </Label>
                    <Badge variant="outline">
                      {config.zoom_level.toFixed(1)}x
                    </Badge>
                  </div>
                  <Slider
                    value={[config.zoom_level]}
                    onValueChange={([value]) => updateConfig('zoom_level', value)}
                    min={1.0}
                    max={3.0}
                    step={0.1}
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    {getZoomLevelDescription(config.zoom_level)}
                  </p>
                </div>

                {/* Transition Speed */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="flex items-center gap-2">
                      <Settings className="h-4 w-4" />
                      Transition Speed
                    </Label>
                    <Badge variant="outline">
                      {config.transition_speed.toFixed(1)}s
                    </Badge>
                  </div>
                  <Slider
                    value={[config.transition_speed]}
                    onValueChange={([value]) => updateConfig('transition_speed', value)}
                    min={0.1}
                    max={2.0}
                    step={0.1}
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    {getTransitionSpeedDescription(config.transition_speed)}
                  </p>
                </div>

                {/* Detection Sensitivity */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="flex items-center gap-2">
                      <Eye className="h-4 w-4" />
                      Face Detection Sensitivity
                    </Label>
                    <Badge variant="outline">
                      {Math.round(config.detection_sensitivity * 100)}%
                    </Badge>
                  </div>
                  <Slider
                    value={[config.detection_sensitivity]}
                    onValueChange={([value]) => updateConfig('detection_sensitivity', value)}
                    min={0.3}
                    max={0.95}
                    step={0.05}
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    {getSensitivityDescription(config.detection_sensitivity)}
                  </p>
                </div>

                {/* Voice Threshold */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="flex items-center gap-2">
                      <Volume2 className="h-4 w-4" />
                      Voice Activity Threshold
                    </Label>
                    <Badge variant="outline">
                      {Math.round(config.voice_threshold * 100)}%
                    </Badge>
                  </div>
                  <Slider
                    value={[config.voice_threshold]}
                    onValueChange={([value]) => updateConfig('voice_threshold', value)}
                    min={0.1}
                    max={0.8}
                    step={0.05}
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    Lower values detect quieter speech
                  </p>
                </div>
              </div>

              {/* Padding Control */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label>Face Padding (pixels)</Label>
                  <Badge variant="outline">{config.padding}px</Badge>
                </div>
                <Slider
                  value={[config.padding]}
                  onValueChange={([value]) => updateConfig('padding', value)}
                  min={20}
                  max={150}
                  step={10}
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground">
                  Extra space around detected faces
                </p>
              </div>

              <div className="flex flex-col gap-3 md:flex-row md:gap-3">
                <Button onClick={onAnalyze} disabled={isProcessing}>
                  Analyze Video
                </Button>
                <Button onClick={onPreview} variant="outline" disabled={isProcessing}>
                  Generate Preview
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="analysis" className="space-y-4">
              {analysisResult ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold">
                          {analysisResult.face_detections}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Face Detections
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold">
                          {analysisResult.voice_segments}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Voice Segments
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold">
                          {analysisResult.speaker_segments?.length || 0}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Speaker Segments
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold">
                          {Math.round(analysisResult.duration || 0)}s
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Duration
                        </p>
                      </CardContent>
                    </Card>
                  </div>

                  {analysisResult.speaker_segments && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Speaker Timeline</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          {analysisResult.speaker_segments.map((segment: any, index: number) => (
                            <div key={index} className="flex items-center gap-3 p-2 bg-muted rounded">
                              <Badge>Speaker {segment.speaker_id + 1}</Badge>
                              <span className="text-sm">
                                {segment.start_time.toFixed(1)}s - {segment.end_time.toFixed(1)}s
                              </span>
                              <Badge variant="outline">
                                {Math.round(segment.confidence * 100)}% confidence
                              </Badge>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              ) : (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Run analysis first to see detailed face detection and voice activity results.
                  </AlertDescription>
                </Alert>
              )}
            </TabsContent>

            <TabsContent value="preview" className="space-y-4">
              {previewData ? (
                <div className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Preview Frames</CardTitle>
                      <p className="text-sm text-muted-foreground">
                        Showing {previewData.total_frames} sample frames with face tracking
                      </p>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {previewData.preview_frames?.map((frame: any, index: number) => (
                          <div key={index} className="border rounded p-3 space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">
                                Frame {frame.frame_number}
                              </span>
                              <Badge variant={frame.has_speech ? "default" : "secondary"}>
                                {frame.has_speech ? "Speaking" : "Silent"}
                              </Badge>
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {frame.timestamp.toFixed(1)}s
                            </div>
                            {frame.faces.length > 0 && (
                              <div className="text-xs">
                                {frame.faces.length} face(s) detected
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Generate a preview to see face tracking overlay information.
                  </AlertDescription>
                </Alert>
              )}
            </TabsContent>

            <TabsContent value="process" className="space-y-4">
              {processingStatus ? (
                <div className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Processing Status</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Status:</span>
                        <Badge variant={
                          processingStatus.status === 'completed' ? 'default' :
                          processingStatus.status === 'failed' ? 'destructive' :
                          'secondary'
                        }>
                          {processingStatus.status}
                        </Badge>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Progress</span>
                          <span>{Math.round(processingStatus.progress * 100)}%</span>
                        </div>
                        <Progress value={processingStatus.progress * 100} />
                      </div>
                      
                      {processingStatus.message && (
                        <p className="text-sm text-muted-foreground">
                          {processingStatus.message}
                        </p>
                      )}
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <div className="space-y-4">
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Ready to process your video with facial AI enhancement. This will apply dynamic zoom effects based on face detection and voice activity.
                    </AlertDescription>
                  </Alert>
                  
                  <Button onClick={onProcess} disabled={isProcessing} className="w-full">
                    {isProcessing ? 'Processing...' : 'Start Processing'}
                  </Button>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default FaceDetectionConfigComponent;
