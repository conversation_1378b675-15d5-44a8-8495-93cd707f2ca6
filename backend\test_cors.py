#!/usr/bin/env python3
"""
Test CORS configuration and frontend-backend integration
"""

import requests
import json

def test_cors():
    """Test CORS configuration"""
    print("🔍 Testing CORS Configuration...")
    
    try:
        # Test OPTIONS request (preflight)
        response = requests.options(
            'http://localhost:8000/',
            headers={
                'Origin': 'http://localhost:8080',
                'Access-Control-Request-Method': 'POST',
                'Access-Control-Request-Headers': 'Content-Type'
            }
        )
        
        cors_origin = response.headers.get('Access-Control-Allow-Origin')
        cors_methods = response.headers.get('Access-Control-Allow-Methods')
        cors_headers = response.headers.get('Access-Control-Allow-Headers')
        
        print(f"  Status Code: {response.status_code}")
        print(f"  CORS Origin: {cors_origin}")
        print(f"  CORS Methods: {cors_methods}")
        print(f"  CORS Headers: {cors_headers}")
        
        if cors_origin and cors_methods:
            print("  ✅ CORS properly configured")
            return True
        else:
            print("  ❌ CORS headers missing")
            return False
            
    except Exception as e:
        print(f"  ❌ CORS test failed: {str(e)}")
        return False

def test_api_from_frontend():
    """Test API calls as if from frontend"""
    print("\n🌐 Testing API calls from frontend perspective...")
    
    try:
        # Test basic API call with Origin header
        response = requests.get(
            'http://localhost:8000/health',
            headers={'Origin': 'http://localhost:8080'}
        )
        
        print(f"  Health endpoint status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  Response: {json.dumps(data, indent=2)}")
            print("  ✅ Frontend can communicate with backend")
            return True
        else:
            print(f"  ❌ API call failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ Frontend API test failed: {str(e)}")
        return False

def test_environment_variables():
    """Test if environment variables are properly configured"""
    print("\n🔧 Testing Environment Configuration...")
    
    try:
        response = requests.get('http://localhost:8000/health')
        if response.status_code == 200:
            data = response.json()
            services = data.get('services', {})
            
            print("  Service Status:")
            for service, status in services.items():
                status_icon = "✅" if status else "❌"
                print(f"    {status_icon} {service}: {status}")
            
            all_services_ok = all(services.values())
            if all_services_ok:
                print("  ✅ All services properly configured")
            else:
                print("  ⚠️  Some services not configured")
            
            return all_services_ok
        else:
            print(f"  ❌ Health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ Environment test failed: {str(e)}")
        return False

def main():
    """Run all integration tests"""
    print("🚀 Starting Frontend-Backend Integration Tests...")
    print("="*50)
    
    results = []
    
    # Test CORS
    results.append(test_cors())
    
    # Test API from frontend perspective
    results.append(test_api_from_frontend())
    
    # Test environment configuration
    results.append(test_environment_variables())
    
    # Summary
    print("\n" + "="*50)
    print("📊 INTEGRATION TEST SUMMARY")
    print("="*50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("✅ All integration tests passed!")
        print("✅ Frontend-Backend integration is working correctly")
    else:
        print("❌ Some integration tests failed")
        print("⚠️  Please check the configuration")

if __name__ == "__main__":
    main()
