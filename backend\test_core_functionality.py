"""
Core Functionality Tests for GPT Editor

This module tests the core GPT Editor functionality without heavy dependencies.
"""

import unittest
import asyncio
import os
import tempfile

# Import core modules
from gpt_editor_service import GPTEditorService
from ffmpeg_command_generator import FFmpegCommandGenerator

class TestGPTEditorCore(unittest.TestCase):
    """Test core GPT Editor functionality"""
    
    def setUp(self):
        self.service = GPTEditorService()
        self.generator = FFmpegCommandGenerator()
    
    def test_service_initialization(self):
        """Test GPT Editor Service initialization"""
        print("Testing GPT Editor Service initialization...")
        
        self.assertIsNotNone(self.service)
        self.assertIsInstance(self.service.available_actions, dict)
        self.assertIsInstance(self.service.video_templates, dict)
        
        # Check that we have expected actions
        expected_actions = ['trim', 'crop', 'add_text', 'add_subtitles', 'apply_template']
        for action in expected_actions:
            self.assertIn(action, self.service.available_actions)
        
        # Check that we have expected templates
        expected_templates = ['podcast', 'gaming', 'tiktok', 'instagram', 'youtube']
        for template in expected_templates:
            self.assertIn(template, self.service.video_templates)
        
        print("✅ Service initialization test passed")
    
    def test_fallback_command_parsing(self):
        """Test fallback command parsing (without OpenAI API)"""
        print("Testing fallback command parsing...")
        
        test_cases = [
            ("trim video to 30 seconds", "trim"),
            ("add subtitles to this video", "add_subtitles"),
            ("make this TikTok ready", "apply_template"),
            ("convert to vertical format", "apply_template")
        ]
        
        for command, expected_action in test_cases:
            commands = self.service._fallback_parse_command(command)
            self.assertGreater(len(commands), 0, f"No commands parsed for: {command}")
            
            # Check if expected action is present
            actions = [cmd['action'] for cmd in commands]
            self.assertIn(expected_action, actions, f"Expected action {expected_action} not found for: {command}")
            
            # Check command structure
            for cmd in commands:
                self.assertIn('action', cmd)
                self.assertIn('parameters', cmd)
                self.assertIn('confidence', cmd)
                self.assertIn('description', cmd)
                self.assertIsInstance(cmd['confidence'], float)
                self.assertGreaterEqual(cmd['confidence'], 0.0)
                self.assertLessEqual(cmd['confidence'], 1.0)
        
        print("✅ Fallback command parsing test passed")
    
    def test_template_system(self):
        """Test template system functionality"""
        print("Testing template system...")
        
        # Test template retrieval
        tiktok_template = self.service.get_template_by_name('tiktok')
        self.assertIsNotNone(tiktok_template)
        self.assertIn('name', tiktok_template)
        self.assertIn('description', tiktok_template)
        self.assertIn('effects', tiktok_template)
        
        # Test non-existent template
        fake_template = self.service.get_template_by_name('nonexistent')
        self.assertIsNone(fake_template)
        
        # Test template listing
        all_templates = self.service.list_available_templates()
        self.assertIsInstance(all_templates, dict)
        self.assertGreater(len(all_templates), 0)
        
        print("✅ Template system test passed")
    
    def test_processing_time_estimation(self):
        """Test processing time estimation"""
        print("Testing processing time estimation...")
        
        test_commands = [
            {"action": "trim", "parameters": {}, "confidence": 0.9, "description": "test"},
            {"action": "add_subtitles", "parameters": {}, "confidence": 0.8, "description": "test"},
            {"action": "crop", "parameters": {}, "confidence": 0.7, "description": "test"}
        ]
        
        # Test with different video durations
        for duration in [30, 60, 120, 300]:
            estimated_time = self.service.estimate_processing_time(test_commands, duration)
            self.assertIsInstance(estimated_time, int)
            self.assertGreater(estimated_time, 0)
            self.assertGreaterEqual(estimated_time, 15)  # Minimum time
        
        print("✅ Processing time estimation test passed")

class TestFFmpegGenerator(unittest.TestCase):
    """Test FFmpeg command generation"""
    
    def setUp(self):
        self.generator = FFmpegCommandGenerator()
    
    def test_generator_initialization(self):
        """Test FFmpeg generator initialization"""
        print("Testing FFmpeg generator initialization...")
        
        self.assertIsNotNone(self.generator)
        self.assertIsNotNone(self.generator.ffmpeg_binary)
        
        print("✅ FFmpeg generator initialization test passed")
    
    def test_trim_command_generation(self):
        """Test trim command generation"""
        print("Testing trim command generation...")
        
        cmd = self.generator._generate_single_command(
            "input.mp4", "output.mp4", "trim",
            {"start_time": 10, "end_time": 60}
        )
        
        self.assertIsNotNone(cmd)
        self.assertIsInstance(cmd, list)
        self.assertIn("ffmpeg", cmd[0])
        self.assertIn("-ss", cmd)
        self.assertIn("-to", cmd)
        self.assertIn("10", cmd)
        self.assertIn("60", cmd)
        
        print("✅ Trim command generation test passed")
    
    def test_crop_command_generation(self):
        """Test crop command generation"""
        print("Testing crop command generation...")
        
        test_cases = [
            {"aspect_ratio": "16:9"},
            {"aspect_ratio": "9:16"},
            {"aspect_ratio": "1:1"},
            {"width": 640, "height": 480}
        ]
        
        for params in test_cases:
            cmd = self.generator._generate_single_command(
                "input.mp4", "output.mp4", "crop", params
            )
            
            self.assertIsNotNone(cmd, f"Failed to generate crop command for {params}")
            self.assertIn("ffmpeg", cmd[0])
            self.assertIn("-vf", cmd)
        
        print("✅ Crop command generation test passed")
    
    def test_text_overlay_command_generation(self):
        """Test text overlay command generation"""
        print("Testing text overlay command generation...")
        
        cmd = self.generator._generate_single_command(
            "input.mp4", "output.mp4", "add_text",
            {"text": "Test Text", "position": "center"}
        )
        
        self.assertIsNotNone(cmd)
        self.assertIn("ffmpeg", cmd[0])
        self.assertIn("-vf", cmd)
        
        # Find the video filter argument
        vf_index = cmd.index("-vf")
        filter_arg = cmd[vf_index + 1]
        self.assertIn("drawtext", filter_arg)
        self.assertIn("Test Text", filter_arg)
        
        print("✅ Text overlay command generation test passed")
    
    def test_command_sequence_generation(self):
        """Test command sequence generation"""
        print("Testing command sequence generation...")
        
        commands = [
            {"action": "trim", "parameters": {"start_time": 0, "end_time": 30}, "confidence": 0.9, "description": "test"},
            {"action": "crop", "parameters": {"aspect_ratio": "16:9"}, "confidence": 0.8, "description": "test"},
            {"action": "add_text", "parameters": {"text": "Test", "position": "center"}, "confidence": 0.7, "description": "test"}
        ]
        
        sequence = self.generator.generate_command_sequence("input.mp4", "output.mp4", commands)
        
        self.assertEqual(len(sequence), len(commands))
        self.assertIsInstance(sequence, list)
        
        for cmd in sequence:
            self.assertIsInstance(cmd, list)
            self.assertIn("ffmpeg", cmd[0])
        
        print("✅ Command sequence generation test passed")
    
    def test_unknown_action_handling(self):
        """Test handling of unknown actions"""
        print("Testing unknown action handling...")
        
        cmd = self.generator._generate_single_command(
            "input.mp4", "output.mp4", "unknown_action", {}
        )
        
        self.assertIsNone(cmd)
        
        print("✅ Unknown action handling test passed")

class TestAsyncFunctionality(unittest.IsolatedAsyncioTestCase):
    """Test async functionality"""
    
    async def test_async_command_parsing(self):
        """Test async command parsing"""
        print("Testing async command parsing...")
        
        service = GPTEditorService()
        
        test_commands = [
            "trim this video to 30 seconds",
            "add subtitles",
            "make this TikTok ready",
            "apply podcast template"
        ]
        
        for command in test_commands:
            commands = await service.parse_editing_command(command)
            self.assertIsInstance(commands, list)
            self.assertGreater(len(commands), 0)
            
            for cmd in commands:
                self.assertIn('action', cmd)
                self.assertIn('confidence', cmd)
        
        print("✅ Async command parsing test passed")

def run_comprehensive_tests():
    """Run comprehensive tests"""
    print("🧪 GPT Editor Core Functionality Test Suite")
    print("=" * 60)
    
    # Test results tracking
    total_tests = 0
    passed_tests = 0
    failed_tests = 0
    
    # Run synchronous tests
    print("\n📋 Running Synchronous Tests...")
    print("-" * 40)
    
    sync_test_classes = [TestGPTEditorCore, TestFFmpegGenerator]
    
    for test_class in sync_test_classes:
        print(f"\n🔍 Testing {test_class.__name__}:")
        
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        runner = unittest.TextTestRunner(verbosity=0, stream=open(os.devnull, 'w'))
        result = runner.run(suite)
        
        class_total = result.testsRun
        class_failed = len(result.failures) + len(result.errors)
        class_passed = class_total - class_failed
        
        total_tests += class_total
        passed_tests += class_passed
        failed_tests += class_failed
        
        if class_failed == 0:
            print(f"✅ {test_class.__name__}: {class_passed}/{class_total} tests passed")
        else:
            print(f"❌ {test_class.__name__}: {class_passed}/{class_total} tests passed, {class_failed} failed")
            
            # Print failure details
            for failure in result.failures + result.errors:
                print(f"   ❌ {failure[0]}: {failure[1].split('AssertionError:')[-1].strip()}")
    
    # Run async tests
    print(f"\n🔄 Running Async Tests...")
    print("-" * 40)
    
    async def run_async_tests():
        test_instance = TestAsyncFunctionality()
        try:
            await test_instance.test_async_command_parsing()
            return 1, 0  # 1 passed, 0 failed
        except Exception as e:
            print(f"❌ Async test failed: {e}")
            return 0, 1  # 0 passed, 1 failed
    
    async_passed, async_failed = asyncio.run(run_async_tests())
    total_tests += async_passed + async_failed
    passed_tests += async_passed
    failed_tests += async_failed
    
    # Final summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"Total Tests Run: {total_tests}")
    print(f"✅ Passed: {passed_tests}")
    print(f"❌ Failed: {failed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if failed_tests == 0:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ GPT Editor core functionality is working correctly")
        print("🚀 System is ready for production use")
    else:
        print(f"\n⚠️  {failed_tests} test(s) failed")
        print("🔧 Please review and fix the failing components")
    
    print("=" * 60)
    
    return failed_tests == 0

if __name__ == "__main__":
    success = run_comprehensive_tests()
