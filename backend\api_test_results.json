[{"endpoint": "/", "method": "GET", "status_code": 200, "success": true, "details": "{\"message\":\"SmartClips Backend API is running!\",\"status\":\"ok\",\"version\":\"1.0.0\"}", "timestamp": "2025-09-01 17:43:40"}, {"endpoint": "/health", "method": "GET", "status_code": 200, "success": true, "details": "Response: {\"status\":\"healthy\",\"ffmpeg_available\":true,\"database\":\"connected\",\"services\":{\"openai\":true,\"cloudi...", "timestamp": "2025-09-01 17:43:40"}, {"endpoint": "/users/", "method": "POST", "status_code": 200, "success": true, "details": "Response: {\"username\":\"testuser\",\"email\":\"<EMAIL>\",\"avatar_url\":null,\"bio\":null,\"subscription\":\"free\"...", "timestamp": "2025-09-01 17:43:41"}, {"endpoint": "/token", "method": "POST", "status_code": 200, "success": true, "details": "Authentication successful", "timestamp": "2025-09-01 17:43:41"}, {"endpoint": "/users/me/", "method": "GET", "status_code": 200, "success": true, "details": "Response: {\"username\":\"testuser\",\"email\":\"<EMAIL>\",\"avatar_url\":null,\"bio\":null,\"subscription\":\"free\"...", "timestamp": "2025-09-01 17:43:41"}, {"endpoint": "/validate-url", "method": "POST", "status_code": 200, "success": true, "details": "{\"valid\":true,\"platform\":\"youtube\",\"video_id\":\"dQw4w9WgXcQ\",\"error\":null}", "timestamp": "2025-09-01 17:43:42"}, {"endpoint": "/url-metadata", "method": "POST", "status_code": 200, "success": true, "details": "Response: {\"title\":\"<PERSON> - Never Gonna Give You Up (Official Video) (4K Remaster)\",\"duration\":213,\"uplo...", "timestamp": "2025-09-01 17:43:45"}, {"endpoint": "/process-instant", "method": "POST", "status_code": 500, "success": false, "details": "Response: {\"detail\":\"Download failed: WARNING: [youtube] YouTube said: ERROR - Precondition check failed.\\nWAR...", "timestamp": "2025-09-01 17:43:48"}, {"endpoint": "/user/clips", "method": "GET", "status_code": 200, "success": true, "details": "[]", "timestamp": "2025-09-01 17:43:48"}, {"endpoint": "/user/stats", "method": "GET", "status_code": 500, "success": false, "details": "Internal Server Error", "timestamp": "2025-09-01 17:43:49"}, {"endpoint": "/user/video-count", "method": "GET", "status_code": 200, "success": true, "details": "{\"videos\":0,\"clips\":0}", "timestamp": "2025-09-01 17:43:51"}, {"endpoint": "/user/social-platforms", "method": "GET", "status_code": 200, "success": true, "details": "Response: [{\"name\":\"YouTube\",\"icon\":\"🎥\",\"connected\":false,\"followers\":\"0\",\"profileUrl\":\"#\",\"color\":\"text-red-5...", "timestamp": "2025-09-01 17:43:51"}, {"endpoint": "/editor/projects", "method": "GET", "status_code": 200, "success": true, "details": "Response: [{\"id\":\"project_1\",\"name\":\"My First Project\",\"description\":\"A sample video project\",\"created_at\":\"20...", "timestamp": "2025-09-01 17:43:51"}, {"endpoint": "/editor/effects/templates", "method": "GET", "status_code": 200, "success": true, "details": "Response: [{\"id\":\"fade_in\",\"name\":\"Fade In\",\"description\":\"Gradually fade in the video\",\"category\":\"transition...", "timestamp": "2025-09-01 17:43:51"}, {"endpoint": "/api/gpt-editor/templates", "method": "GET", "status_code": 200, "success": true, "details": "Response: {\"templates\":{\"podcast\":{\"name\":\"Professional Podcast\",\"description\":\"Clean, professional podcast vi...", "timestamp": "2025-09-01 17:43:51"}, {"endpoint": "/api/gpt-editor/stats", "method": "GET", "status_code": 200, "success": true, "details": "{\"total_jobs\":0,\"running_jobs\":0,\"status_breakdown\":{},\"active_locks\":0}", "timestamp": "2025-09-01 17:43:51"}, {"endpoint": "/api/gpt-editor/presets", "method": "GET", "status_code": 200, "success": true, "details": "{\"presets\":[]}", "timestamp": "2025-09-01 17:43:51"}, {"endpoint": "/api/gpt-editor/chat-history", "method": "GET", "status_code": 200, "success": true, "details": "{\"chat_history\":[]}", "timestamp": "2025-09-01 17:43:51"}, {"endpoint": "/api/face-detection/analyze", "method": "POST", "status_code": 200, "success": false, "details": "Response: {\"success\":false,\"message\":\"Face detection analysis failed\",\"job_id\":null,\"result_url\":null,\"analysi...", "timestamp": "2025-09-01 17:43:51"}, {"endpoint": "/share/youtube", "method": "POST", "status_code": 422, "success": false, "details": "Response: {\"detail\":[{\"type\":\"missing\",\"loc\":[\"body\",\"clipId\"],\"msg\":\"Field required\",\"input\":{\"video_url\":\"ht...", "timestamp": "2025-09-01 17:43:51"}, {"endpoint": "/share/instagram", "method": "POST", "status_code": 422, "success": false, "details": "Response: {\"detail\":[{\"type\":\"missing\",\"loc\":[\"body\",\"clipId\"],\"msg\":\"Field required\",\"input\":{\"video_url\":\"ht...", "timestamp": "2025-09-01 17:43:51"}, {"endpoint": "/share/tiktok", "method": "POST", "status_code": 422, "success": false, "details": "Response: {\"detail\":[{\"type\":\"missing\",\"loc\":[\"body\",\"clipId\"],\"msg\":\"Field required\",\"input\":{\"video_url\":\"ht...", "timestamp": "2025-09-01 17:43:51"}, {"endpoint": "/share/twitter", "method": "POST", "status_code": 422, "success": false, "details": "Response: {\"detail\":[{\"type\":\"missing\",\"loc\":[\"body\",\"clipId\"],\"msg\":\"Field required\",\"input\":{\"video_url\":\"ht...", "timestamp": "2025-09-01 17:43:51"}]