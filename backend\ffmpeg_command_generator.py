"""
FFmpeg Command Generator for AI Video Editor

This module generates FFmpeg commands based on structured editing instructions
from the AI command interpreter.
"""

import os
import logging
import subprocess
from typing import List, Dict, Any, Optional, Tuple
import tempfile
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FFmpegCommandGenerator:
    """Generates and executes FFmpeg commands for video editing operations"""
    
    def __init__(self, temp_dir: str = None):
        self.temp_dir = temp_dir or tempfile.gettempdir()
        self.ffmpeg_binary = os.environ.get('FFMPEG_BINARY', 'ffmpeg')
        
        # Verify FFmpeg is available
        try:
            subprocess.run([self.ffmpeg_binary, '-version'], 
                         capture_output=True, check=True)
            logger.info("FFmpeg is available")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("FFmpeg not found. Please install FFmpeg.")
            raise RuntimeError("FFmpeg not available")

    def generate_command_sequence(self, input_path: str, output_path: str, 
                                commands: List[Dict[str, Any]]) -> List[List[str]]:
        """
        Generate a sequence of FFmpeg commands based on editing instructions
        
        Args:
            input_path: Path to input video
            output_path: Path to output video
            commands: List of structured editing commands
            
        Returns:
            List of FFmpeg command arrays
        """
        command_sequence = []
        current_input = input_path
        
        for i, cmd in enumerate(commands):
            action = cmd["action"]
            parameters = cmd["parameters"]
            
            # Generate intermediate output path for chaining commands
            if i == len(commands) - 1:
                current_output = output_path
            else:
                current_output = os.path.join(
                    self.temp_dir, 
                    f"intermediate_{i}_{action}.mp4"
                )
            
            ffmpeg_cmd = self._generate_single_command(
                current_input, current_output, action, parameters
            )
            
            if ffmpeg_cmd:
                command_sequence.append(ffmpeg_cmd)
                current_input = current_output
        
        return command_sequence

    def _generate_single_command(self, input_path: str, output_path: str, 
                                action: str, parameters: Dict[str, Any]) -> Optional[List[str]]:
        """Generate a single FFmpeg command for a specific action"""
        
        base_cmd = [self.ffmpeg_binary, '-y', '-i', input_path]
        
        if action == "trim":
            start_time = parameters.get("start_time", 0)
            end_time = parameters.get("end_time")
            duration = parameters.get("duration")
            
            if start_time:
                base_cmd.extend(['-ss', str(start_time)])
            
            if end_time:
                base_cmd.extend(['-to', str(end_time)])
            elif duration:
                base_cmd.extend(['-t', str(duration)])
            
            base_cmd.extend(['-c', 'copy', output_path])
            
        elif action == "crop":
            aspect_ratio = parameters.get("aspect_ratio")
            width = parameters.get("width")
            height = parameters.get("height")
            
            if aspect_ratio:
                if aspect_ratio == "16:9":
                    # For 16:9, crop width to match height * 16/9
                    filter_str = "crop='min(iw,ih*16/9)':'min(ih,iw*9/16)'"
                elif aspect_ratio == "9:16":
                    # For 9:16 (vertical), crop to make it taller
                    filter_str = "crop='min(iw,ih*9/16)':'min(ih,iw*16/9)'"
                elif aspect_ratio == "1:1":
                    # For square, crop to minimum dimension
                    filter_str = "crop='min(iw,ih)':'min(iw,ih)'"
                else:
                    # Parse custom aspect ratio
                    try:
                        w_ratio, h_ratio = map(float, aspect_ratio.split(':'))
                        filter_str = f"crop='min(iw,ih*{w_ratio}/{h_ratio})':'min(ih,iw*{h_ratio}/{w_ratio})'"
                    except:
                        filter_str = "crop=iw:ih"  # No crop
            elif width and height:
                filter_str = f"crop={width}:{height}"
            else:
                return None
            
            base_cmd.extend(['-vf', filter_str, '-c:a', 'copy', output_path])
            
        elif action == "resize":
            width = parameters.get("width", -1)
            height = parameters.get("height", -1)
            
            if width > 0 or height > 0:
                filter_str = f"scale={width}:{height}"
                base_cmd.extend(['-vf', filter_str, '-c:a', 'copy', output_path])
            else:
                return None
                
        elif action == "add_text":
            text = parameters.get("text", "Sample Text")
            position = parameters.get("position", "center")
            font_size = parameters.get("font_size", 24)
            color = parameters.get("color", "white")
            
            # Position mapping
            position_map = {
                "top": "x=(w-text_w)/2:y=50",
                "bottom": "x=(w-text_w)/2:y=h-text_h-50",
                "center": "x=(w-text_w)/2:y=(h-text_h)/2",
                "top_left": "x=50:y=50",
                "top_right": "x=w-text_w-50:y=50",
                "bottom_left": "x=50:y=h-text_h-50",
                "bottom_right": "x=w-text_w-50:y=h-text_h-50"
            }
            
            pos_str = position_map.get(position, position_map["center"])
            filter_str = f"drawtext=text='{text}':fontsize={font_size}:fontcolor={color}:{pos_str}"
            
            base_cmd.extend(['-vf', filter_str, '-c:a', 'copy', output_path])
            
        elif action == "adjust_speed":
            speed = parameters.get("speed", 1.0)
            
            if speed != 1.0:
                video_speed = 1.0 / speed
                audio_speed = speed
                
                filter_str = f"[0:v]setpts={video_speed}*PTS[v];[0:a]atempo={audio_speed}[a]"
                base_cmd.extend(['-filter_complex', filter_str, '-map', '[v]', '-map', '[a]', output_path])
            else:
                base_cmd.extend(['-c', 'copy', output_path])
                
        elif action == "add_effects":
            effects = []
            
            brightness = parameters.get("brightness")
            if brightness and brightness != 1.0:
                effects.append(f"eq=brightness={brightness - 1.0}")
            
            contrast = parameters.get("contrast")
            if contrast and contrast != 1.0:
                effects.append(f"eq=contrast={contrast}")
            
            saturation = parameters.get("saturation")
            if saturation and saturation != 1.0:
                effects.append(f"eq=saturation={saturation}")
            
            blur = parameters.get("blur")
            if blur:
                effects.append(f"boxblur={blur}")
            
            if effects:
                filter_str = ",".join(effects)
                base_cmd.extend(['-vf', filter_str, '-c:a', 'copy', output_path])
            else:
                base_cmd.extend(['-c', 'copy', output_path])
                
        elif action == "color_correction":
            gamma = parameters.get("gamma", 1.0)
            brightness = parameters.get("brightness", 1.0)
            contrast = parameters.get("contrast", 1.0)
            saturation = parameters.get("saturation", 1.0)
            
            filter_str = f"eq=gamma={gamma}:brightness={brightness-1.0}:contrast={contrast}:saturation={saturation}"
            base_cmd.extend(['-vf', filter_str, '-c:a', 'copy', output_path])
            
        elif action == "add_watermark":
            watermark_text = parameters.get("text", "SmartClips.io")
            position = parameters.get("position", "bottom_right")
            opacity = parameters.get("opacity", 0.7)
            
            position_map = {
                "top_left": "x=10:y=10",
                "top_right": "x=w-text_w-10:y=10",
                "bottom_left": "x=10:y=h-text_h-10",
                "bottom_right": "x=w-text_w-10:y=h-text_h-10",
                "center": "x=(w-text_w)/2:y=(h-text_h)/2"
            }
            
            pos_str = position_map.get(position, position_map["bottom_right"])
            filter_str = f"drawtext=text='{watermark_text}':fontsize=16:fontcolor=white@{opacity}:{pos_str}"
            
            base_cmd.extend(['-vf', filter_str, '-c:a', 'copy', output_path])
            
        elif action == "extract_audio":
            base_cmd = [self.ffmpeg_binary, '-y', '-i', input_path]
            audio_output = output_path.replace('.mp4', '.mp3')
            base_cmd.extend(['-vn', '-acodec', 'mp3', audio_output])
            
        elif action == "enhance_audio":
            # Apply audio normalization and noise reduction
            filter_str = "loudnorm=I=-16:TP=-1.5:LRA=11"
            base_cmd.extend(['-af', filter_str, '-c:v', 'copy', output_path])
            
        elif action == "stabilize":
            # Two-pass video stabilization
            # This is a simplified version - full stabilization requires two passes
            filter_str = "deshake"
            base_cmd.extend(['-vf', filter_str, '-c:a', 'copy', output_path])
            
        else:
            logger.warning(f"Unknown action: {action}")
            return None
        
        return base_cmd

    async def execute_command_sequence(self, command_sequence: List[List[str]], 
                                     progress_callback=None) -> bool:
        """
        Execute a sequence of FFmpeg commands
        
        Args:
            command_sequence: List of FFmpeg command arrays
            progress_callback: Optional callback for progress updates
            
        Returns:
            True if all commands executed successfully
        """
        total_commands = len(command_sequence)
        
        for i, cmd in enumerate(command_sequence):
            try:
                logger.info(f"Executing command {i+1}/{total_commands}: {' '.join(cmd[:5])}...")
                
                # Execute FFmpeg command
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    check=True
                )
                
                if progress_callback:
                    progress = (i + 1) / total_commands
                    await progress_callback(progress, f"Completed step {i+1}/{total_commands}")
                
                logger.info(f"Command {i+1} completed successfully")
                
            except subprocess.CalledProcessError as e:
                logger.error(f"FFmpeg command failed: {e}")
                logger.error(f"Command: {' '.join(cmd)}")
                logger.error(f"Error output: {e.stderr}")
                return False
            except Exception as e:
                logger.error(f"Unexpected error executing command: {e}")
                return False
        
        return True

    def get_video_info(self, video_path: str) -> Dict[str, Any]:
        """Get video information using FFprobe"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', video_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            info = json.loads(result.stdout)
            
            video_stream = None
            audio_stream = None
            
            for stream in info.get('streams', []):
                if stream.get('codec_type') == 'video' and not video_stream:
                    video_stream = stream
                elif stream.get('codec_type') == 'audio' and not audio_stream:
                    audio_stream = stream
            
            video_info = {
                'duration': float(info.get('format', {}).get('duration', 0)),
                'size': int(info.get('format', {}).get('size', 0)),
                'has_audio': audio_stream is not None,
                'has_video': video_stream is not None
            }
            
            if video_stream:
                video_info.update({
                    'width': int(video_stream.get('width', 0)),
                    'height': int(video_stream.get('height', 0)),
                    'fps': eval(video_stream.get('r_frame_rate', '0/1')),
                    'codec': video_stream.get('codec_name'),
                    'aspect_ratio': f"{video_stream.get('width', 0)}:{video_stream.get('height', 0)}"
                })
            
            return video_info
            
        except Exception as e:
            logger.error(f"Error getting video info: {e}")
            return {}

    def cleanup_intermediate_files(self, commands: List[Dict[str, Any]]):
        """Clean up intermediate files created during processing"""
        for i, cmd in enumerate(commands[:-1]):  # Skip the last command
            action = cmd["action"]
            intermediate_file = os.path.join(
                self.temp_dir, 
                f"intermediate_{i}_{action}.mp4"
            )
            
            if os.path.exists(intermediate_file):
                try:
                    os.remove(intermediate_file)
                    logger.info(f"Cleaned up intermediate file: {intermediate_file}")
                except Exception as e:
                    logger.warning(f"Failed to clean up {intermediate_file}: {e}")
