#!/usr/bin/env python3
"""
Test script for the enhanced AI clipper functionality
"""

import sys
import os
sys.path.append('backend')

from backend.video_processing import segment_transcript, _is_punchy_content
from backend.main import group_words_into_segments, _is_engaging_content

def test_ai_clipper_length_logic():
    """Test that AI clipper uses max_duration as limit, not target"""
    print("🔍 Testing AI Clipper Length Logic...")
    
    # Mock transcript with varying content types
    mock_transcript = """
    Hey everyone! This is an amazing tutorial that will blow your mind! 
    First, let me show you this incredible trick. You simply need to follow these steps carefully.
    The secret is in the timing! Most people make this mistake and fail.
    Now, let's dive into the technical details of how this actually works.
    This requires a deeper understanding of the underlying principles.
    """
    
    # Mock timestamps for the transcript
    mock_timestamps = [
        {"word": "Hey", "start": 0.0, "end": 0.3},
        {"word": "everyone!", "start": 0.3, "end": 0.8},
        {"word": "This", "start": 1.0, "end": 1.2},
        {"word": "is", "start": 1.2, "end": 1.3},
        {"word": "an", "start": 1.3, "end": 1.4},
        {"word": "amazing", "start": 1.4, "end": 1.9},
        {"word": "tutorial", "start": 1.9, "end": 2.5},
        {"word": "that", "start": 2.5, "end": 2.7},
        {"word": "will", "start": 2.7, "end": 2.9},
        {"word": "blow", "start": 2.9, "end": 3.2},
        {"word": "your", "start": 3.2, "end": 3.4},
        {"word": "mind!", "start": 3.4, "end": 4.0},
        {"word": "First,", "start": 5.0, "end": 5.4},
        {"word": "let", "start": 5.4, "end": 5.6},
        {"word": "me", "start": 5.6, "end": 5.7},
        {"word": "show", "start": 5.7, "end": 6.0},
        {"word": "you", "start": 6.0, "end": 6.2},
        {"word": "this", "start": 6.2, "end": 6.5},
        {"word": "incredible", "start": 6.5, "end": 7.2},
        {"word": "trick.", "start": 7.2, "end": 7.8},
        {"word": "You", "start": 8.5, "end": 8.7},
        {"word": "simply", "start": 8.7, "end": 9.2},
        {"word": "need", "start": 9.2, "end": 9.5},
        {"word": "to", "start": 9.5, "end": 9.6},
        {"word": "follow", "start": 9.6, "end": 10.1},
        {"word": "these", "start": 10.1, "end": 10.4},
        {"word": "steps", "start": 10.4, "end": 10.9},
        {"word": "carefully.", "start": 10.9, "end": 11.8},
        {"word": "The", "start": 12.5, "end": 12.7},
        {"word": "secret", "start": 12.7, "end": 13.2},
        {"word": "is", "start": 13.2, "end": 13.4},
        {"word": "in", "start": 13.4, "end": 13.5},
        {"word": "the", "start": 13.5, "end": 13.7},
        {"word": "timing!", "start": 13.7, "end": 14.5},
        {"word": "Most", "start": 15.0, "end": 15.3},
        {"word": "people", "start": 15.3, "end": 15.8},
        {"word": "make", "start": 15.8, "end": 16.1},
        {"word": "this", "start": 16.1, "end": 16.4},
        {"word": "mistake", "start": 16.4, "end": 17.0},
        {"word": "and", "start": 17.0, "end": 17.2},
        {"word": "fail.", "start": 17.2, "end": 17.8},
    ]
    
    # Test different max_duration values
    test_cases = [
        (10.0, 30.0, "Standard range"),
        (5.0, 20.0, "Shorter clips preferred"),
        (15.0, 45.0, "Longer clips allowed"),
    ]
    
    all_passed = True
    
    for min_dur, max_dur, description in test_cases:
        print(f"\n  📊 Testing {description} (min: {min_dur}s, max: {max_dur}s)")
        
        try:
            # Test basic segmentation
            segments = segment_transcript(
                mock_transcript, 
                mock_timestamps, 
                min_duration=min_dur, 
                max_duration=max_dur, 
                refine_with_ai=False
            )
            
            if not segments:
                print(f"    ❌ No segments created")
                all_passed = False
                continue
            
            print(f"    ✅ Created {len(segments)} segments")
            
            # Validate that segments respect max_duration as limit
            valid_segments = 0
            for i, segment in enumerate(segments):
                duration = segment['end'] - segment['start']
                is_valid = min_dur <= duration <= max_dur
                
                if is_valid:
                    valid_segments += 1
                    print(f"      Segment {i+1}: {duration:.1f}s ✅")
                else:
                    print(f"      Segment {i+1}: {duration:.1f}s ❌ (outside range)")
                    all_passed = False
            
            print(f"    Valid segments: {valid_segments}/{len(segments)}")
            
        except Exception as e:
            print(f"    ❌ Error: {str(e)}")
            all_passed = False
    
    return all_passed

def test_engaging_content_detection():
    """Test the engaging content detection logic"""
    print("\n🔍 Testing Engaging Content Detection...")
    
    test_cases = [
        ("This is amazing! You won't believe what happens next!", True),
        ("Check out this incredible trick that will blow your mind!", True),
        ("Here's a simple tutorial on basic concepts.", False),
        ("What?! This is the best hack ever discovered!", True),
        ("The weather today is quite nice and pleasant.", False),
        ("Never make this mistake again! Here's the secret tip.", True),
        ("This is a regular conversation about normal topics.", False),
    ]
    
    passed = 0
    for text, expected in test_cases:
        result = _is_engaging_content(text)
        status = "✅" if result == expected else "❌"
        print(f"  {status} '{text[:40]}...' -> {result} (expected {expected})")
        if result == expected:
            passed += 1
    
    print(f"\nEngaging content detection: {passed}/{len(test_cases)} tests passed")
    return passed == len(test_cases)

def test_word_grouping_intelligence():
    """Test the intelligent word grouping functionality"""
    print("\n🔍 Testing Intelligent Word Grouping...")
    
    # Mock word segments
    mock_word_segments = [
        {"text": "Hey", "start": 0.0, "end": 0.3},
        {"text": "everyone!", "start": 0.3, "end": 0.8},
        {"text": "This", "start": 1.0, "end": 1.2},
        {"text": "is", "start": 1.2, "end": 1.3},
        {"text": "amazing!", "start": 1.3, "end": 1.8},
        {"text": "Check", "start": 2.5, "end": 2.8},
        {"text": "this", "start": 2.8, "end": 3.0},
        {"text": "out.", "start": 3.0, "end": 3.5},
        {"text": "It's", "start": 4.0, "end": 4.2},
        {"text": "incredible!", "start": 4.2, "end": 4.8},
    ]
    
    try:
        segments = group_words_into_segments(mock_word_segments, 2.0, 8.0)
        
        if segments and len(segments) > 0:
            print(f"  ✅ Successfully grouped words into {len(segments)} segments")
            
            for i, segment in enumerate(segments):
                duration = segment['end'] - segment['start']
                print(f"    Segment {i+1}: {duration:.1f}s - '{segment['text'][:30]}...'")
            
            return True
        else:
            print("  ❌ No segments created from word grouping")
            return False
            
    except Exception as e:
        print(f"  ❌ Word grouping error: {str(e)}")
        return False

def main():
    """Run all AI clipper tests"""
    print("🚀 Testing Enhanced AI Clipper Functionality\n")
    
    tests = [
        test_ai_clipper_length_logic,
        test_engaging_content_detection,
        test_word_grouping_intelligence,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 AI Clipper Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All AI clipper tests passed! Enhanced functionality is working correctly.")
        print("✨ The AI clipper now uses max_duration as a limit and creates optimal clips!")
    else:
        print("⚠️  Some AI clipper tests failed.")
    
    return passed == total

if __name__ == "__main__":
    main()
