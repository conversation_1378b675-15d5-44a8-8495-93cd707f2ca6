"""
Minimal SmartClips Backend for Pre-Deployment Verification
This version includes core functionality without heavy dependencies
"""

import os
import json
import time
import logging
import tempfile
import subprocess
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any

from fastapi import FastAPI, Depends, HTTPException, status, File, UploadFile, Form, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
from sqlalchemy.orm import Session
from passlib.context import CryptContext
from jose import JWTError, jwt
import requests
import cloudinary
import cloudinary.uploader

# Import local modules
import models
from database import SessionLocal, engine, Base
import storage
import url_processor

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create database tables
Base.metadata.create_all(bind=engine)

# Initialize FastAPI app
app = FastAPI(
    title="SmartClips API",
    description="AI-powered video clipping and editing platform",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "https://smartclips.vercel.app",
        "https://*.vercel.app"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configure Cloudinary
cloudinary.config(
    cloud_name=os.getenv("CLOUDINARY_CLOUD_NAME"),
    api_key=os.getenv("CLOUDINARY_API_KEY"),
    api_secret=os.getenv("CLOUDINARY_API_SECRET")
)

# Authentication setup
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-here")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Pydantic models
class UserCreate(BaseModel):
    username: str
    email: str
    password: str

class UserResponse(BaseModel):
    id: int
    username: str
    email: str
    created_at: datetime

class Token(BaseModel):
    access_token: str
    token_type: str

class URLValidationRequest(BaseModel):
    url: str

class URLMetadataRequest(BaseModel):
    url: str

class ProcessURLRequest(BaseModel):
    url: str
    clip_length: Optional[int] = 60

# Database dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Authentication functions
def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
    user = db.query(models.User).filter(models.User.username == username).first()
    if user is None:
        raise credentials_exception
    return user

# Utility functions
def check_ffmpeg():
    """Check if FFmpeg is available"""
    try:
        subprocess.run(["ffmpeg", "-version"], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def check_service_availability():
    """Check availability of external services"""
    services = {
        "openai": bool(os.getenv("OPENAI_API_KEY")),
        "cloudinary": bool(os.getenv("CLOUDINARY_CLOUD_NAME") and 
                          os.getenv("CLOUDINARY_API_KEY") and 
                          os.getenv("CLOUDINARY_API_SECRET")),
        "elevenlabs": bool(os.getenv("ELEVENLABS_API_KEY")),
        "ffmpeg": check_ffmpeg()
    }
    return services

# API Routes

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "SmartClips API is running",
        "version": "1.0.0",
        "status": "healthy"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    services = check_service_availability()
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": services,
        "ffmpeg_available": services["ffmpeg"]
    }

# Authentication endpoints
@app.post("/users/", response_model=UserResponse)
async def create_user(user: UserCreate, db: Session = Depends(get_db)):
    """Create a new user"""
    # Check if user already exists
    db_user = db.query(models.User).filter(
        (models.User.username == user.username) | (models.User.email == user.email)
    ).first()
    if db_user:
        raise HTTPException(status_code=400, detail="Username or email already registered")
    
    # Create new user
    hashed_password = get_password_hash(user.password)
    db_user = models.User(
        username=user.username,
        email=user.email,
        hashed_password=hashed_password
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    return UserResponse(
        id=db_user.id,
        username=db_user.username,
        email=db_user.email,
        created_at=db_user.created_at
    )

@app.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    """Login and get access token"""
    user = db.query(models.User).filter(models.User.username == form_data.username).first()
    if not user or not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@app.get("/users/me/", response_model=UserResponse)
async def read_users_me(current_user: models.User = Depends(get_current_user)):
    """Get current user info"""
    return UserResponse(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        created_at=current_user.created_at
    )

# Video processing endpoints
@app.post("/validate-url")
async def validate_url(request: URLValidationRequest):
    """Validate video URL"""
    try:
        validation_result = url_processor.validate_url(request.url)
        return validation_result
    except Exception as e:
        logger.error(f"URL validation error: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/url-metadata")
async def get_url_metadata(request: URLMetadataRequest):
    """Get video metadata from URL"""
    try:
        metadata = url_processor.get_video_metadata(request.url)
        return metadata
    except Exception as e:
        logger.error(f"Metadata extraction error: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/process-url")
async def process_url(
    request: ProcessURLRequest,
    background_tasks: BackgroundTasks,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Process video from URL (simplified version)"""
    try:
        # Basic URL validation
        validation_result = url_processor.validate_url(request.url)
        if not validation_result.get("is_valid"):
            raise HTTPException(status_code=400, detail="Invalid URL")
        
        # For now, return a mock response
        # In production, this would trigger actual video processing
        return {
            "message": "Video processing started",
            "url": request.url,
            "clip_length": request.clip_length,
            "status": "processing",
            "estimated_time": "2-5 minutes"
        }
    except Exception as e:
        logger.error(f"URL processing error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# User data endpoints
@app.get("/user/clips")
async def get_user_clips(current_user: models.User = Depends(get_current_user), db: Session = Depends(get_db)):
    """Get user's clips"""
    clips = db.query(models.Clip).filter(models.Clip.user_id == current_user.id).all()
    return {"clips": clips}

@app.get("/user/stats")
async def get_user_stats(current_user: models.User = Depends(get_current_user), db: Session = Depends(get_db)):
    """Get user statistics"""
    clip_count = db.query(models.Clip).filter(models.Clip.user_id == current_user.id).count()
    return {
        "total_clips": clip_count,
        "user_since": current_user.created_at,
        "last_activity": datetime.utcnow()
    }

@app.get("/user/video-count")
async def get_user_video_count(current_user: models.User = Depends(get_current_user), db: Session = Depends(get_db)):
    """Get user's video count"""
    count = db.query(models.Clip).filter(models.Clip.user_id == current_user.id).count()
    return {"count": count}

@app.get("/user/social-platforms")
async def get_user_social_platforms(current_user: models.User = Depends(get_current_user)):
    """Get user's connected social platforms"""
    return {"platforms": []}  # Mock response

# GPT Editor endpoints
@app.get("/api/gpt-editor/templates")
async def get_gpt_editor_templates():
    """Get GPT editor templates"""
    templates = [
        {"id": 1, "name": "Viral Hook", "description": "Create engaging opening hooks"},
        {"id": 2, "name": "Story Arc", "description": "Structure your content with narrative flow"},
        {"id": 3, "name": "Call to Action", "description": "End with compelling CTAs"}
    ]
    return {"templates": templates}

@app.get("/api/gpt-editor/presets")
async def get_gpt_editor_presets():
    """Get GPT editor presets"""
    presets = [
        {"id": 1, "name": "TikTok Style", "platform": "tiktok"},
        {"id": 2, "name": "YouTube Shorts", "platform": "youtube"},
        {"id": 3, "name": "Instagram Reels", "platform": "instagram"}
    ]
    return {"presets": presets}

# File upload endpoint
@app.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    current_user: models.User = Depends(get_current_user)
):
    """Upload video file"""
    try:
        # Basic file validation
        if not file.content_type.startswith('video/'):
            raise HTTPException(status_code=400, detail="File must be a video")
        
        # For now, return mock response
        # In production, this would handle actual file upload to Cloudinary
        return {
            "message": "File uploaded successfully",
            "filename": file.filename,
            "size": file.size if hasattr(file, 'size') else 0,
            "status": "uploaded"
        }
    except Exception as e:
        logger.error(f"File upload error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Mount static files
try:
    app.mount("/static", StaticFiles(directory="static"), name="static")
except Exception as e:
    logger.warning(f"Could not mount static files: {e}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
