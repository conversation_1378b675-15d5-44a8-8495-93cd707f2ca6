"""
Test actual video processing with the GPT Editor

This script demonstrates real video processing using the AI-powered editor
with a sample video file.
"""

import os
import asyncio
import logging
import json
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_ffmpeg_processing():
    """Test actual FFmpeg processing with sample video"""
    
    logger.info("🎬 Testing GPT Editor with Real Video Processing")
    logger.info("=" * 60)
    
    # Check if sample video exists
    sample_video = "sample_video.mp4"
    if not os.path.exists(sample_video):
        logger.error(f"Sample video not found: {sample_video}")
        logger.info("Please run: ffmpeg -f lavfi -i testsrc=duration=10:size=640x480:rate=30 -f lavfi -i sine=frequency=1000:duration=10 -c:v libx264 -c:a aac -t 10 sample_video.mp4")
        return
    
    logger.info(f"✅ Found sample video: {sample_video}")
    
    # Import our modules
    try:
        from ffmpeg_command_generator import FFmpegCommandGenerator
        from gpt_editor_service import GPTEditorService
        
        logger.info("✅ Successfully imported GPT Editor modules")
        
    except ImportError as e:
        logger.error(f"❌ Failed to import modules: {e}")
        return
    
    # Initialize services
    ffmpeg_gen = FFmpegCommandGenerator()
    gpt_service = GPTEditorService()
    
    # Test 1: Get video information
    logger.info("\n📊 Test 1: Video Analysis")
    logger.info("-" * 30)
    
    try:
        video_info = ffmpeg_gen.get_video_info(sample_video)
        logger.info(f"Video Duration: {video_info.get('duration', 0):.2f} seconds")
        logger.info(f"Resolution: {video_info.get('width', 0)}x{video_info.get('height', 0)}")
        logger.info(f"Has Audio: {video_info.get('has_audio', False)}")
        logger.info(f"FPS: {video_info.get('fps', 0):.2f}")
        logger.info(f"Codec: {video_info.get('codec', 'unknown')}")
        
    except Exception as e:
        logger.error(f"❌ Video analysis failed: {e}")
        return
    
    # Test 2: Parse natural language commands
    logger.info("\n🧠 Test 2: AI Command Parsing")
    logger.info("-" * 35)
    
    test_commands = [
        "Crop this video to vertical format for TikTok",
        "Add text overlay saying 'Demo Video' in the center",
        "Trim the video to 5 seconds"
    ]
    
    parsed_results = []
    for i, command in enumerate(test_commands, 1):
        logger.info(f"\nCommand {i}: '{command}'")
        
        try:
            parsed_commands = await gpt_service.parse_editing_command(command, video_info)
            parsed_results.extend(parsed_commands)
            
            for cmd in parsed_commands:
                logger.info(f"  → {cmd['action']}: {cmd['description']} (confidence: {cmd['confidence']:.2f})")
                
        except Exception as e:
            logger.error(f"  ❌ Parsing failed: {e}")
            # Use fallback parsing
            parsed_commands = gpt_service._fallback_parse_command(command)
            parsed_results.extend(parsed_commands)
            logger.info(f"  → Using fallback parsing: {len(parsed_commands)} commands")
    
    # Test 3: Generate FFmpeg commands
    logger.info("\n⚙️  Test 3: FFmpeg Command Generation")
    logger.info("-" * 40)
    
    # Use a subset of commands for actual processing
    processing_commands = [
        {"action": "trim", "parameters": {"start_time": 0, "end_time": 5}, "confidence": 0.9, "description": "Trim to 5 seconds"},
        {"action": "crop", "parameters": {"aspect_ratio": "9:16"}, "confidence": 0.8, "description": "Crop to vertical"},
        {"action": "add_text", "parameters": {"text": "GPT Editor Demo", "position": "center"}, "confidence": 0.7, "description": "Add demo text"}
    ]
    
    output_video = "processed_demo.mp4"
    
    try:
        command_sequence = ffmpeg_gen.generate_command_sequence(
            sample_video, output_video, processing_commands
        )
        
        logger.info(f"Generated {len(command_sequence)} FFmpeg commands:")
        for i, cmd in enumerate(command_sequence, 1):
            # Show first few parts of each command
            cmd_preview = ' '.join(cmd[:10]) + ('...' if len(cmd) > 10 else '')
            logger.info(f"  {i}. {cmd_preview}")
        
    except Exception as e:
        logger.error(f"❌ Command generation failed: {e}")
        return
    
    # Test 4: Execute FFmpeg commands
    logger.info("\n🎯 Test 4: Video Processing Execution")
    logger.info("-" * 40)
    
    try:
        logger.info("Starting video processing...")
        
        async def progress_callback(progress, message):
            progress_bar = "█" * int(progress * 20) + "░" * (20 - int(progress * 20))
            logger.info(f"[{progress_bar}] {progress*100:5.1f}% | {message}")
        
        success = await ffmpeg_gen.execute_command_sequence(
            command_sequence, progress_callback
        )
        
        if success:
            logger.info("✅ Video processing completed successfully!")
            
            # Check if output file was created
            if os.path.exists(output_video):
                output_info = ffmpeg_gen.get_video_info(output_video)
                logger.info(f"Output video created: {output_video}")
                logger.info(f"Output duration: {output_info.get('duration', 0):.2f} seconds")
                logger.info(f"Output resolution: {output_info.get('width', 0)}x{output_info.get('height', 0)}")
            else:
                logger.warning("⚠️  Output file not found")
        else:
            logger.error("❌ Video processing failed")
            
    except Exception as e:
        logger.error(f"❌ Processing execution failed: {e}")
    
    # Test 5: Cleanup and summary
    logger.info("\n🧹 Test 5: Cleanup")
    logger.info("-" * 20)
    
    try:
        ffmpeg_gen.cleanup_intermediate_files(processing_commands)
        logger.info("✅ Intermediate files cleaned up")
        
    except Exception as e:
        logger.warning(f"⚠️  Cleanup warning: {e}")
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📋 PROCESSING SUMMARY")
    logger.info("=" * 60)
    
    logger.info(f"Input Video: {sample_video}")
    logger.info(f"Output Video: {output_video}")
    logger.info(f"Commands Processed: {len(processing_commands)}")
    logger.info(f"FFmpeg Commands Generated: {len(command_sequence) if 'command_sequence' in locals() else 0}")
    
    if os.path.exists(output_video):
        file_size = os.path.getsize(output_video) / 1024  # KB
        logger.info(f"Output File Size: {file_size:.1f} KB")
        logger.info("✅ Processing completed successfully!")
    else:
        logger.info("❌ Processing failed - no output file")
    
    logger.info("\n🎉 GPT Editor Video Processing Test Complete!")

async def test_template_system():
    """Test template system functionality"""
    
    logger.info("\n🎨 Testing Template System")
    logger.info("-" * 30)
    
    try:
        # Import without heavy dependencies
        from gpt_editor_service import GPTEditorService
        
        gpt_service = GPTEditorService()
        templates = gpt_service.video_templates
        
        logger.info(f"Available templates: {len(templates)}")
        
        for name, config in templates.items():
            logger.info(f"\n📋 {config['name']}")
            logger.info(f"   Description: {config['description']}")
            logger.info(f"   Effects: {len(config['effects'])} configured")
            logger.info(f"   Platforms: {', '.join(config.get('target_platforms', []))}")
        
        # Test template recommendation logic
        sample_commands = [
            "Make this TikTok ready",
            "Apply podcast styling", 
            "Create gaming highlights",
            "Make it professional for business"
        ]
        
        logger.info(f"\n🎯 Template Matching Test:")
        for cmd in sample_commands:
            parsed = await gpt_service.parse_editing_command(cmd)
            template_actions = [p for p in parsed if p['action'] == 'apply_template']
            
            if template_actions:
                template_name = template_actions[0]['parameters'].get('template', 'none')
                logger.info(f"   '{cmd}' → {template_name}")
            else:
                logger.info(f"   '{cmd}' → no template detected")
        
    except Exception as e:
        logger.error(f"❌ Template system test failed: {e}")

async def main():
    """Main test function"""
    await test_ffmpeg_processing()
    await test_template_system()

if __name__ == "__main__":
    asyncio.run(main())
