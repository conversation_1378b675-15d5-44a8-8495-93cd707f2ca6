import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/context/AuthContext";

interface SocialAuthProps {
  mode: "signin" | "signup";
  redirecAfterAuth?: string;
}

const SocialAuth: React.FC<SocialAuthProps> = ({ mode }) => {
  const { signInWithGoogle, signInWithFacebook, signInWithApple } = useAuth();
  const isSignIn = mode === "signin";
  const buttonText = isSignIn ? "Sign in" : "Sign up";

  const handleGoogleAuth = async () => {
    try {
      await signInWithGoogle();
    } catch (error) {
      toast({
        title: `Google ${isSignIn ? "sign in" : "sign up"} failed`,
        description:
          error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      });
    }
  };

  const handleFacebookAuth = async () => {
    try {
      await signInWithFacebook();
    } catch (error) {
      toast({
        title: `Facebook ${isSignIn ? "sign in" : "sign up"} failed`,
        description:
          error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      });
    }
  };

  const handleAppleAuth = async () => {
    try {
      await signInWithApple();
    } catch (error) {
      toast({
        title: `Apple ${isSignIn ? "sign in" : "sign up"} failed`,
        description:
          error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex flex-col gap-3">
      <Button
        variant="outline"
        type="button"
        className="bg-background/80 flex items-center justify-center gap-2"
        onClick={handleGoogleAuth}
      >
        <svg viewBox="0 0 24 24" className="h-5 w-5" aria-hidden="true">
          <path
            d="M12.0003 4.75C13.7703 4.75 15.3553 5.36002 16.6053 6.54998L20.0303 3.125C17.9502 1.19 15.2353 0 12.0003 0C7.31028 0 3.25527 2.69 1.28027 6.60998L5.27028 9.70498C6.21525 6.86002 8.87028 4.75 12.0003 4.75Z"
            fill="#EA4335"
          />
          <path
            d="M23.49 12.275C23.49 11.49 23.415 10.73 23.3 10H12V14.51H18.47C18.18 15.99 17.34 17.25 16.08 18.1L19.945 21.1C22.2 19.01 23.49 15.92 23.49 12.275Z"
            fill="#4285F4"
          />
          <path
            d="M5.26498 14.2949C5.02498 13.5699 4.88501 12.7999 4.88501 11.9999C4.88501 11.1999 5.01998 10.4299 5.26498 9.7049L1.275 6.60986C0.46 8.22986 0 10.0599 0 11.9999C0 13.9399 0.46 15.7699 1.28 17.3899L5.26498 14.2949Z"
            fill="#FBBC05"
          />
          <path
            d="M12.0004 24.0001C15.2404 24.0001 17.9654 22.935 19.9454 21.095L16.0804 18.095C15.0054 18.82 13.6204 19.245 12.0004 19.245C8.8704 19.245 6.21537 17.135 5.2654 14.29L1.27539 17.385C3.25539 21.31 7.3104 24.0001 12.0004 24.0001Z"
            fill="#34A853"
          />
        </svg>
        {buttonText} with Google
      </Button>

      <Button
        variant="outline"
        type="button"
        className="bg-background/80 flex items-center justify-center gap-2"
        onClick={handleFacebookAuth}
      >
        <svg
          viewBox="0 0 24 24"
          className="h-5 w-5 text-[#1877F2]"
          fill="currentColor"
          aria-hidden="true"
        >
          <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
        </svg>
        {buttonText} with Facebook
      </Button>

      <Button
        variant="outline"
        type="button"
        className="bg-background/80 flex items-center justify-center gap-2"
        onClick={handleAppleAuth}
      >
        <svg
          viewBox="0 0 24 24"
          className="h-5 w-5"
          fill="currentColor"
          aria-hidden="true"
        >
          <path d="M16.2127 0C14.6791 0.0787979 12.9 1.39876 11.9512 3.12751C11.0881 4.71174 10.2615 7.32799 11.3331 9.26028C12.0037 9.11557 12.7258 9.03676 13.4733 9.03676C14.9646 9.03676 16.3898 9.46922 17.4615 10.2362C18.2242 10.6111 18.8699 11.1211 19.3777 11.7946C19.2413 11.7083 19.0937 11.6332 18.946 11.5581C17.5598 10.9033 15.8316 10.5971 14.1778 10.6534C9.59544 10.7734 7.86189 13.9801 7.86189 13.9801C7.86189 13.9801 9.66456 15.0379 11.9137 16.381C14.163 17.724 15.8786 18.057 15.8786 18.057C15.8786 18.057 16.3114 16.6871 17.4864 15.0604C18.1222 14.1073 18.925 13.084 20.0931 12.3733C19.2554 8.92203 17.8447 6.01876 16.0919 4.33004C15.0327 3.23753 13.8521 2.69501 12.6591 2.69501C11.6625 2.69501 10.6908 3.04497 9.95212 3.73966C8.52097 5.04864 8.3097 7.38293 9.49313 8.42043C9.80656 8.70288 10.2122 8.86884 10.6659 8.90134C10.8636 8.90134 11.0613 8.88009 11.2466 8.82635C11.267 8.7726 11.2793 8.7301 11.3259 8.73011C11.4373 8.63756 11.548 8.54501 11.6593 8.45245C11.8691 8.30587 12.0531 8.21506 12.2121 8.18056C12.3711 8.14605 12.503 8.1233 12.6102 8.11206C12.7174 8.10081 12.8001 8.10081 12.8609 8.10081C12.9216 8.10081 12.9668 8.10081 12.9917 8.10081H13.0166C13.5366 8.10081 14.0324 8.08458 14.4904 7.74499C14.9483 7.4054 15.3082 6.74258 15.1907 5.68383C14.9385 5.71633 14.7531 5.75884 14.6418 5.80134C13.9402 6.01708 13.4449 6.31244 13.1328 6.66682C12.8207 7.02121 12.7067 7.38778 12.7067 7.75262C12.7067 8.11745 12.8207 8.44174 12.9978 8.70696C12.7865 8.67302 12.609 8.56859 12.4661 8.42114C12.3231 8.2737 12.205 8.05421 12.112 7.76268C12.0191 7.47115 11.9754 7.10833 11.9808 6.67423C11.9863 6.24012 12.0508 5.83429 12.1744 5.4568C12.3687 4.84567 12.5631 4.32161 12.7574 3.87464C12.5614 3.85922 12.3444 3.90191 12.1071 4.00271C11.8698 4.10351 11.6176 4.26157 11.3504 4.47689C11.0832 4.6922 10.8122 4.96772 10.5374 5.30345C10.2626 5.63917 10.0023 6.0368 9.75639 6.49634C9.51046 6.95587 9.30111 7.48209 9.12841 8.07499C9.03426 8.41072 8.96118 8.75605 8.90916 9.11098C8.85714 9.46592 8.82315 9.82781 8.80719 10.1967C8.79124 10.5656 8.78665 10.926 8.79346 11.2779C8.88286 11.2023 8.98349 11.132 9.08754 11.0693C10.6831 10.1619 12.2743 9.84785 14.1593 9.79053C16.0444 9.73321 17.9213 10.3205 19.02 11.095C19.1412 11.1743 19.2584 11.2577 19.3717 11.3492C21.0196 9.72008 22 7.79503 22 7.79503C22 7.79503 20.8859 4.92312 18.4837 2.52624C17.2468 1.29374 16.027 0.477729 14.6292 0.19499C14.4187 0.150012 14.204 0.112783 13.9866 0.0824976C14.5794 0.0300019 15.1476 -0.00374868 15.7158 0.0299973C15.8799 0.0299973 16.0439 0.0374978 16.2127 0Z M0.765569 18.3842C0.616455 18.2351 0.467342 18.0786 0.318228 17.9146C0.212152 17.8113 0.106076 17.6992 0 17.5796C0.116284 17.8112 0.242723 18.0316 0.379319 18.241C0.508529 18.2942 0.635126 18.3392 0.765569 18.3842Z M4.08859 20.4673C4.08859 20.4942 4.09325 20.5211 4.10723 20.5211C4.10723 20.5211 4.10723 20.4942 4.10723 20.4673C4.10723 20.4404 4.10257 20.4134 4.08859 20.4134C4.08859 20.4404 4.08859 20.4404 4.08859 20.4673Z" />
          <path d="M12.7514 9.2749C10.6376 9.32031 8.97174 10.2682 7.79308 11.0981C7.28532 11.448 6.8976 11.7752 6.65402 12.013C6.53223 12.132 6.43637 12.2279 6.37252 12.2968C6.34059 12.3312 6.31554 12.3589 6.29735 12.3783L6.27438 12.4023L6.26698 12.41C5.90694 12.7945 5.61166 13.2236 5.39114 13.6825C4.51718 15.4676 4.84024 17.9006 5.98489 19.927C6.78882 21.3451 7.92481 22.7332 9.61876 23.7503C11.2954 24.7649 13.1729 24.6039 14.3622 24.1247C15.5767 23.6263 16.7854 22.7704 18.1095 21.3969L18.165 21.341C20.069 19.3856 21.9206 16.055 23.7198 11.3489C21.8912 9.95572 19.613 9.12624 17.342 9.01074C15.7363 8.93316 14.1772 9.2432 12.7514 9.2749ZM20.1494 14.6729C20.2738 14.1368 20.2738 13.586 20.1494 13.0499C20.025 12.5138 19.7794 12.0125 19.433 11.5834C19.2231 11.3327 18.9785 11.1117 18.7028 10.9231C17.7734 10.2576 16.5379 9.91017 15.3593 9.96696C14.1805 10.0238 12.9867 10.4826 12.1272 11.2725C11.6988 11.6597 11.3459 12.1208 11.0873 12.6326C10.8287 13.1443 10.6685 13.6992 10.6144 14.27C10.5603 14.8408 10.6131 15.4161 10.77 15.9654C10.9269 16.5146 11.1854 17.0276 11.5322 17.4753C11.7999 17.8241 12.1214 18.1298 12.4848 18.381C12.8643 18.6245 13.2817 18.8023 13.7202 18.9079C14.1587 19.0136 14.6117 19.0454 15.0615 19.0021C15.5113 18.9588 15.9519 18.8407 16.3647 18.6529C16.7775 18.465 17.156 18.2104 17.4853 17.8998C17.6528 17.7303 17.8042 17.5463 17.9376 17.3513C18.0711 17.1563 18.174 16.9524 18.2471 16.7425C18.3202 16.5325 18.3637 16.3167 18.3769 16.0988C18.3901 15.8809 18.3728 15.6624 18.3255 15.4492C18.2782 15.2359 18.202 15.0304 18.0983 14.8371C17.9947 14.6437 17.8668 14.4665 17.7174 14.3075C17.7258 14.4639 17.7089 14.6206 17.6672 14.7714C17.8539 14.5908 17.9955 14.371 18.0813 14.1292C18.1671 13.8873 18.1949 13.6301 18.1628 13.3773C18.1308 13.1246 18.0399 12.8838 17.8977 12.6725C17.7555 12.4612 17.5668 12.2858 17.3464 12.1606C17.2708 12.1207 17.1915 12.0884 17.1092 12.0637C17.4714 12.193 17.7841 12.4182 18.01 12.7113C18.3974 13.2179 18.544 13.8631 18.4104 14.4879C18.2768 15.1127 17.8778 15.6514 17.3247 15.9701C16.7715 16.2887 16.1157 16.3585 15.5064 16.1624C14.897 15.9663 14.3856 15.5222 14.0928 14.9332L14.0928 14.9331C13.969 14.6801 13.892 14.4063 13.8662 14.1259C13.8404 13.8455 13.8662 13.5628 13.9421 13.2931C14.018 13.0233 14.1429 12.7714 14.3102 12.5501C14.4774 12.3288 14.684 12.1418 14.9196 11.9992C15.0271 11.9326 15.1406 11.8752 15.2583 11.8271C15.0685 11.8259 14.8794 11.8452 14.6937 11.8848C14.3165 11.965 13.9673 12.1435 13.6801 12.3996C13.3929 12.6557 13.1779 12.9805 13.0581 13.3429C12.9383 13.7052 12.918 14.0922 12.9993 14.4643C13.0805 14.8365 13.2606 15.1803 13.5214 15.464C13.7821 15.7478 14.1142 15.9619 14.4859 16.0851C14.8576 16.2083 15.2552 16.2362 15.6406 16.1663C16.026 16.0964 16.3864 15.9311 16.6866 15.6885C16.9868 15.4458 17.2172 15.1339 17.3551 14.7823C17.493 14.4307 17.5348 14.0508 17.4766 13.6811C17.4182 13.3114 17.262 12.9636 17.0232 12.6703C16.7844 12.3769 16.4709 12.1479 16.11 12.012L16.0911 12.0053C15.9037 11.9277 15.7072 11.8749 15.506 11.8483C15.9748 11.9035 16.425 12.0556 16.8319 12.2955C17.2388 12.5354 17.5935 12.8586 17.8735 13.2436C18.1536 13.6285 18.3535 14.0674 18.4599 14.5318C18.5662 14.9962 18.5764 15.4764 18.4898 15.9458C18.5874 15.7881 18.664 15.6191 18.718 15.443C18.7721 15.2669 18.8033 15.0856 18.8111 14.9025C18.819 14.7194 18.8036 14.5362 18.7651 14.3579C18.7266 14.1796 18.6656 14.0078 18.5834 13.848C18.4124 13.5273 18.1628 13.2512 17.8568 13.0451C17.9334 13.2852 17.9534 13.54 17.9151 13.789C17.8768 14.038 17.7813 14.2733 17.6363 14.4729C17.6945 14.4142 17.7451 14.3491 17.7868 14.2791C17.8886 14.1367 17.9535 13.9715 17.9758 13.7982C17.9982 13.6248 17.9773 13.4488 17.9152 13.2851C17.8531 13.1213 17.752 12.975 17.6208 12.8598C17.4896 12.7446 17.3333 12.6645 17.1651 12.6269C17.3333 12.6645 17.4916 12.7199 17.6338 12.7996C17.7761 12.8793 17.9025 12.9722 18.0082 13.0828C18.1139 13.1934 18.1985 13.3209 18.2574 13.4563C18.3164 13.5917 18.3493 13.7346 18.3541 13.8869C18.3572 14.0021 18.3437 14.119 18.3158 14.2317C18.8992 14.1367 19.4304 13.9211 19.689 13.5933C19.9117 13.3097 20.0747 12.9877 20.1729 12.6468C20.2711 12.3059 20.3031 11.9522 20.2681 11.601C20.2327 11.2519 20.1313 10.9095 19.9679 10.5883C19.8046 10.2671 19.5816 9.97167 19.307 9.71481C19.0323 9.45794 18.7094 9.24346 18.3538 9.07912C17.9981 8.91479 17.6145 8.8022 17.2176 8.74613C16.8206 8.69005 16.4152 8.69122 16.0183 8.74708C16.4192 8.72195 16.821 8.75238 17.2137 8.83687C17.6065 8.92136 17.986 9.05951 18.3414 9.24573C18.6968 9.43196 19.0246 9.66386 19.3148 9.93453C19.6049 10.2051 19.853 10.5161 20.0404 10.853C20.2277 11.1899 20.3518 11.5481 20.408 11.9145C20.4641 12.2809 20.4518 12.6617 20.3715 13.0229C20.3111 13.2839 20.2114 13.5334 20.0757 13.7617C19.94 13.9901 19.7697 14.194 19.5726 14.3645C19.542 14.3922 19.5105 14.4189 19.4783 14.4448C19.7676 14.5829 20.0002 14.6095 20.1494 14.6729Z" />
        </svg>
        {buttonText} with Apple
      </Button>
    </div>
  );
};

export default SocialAuth;
